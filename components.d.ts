/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AngleInput: typeof import('./src/components/common/AngleInput/AngleInput.vue')['default']
    BaseToolBox: typeof import('./src/components/toolbox/BaseToolBox.vue')['default']
    CloseToolBox: typeof import('./src/components/toolbox/CloseTool/CloseToolBox.vue')['default']
    CopyCloseToolBox: typeof import('./src/components/toolbox/CopyCloseTool/CopyCloseToolBox.vue')['default']
    DingToolBox: typeof import('./src/components/toolbox/DingTool/DingToolBox.vue')['default']
    DrawArrowSceneSetting: typeof import('./src/components/toolbox/ArrowTool/DrawArrowSceneSetting.vue')['default']
    DrawArrowToolBox: typeof import('./src/components/toolbox/ArrowTool/DrawArrowToolBox.vue')['default']
    DrawMosaicSceneSetting: typeof import('./src/components/toolbox/MosaicTool/DrawMosaicSceneSetting.vue')['default']
    DrawMosaicToolBox: typeof import('./src/components/toolbox/MosaicTool/DrawMosaicToolBox.vue')['default']
    DrawPencilSceneSetting: typeof import('./src/components/toolbox/PencilTool/DrawPencilSceneSetting.vue')['default']
    DrawPencilToolBox: typeof import('./src/components/toolbox/PencilTool/DrawPencilToolBox.vue')['default']
    DrawRectangleSceneSetting: typeof import('./src/components/toolbox/RectangleTool/DrawRectangleSceneSetting.vue')['default']
    DrawRectangleToolBox: typeof import('./src/components/toolbox/RectangleTool/DrawRectangleToolBox.vue')['default']
    DrawRoundSceneSetting: typeof import('./src/components/toolbox/RoundTool/DrawRoundSceneSetting.vue')['default']
    DrawRoundToolBox: typeof import('./src/components/toolbox/RoundTool/DrawRoundToolBox.vue')['default']
    EnvelopeContainer: typeof import('./src/components/common/container/EnvelopeContainer/EnvelopeContainer.vue')['default']
    ExpressionListToolBox: typeof import('./src/components/toolbox/ExpressionListTool/ExpressionListToolBox.vue')['default']
    FileTagSelect: typeof import('./src/components/FileTagSelect/FileTagSelect.vue')['default']
    FontSizeSelectPanel: typeof import('./src/components/panel/FontSizeSelectPanel.vue')['default']
    HighlightToolBox: typeof import('./src/components/toolbox/HighlightTool/HighlightToolBox.vue')['default']
    ImagePreview: typeof import('./src/components/common/imagePreview/ImagePreview.vue')['default']
    ImageUploadSceneSetting: typeof import('./src/components/toolbox/ImageUploadTool/ImageUploadSceneSetting.vue')['default']
    ImageUploadToolBox: typeof import('./src/components/toolbox/ImageUploadTool/ImageUploadToolBox.vue')['default']
    InputFormat: typeof import('./src/components/toolbox/SaveTool/InputFormat.vue')['default']
    LayoutSceneSetting: typeof import('./src/components/toolbox/LayoutTool/LayoutSceneSetting.vue')['default']
    LayoutToolBox: typeof import('./src/components/toolbox/LayoutTool/LayoutToolBox.vue')['default']
    LeaferColorPicker: typeof import('./src/components/common/LeaferColorPicker/LeaferColorPicker.vue')['default']
    LeftMenu: typeof import('./src/components/common/LeftMenu/LeftMenu.vue')['default']
    LongCaptureToolBar: typeof import('./src/components/LongCaptureToolBar/LongCaptureToolBar.vue')['default']
    LongCaptureToolBox: typeof import('./src/components/toolbox/LongCaptureTool/LongCaptureToolBox.vue')['default']
    OcrToolBox: typeof import('./src/components/toolbox/OcrTool/OcrToolBox.vue')['default']
    OcrToolSetting: typeof import('./src/components/toolbox/OcrTool/OcrToolSetting.vue')['default']
    QrCodeDisplay: typeof import('./src/components/toolbox/QrCodeTool/QrCodeDisplay.vue')['default']
    QrCodeToolBox: typeof import('./src/components/toolbox/QrCodeTool/QrCodeToolBox.vue')['default']
    RecallToolBox: typeof import('./src/components/toolbox/RecallTool/RecallToolBox.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RulerToolBox: typeof import('./src/components/toolbox/RulerTool/RulerToolBox.vue')['default']
    SaveToolBox: typeof import('./src/components/toolbox/SaveTool/SaveToolBox.vue')['default']
    SaveToolSceneSetting: typeof import('./src/components/toolbox/SaveTool/SaveToolSceneSetting.vue')['default']
    ScreenShotContextMenu: typeof import('./src/components/contextMenu/ScreenShotContextMenu.vue')['default']
    ScreenshotDragToolBox: typeof import('./src/components/toolbox/ScreenshotDrag/ScreenshotDragToolBox.vue')['default']
    SerialNumberSceneSetting: typeof import('./src/components/toolbox/SerialNumberTool/SerialNumberSceneSetting.vue')['default']
    SerialNumberToolBox: typeof import('./src/components/toolbox/SerialNumberTool/SerialNumberToolBox.vue')['default']
    SettingDivision: typeof import('./src/components/menu/SettingDivision/SettingDivision.vue')['default']
    SettingGroup: typeof import('./src/components/menu/SettingGroup/SettingGroup.vue')['default']
    SettingItem: typeof import('./src/components/menu/SettingItem/SettingItem.vue')['default']
    StrokeSelectPanel: typeof import('./src/components/panel/StrokeSelectPanel.vue')['default']
    StrokeWidthSelectPanel: typeof import('./src/components/panel/StrokeWidthSelectPanel.vue')['default']
    TAffix: typeof import('tdesign-vue-next')['Affix']
    TAlert: typeof import('tdesign-vue-next')['Alert']
    TBadge: typeof import('tdesign-vue-next')['Badge']
    TButton: typeof import('tdesign-vue-next')['Button']
    TButtonGroup: typeof import('tdesign-vue-next')['ButtonGroup']
    TCascader: typeof import('tdesign-vue-next')['Cascader']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TDateRangePicker: typeof import('tdesign-vue-next')['DateRangePicker']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TDropdownItem: typeof import('tdesign-vue-next')['DropdownItem']
    TDropdownMenu: typeof import('tdesign-vue-next')['DropdownMenu']
    TEmpty: typeof import('tdesign-vue-next')['Empty']
    TextSceneSetting: typeof import('./src/components/toolbox/TextTool/TextSceneSetting.vue')['default']
    TextToolBox: typeof import('./src/components/toolbox/TextTool/TextToolBox.vue')['default']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    ThemeImage: typeof import('./src/components/common/ThemeImage/ThemeImage.vue')['default']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TImageViewer: typeof import('tdesign-vue-next')['ImageViewer']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputGroup: typeof import('tdesign-vue-next')['InputGroup']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLink: typeof import('tdesign-vue-next')['Link']
    ToolboxBar: typeof import('./src/components/ToolboxBar/ToolboxBar.vue')['default']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TPopconfirm: typeof import('tdesign-vue-next')['Popconfirm']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadio: typeof import('tdesign-vue-next')['Radio']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TranslateSceneSetting: typeof import('./src/components/toolbox/TranslateTool/TranslateSceneSetting.vue')['default']
    TranslateSettingModal: typeof import('./src/components/toolbox/TranslateTool/templates/TranslateSettingModal.vue')['default']
    TranslateToolBox: typeof import('./src/components/toolbox/TranslateTool/TranslateToolBox.vue')['default']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TSlider: typeof import('tdesign-vue-next')['Slider']
    TSpace: typeof import('tdesign-vue-next')['Space']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTabPanel: typeof import('tdesign-vue-next')['TabPanel']
    TTabs: typeof import('tdesign-vue-next')['Tabs']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTooltip: typeof import('tdesign-vue-next')['Tooltip']
    UCheck: typeof import('./src/components/common/UCheck/UCheck.vue')['default']
    UColorPicker: typeof import('./src/components/common/UColorPicker/UColorPicker.vue')['default']
    WatermarkSceneSetting: typeof import('./src/components/toolbox/WatermarkTool/WatermarkSceneSetting.vue')['default']
    WatermarkToolBox: typeof import('./src/components/toolbox/WatermarkTool/WatermarkToolBox.vue')['default']
    WheelWaveContainer: typeof import('./src/components/common/container/WheelWaveContainer/WheelWaveContainer.vue')['default']
  }
}
