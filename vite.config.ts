import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from 'unocss/vite'

import AutoImport from 'unplugin-vue-components/vite'
import Components from 'unplugin-vue-components/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'
import { codeInspectorPlugin } from 'code-inspector-plugin';

import { join } from 'path'


// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => ['iconpark-icon'].includes(tag)
        }
      }
    }),
    vueJsx(),
    vueDevTools(),
    AutoImport({
      resolvers: [TDesignResolver({
        library: 'vue-next'
      })],
    }),
    Components({
      resolvers: [TDesignResolver({
        library: 'vue-next',
      })],
    }),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
    UnoCSS(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    sourcemap: true,
    emptyOutDir: true,
    outDir: join(__dirname, '/src-utools/dist'),
    rollupOptions: {
      input: {
        main: join(__dirname, '/index.html'),
        substrate: join(__dirname, '/screenshotContainer.html'),
        captureContainer: join(__dirname, '/captureContainer.html'),
        // suspendContainer: join(__dirname, '/suspendContainer.html'),
        // recordContainer: join(__dirname, '/recordContainer.html')
      }
    }
  }
})
