const {  clipboard  } = require('electron');
const { exec, spawn } = require('node:child_process');


function windowScreenCapture(executePath = '') {
  if (!executePath) {
    return;
  }
  return new Promise((resolve, reject) => {
    const beforePngs = clipboard.readImage().toDataURL();
    const child = spawn(`${executePath}/capture.exe`);
    child.on('close', (code) => {
      if (code === 8) {
        const pngs = clipboard.readImage().toDataURL();
        if (!pngs || pngs === beforePngs) {
          reject();
        }
        resolve(pngs);
      }
      reject();
    });
  });
}

function macScreenCapture(executePath = '') {
  const beforePngs = clipboard.readImage().toDataURL();
  return new Promise((resolve, reject) => {
    exec('screencapture -i -U -c', (error, stdout, stderr) => {
      if (error) {
        reject(error);
      }
      // 从剪切板上取到图片
      const pngs = clipboard.readImage().toDataURL();
      if (!pngs || pngs === beforePngs) {
        reject();
      }
      resolve(pngs);
    })
  })
}
module.exports = utools.isMacOS() ? macScreenCapture
  : utools.isWindows() ? windowScreenCapture : null;

