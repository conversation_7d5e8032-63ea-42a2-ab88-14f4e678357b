const { ipc<PERSON>enderer, nativeImage } = require('electron');
const { spawn, exec } = require( "node:child_process");
window.https = require('https');
window.http = require('http');
window.zip = require('cross-zip');
window.nodeCrypto = require('crypto');
window.spawn = spawn;
window.exec = exec;
window.ipcRenderer = ipcRenderer;
window.nativeImage = nativeImage;
// 这里使用申请权限方式, 不将所有 fs 方法都挂载到 window 上
window.fs = require('../lib/fs')(['writeFileSync', 'existsSync', 'readFileSync', 'renameSync',
  'mkdirSync', 'createWriteStream', 'unlinkSync', 'readdirSync', 'rmSync', 'statSync']);
window.nodeBuffer = require('node:buffer');
window.path = require('path');
window.environment = 'capture';
window.uiohook = require('uiohook-napi');
window.screenshot = require('screenshot-desktop');
window.sceneCode = 'default';
