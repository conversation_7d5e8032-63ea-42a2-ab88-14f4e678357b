const { ipc<PERSON>enderer, nativeImage, clipboard  } = require('electron');
const { spawn, exec, execSync } = require( "node:child_process");
window.spawn = spawn;
window.exec = exec;
window.execSync = execSync;
window.ipcRenderer = ipcRenderer;
window.nativeImage = nativeImage;
window.clipboard = clipboard;
window.screenshot = require('screenshot-desktop');
// 这里使用申请权限方式, 不将所有 fs 方法都挂载到 window 上
window.fs = require('./lib/fs')(['writeFileSync', 'readFile',
  'mkdirSync', 'existsSync',
  'createWriteStream', 'createReadStream',
  'rename', 'readdirSync', 'renameSync', 'readFileSync', 'copyFile',
  'unlinkSync', 'rmSync']);
window.nodeBuffer = require('node:buffer');
window.path = require('path');
window.localCapture = require('./lib/screenshot');
window.https = require('https');
window.http = require('http');
window.net = require('net');
if (process.arch !== 'ia32') {
  window.uiohook = require('uiohook-napi');
}
window.nodeHttp = require('phin');
window.zip = require('cross-zip');
window.readLine = require('readline');
window.nodeProcess = process;
window.nodeCrypto = require('crypto');

//
// const fs = require('fs');
// (async () => {
//   const ousrces = await utools.desktopCaptureSources({ types: ['window', 'screen'] })
//   console.log('ousrces', ousrces)
//   const source = ousrces[0];
//   window.source = source;
//   const size = source.thumbnail.getSize();
//   console.log('size', source.thumbnail.getSize())
//   const stream = await navigator.mediaDevices.getUserMedia({
//     audio: false,
//     video: {
//       mandatory: {
//         chromeMediaSource: 'desktop',
//         chromeMediaSourceId: source.id,
//       }
//     }
//   })
//   // 创建 MediaRecorder 实例
//   const mediaRecorder = new MediaRecorder(stream, {
//     mimeType: 'video/x-matroska;codecs=avc1' // 这里使用 webm 格式，因为大多数浏览器不支持直接录制 MP4
//   });
//   // 处理数据可用事件
//   mediaRecorder.ondataavailable = async (event) => {
//     let dataArrayBuffer = await event.data.arrayBuffer();
//     fs.writeFileSync('/Users/<USER>/Downloads/demo.mkv', Buffer.from(dataArrayBuffer));
//   };
//
//   // // 处理停止事件
//   // mediaRecorder.onstop = async () => {
//   //   console.log('处理停止事件')
//   //   console.log('chunks', chunks);
//   //   // const buffer = Buffer.concat(chunks.map(chunk => Buffer.from(chunk)));
//   //   // fs.writeFileSync('/Users/<USER>/Downloads/demo.webm', buffer);
//   //   // 如果需要，将 Blob 转换为 MP4 格式
//   //   // 你可以使用 ffmpeg.js 或其他库进行转换
//   // };
//
//   // 开始录制
//   mediaRecorder.start();
//   // 停止录制的示例（例如，5秒后停止）
//   setTimeout(() => {
//     mediaRecorder.stop();
//   }, 5 * 1000); // 录制 30 秒
//
//
//
//   console.log('ousrces', ousrces)
// })();
