console.log('preload loading....');
const { ipcRenderer, clipboard, nativeImage } = require('electron');
const { spawn, exec } = require( "node:child_process");
window.path = require('path');
window.https = require('https');
window.http = require('http');
window.net = require('net');
window.zip = require('cross-zip');
window.nodeCrypto = require('crypto');
// window.uiohook = require('uiohook-napi');
window.spawn = spawn;
window.exec = exec;
window.nativeImage = nativeImage;
window.clipboard = clipboard;
window.ipcRenderer = ipcRenderer;
// 这里使用申请权限方式, 不将所有 fs 方法都挂载到 window 上
window.fs = require('../lib/fs')(['writeFileSync', 'existsSync', 'readFileSync', 'renameSync',
  'mkdirSync', 'createWriteStream', 'unlinkSync', 'readdirSync', 'rmSync', 'statSync']);
window.nodeBuffer = require('node:buffer');
window.environment = 'screenshot';
window.sceneCode = 'default';
