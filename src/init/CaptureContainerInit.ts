import WinHelper from '@/core/utils/WinHelper'
import { initScreenshotApiProvider } from '@/core/sdk/screenshotApiProvider'

window.api = {};
window.tempData = {};
window.globalParams = {};

window.winHelper = new WinHelper({
  initCallback(initData) {
    window.winHelper.setData<number>('captureDisplayId', initData.captureDisplayId);

    initScreenshotApiProvider();
    const currentWindow = window.winHelper.getCurrentWindow();
    const display = utools.getDisplayNearestPoint(currentWindow.getBounds());
    const bounds = currentWindow.getBounds();
    currentWindow.setBounds({
      x: bounds.x,
      y: bounds.y,
      width: display.bounds.width,
      height: display.bounds.height
    })
    currentWindow.show();
    if (utools.isMacOS()) {
      // MacOS
      currentWindow.setSimpleFullScreen(true);
    } else {
      // windows/linux
      currentWindow.setFullScreen(true);
    }
    // 注册 API
    window.api.setOtherWindowsIds = (otherWindowsIds: number[]) => {
      window.winHelper.setData('otherWindowsIds', otherWindowsIds);
    }
  },
})
