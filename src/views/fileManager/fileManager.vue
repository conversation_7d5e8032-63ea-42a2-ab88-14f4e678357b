<script setup lang="ts">
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import FileManagerConfigModal from '@/views/fileManager/template/FileManagerConfigModal.vue'
import { computed, onMounted, provide, ref, toRefs, useTemplateRef } from 'vue'
import type { FileManagerConfigModalInstance } from '@/views/fileManager/template/type'
import { timeShortcuts } from '@/views/fileManager/data'
import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import sqls from '@/sql/sqls'
import { cloneDeep } from 'es-toolkit'
import DataHandlerUtils from '@/utils/DataHandlerUtils'
import ScreenshotDB, { type ScreenshotInfoData } from '@/utils/ScreenshotDB'
import DataUtils from '@/utils/DataUtils'
import { useSingleLoading } from '@/hooks/useLoading'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { type FileManagerProvide, fileManagerProvideCode } from '@/views/fileManager/index'
import { useRouter } from 'vue-router'
import ImagePreview from '@/views/fileManager/template/ImagePreview.vue'
import type { ImagePreviewInterface } from './template/ImagePreview'
import PictureFilePreview from './template/PictureFilePreview.vue'
import { NotifyPlugin } from 'tdesign-vue-next'
import { PageHeader, Result, BackTop } from '@xiaou66/u-web-ui';


const fileManagerConfigModalRef = ref<FileManagerConfigModalInstance>();

const imageDisplayMode = ref<'originalSrc' | 'editSrc'>('originalSrc');
const searchParams = ref({
  page: 0,
  timeQuery: [],
  pageSize: 20,
  orderDesc: true,
  tags: [],
});

const searchResult = ref<{
  total: number,
  tableData: {
    id: string,
    src?: string,
    originalSrc: string,
    editSrc: string,
    dataInfo: ScreenshotInfoData
  }[]
}>({
  tableData: [],
  total: 0
})
// 数据不一致
const dataInconsistency = ref(false);
function handleOrderChange() {
  searchParams.value.orderDesc = !searchParams.value.orderDesc;
  requestList();
}

async function requestList(page = 1, clear = false) {
  if (clear) {
    searchResult.value.tableData.length = 0;
  }
  searchParams.value.page = page;
  const requestParams: typeof searchParams.value = cloneDeep(searchParams.value);
  // region 处理时间 + 1 天
  requestParams.timeQuery = DataHandlerUtils.handleEndDate(requestParams.timeQuery);
  // endregion
  let where = "";
  if (requestParams.timeQuery
      && requestParams.timeQuery[0] && requestParams.timeQuery[1]) {
    where += `and fl.create_time >= '${requestParams.timeQuery[0]}' and fl.create_time < '${requestParams.timeQuery[1]}'`;
  }
  // file_library_tag
  let join = '';
  if (requestParams.tags.length > 0) {
    join += 'inner join file_library_tag flt on flt.file_id = fl.file_id';
    where += `and flt.tag_id in (${requestParams.tags.join(',')})`;
  }
  const sql = `select DISTINCT fl.file_id from file_library fl ${join}
                        where 1=1 ${where} order by create_time ${requestParams.orderDesc ? 'desc' : ''}`;
  const result = await window.db.page(sql, {
    page: requestParams.page,
    pageSize: requestParams.pageSize
  });
  searchResult.value.total = result.total;
  dataInconsistency.value = false;
  searchResult.value.tableData = result.data.map(item => {
    const originalPath = window.path.join(DataUtils.getDataScreenshotLibrary(), item.fileId, 'original.png');
    if (!window.fs.existsSync(originalPath)) {
      dataInconsistency.value = true;
      return null;
    }

    // 编辑图片
    let editFilePath = window.path.join(DataUtils.getDataScreenshotLibrary(), item.fileId, 'edit.png');
    if (!window.fs.existsSync(editFilePath)) {
      editFilePath = '';
    }

    // 数据信息
    const dataInfoPath = window.path.join(DataUtils.getDataScreenshotLibrary(), item.fileId, 'info.json');
    let dataInfo = null;
    if (window.fs.existsSync(dataInfoPath)) {
      dataInfo = JSON.parse(window.fs.readFileSync(dataInfoPath, 'utf-8'));
    }

    const data = {
      id: item.fileId,
      originalSrc: originalPath ? 'file://' + originalPath.replace(/\s/g, '%20') : '',
      editSrc: editFilePath ? 'file://' + editFilePath.replace(/\s/g, '%20') : '',
      dataInfo
    };
    // @ts-ignore
    data.src = data[imageDisplayMode.value] || data.originalSrc;

    if (clear) {
      exitBatchSelect();
    }
    return data;
  }).filter(item => item);
  console.log('searchParams', requestParams, searchResult.value)
}


provide<FileManagerProvide>(fileManagerProvideCode, {
  requestList
});
onMounted(() => {
  setTimeout(() => {
    console.log('loading', installSQL.value, localFileManagerEnable.value)
    if (installSQL.value && localFileManagerEnable.value) {
      requestList();
    }
  }, 100)
});

const installSQL = ref<boolean>(true);
async function refreshInstallSQLStatus(): Promise<boolean> {
  installSQL.value = await window.db.checkSqliteEnvironment();
  return installSQL.value;
}

const [handleDownloadSqlite, downloadSqliteStatus] = useSingleLoading( async (resourceCode: string) => {
  await EnvironmentUtils.downloadResource(DataUtils.getSqlitePath(), resourceCode);
  const status = await refreshInstallSQLStatus();
  if (status) {
    handleEnableStorage()
  }
});

onMounted(async () => {
  refreshInstallSQLStatus().then(() => {});
});
const windows = window.utools.isWindows();
const x64 = window.nodeProcess.arch.includes('64');
const { localFileManagerEnable } = toRefs(useFileManagerStore());
function handleEnableStorage() {
  window.db.initDatabase(sqls.initTable);
  setTimeout(() => {
    localFileManagerEnable.value = true;
    requestList();
  }, 500);
}


const contentInnerRef = ref<HTMLDivElement>(null);

const { fileTagLibraryList } = toRefs(useFileManagerStore());
const router = useRouter();
function handleRouterToScene() {
  router.replace({ name: 'sceneManager' })
}

const imagePreviewRef = useTemplateRef<ImagePreviewInterface>('imagePreviewRef');

const batchSelect = ref({
  status: false,
  list: []
});
const batchSelectedCount = computed(() => batchSelect.value.list.length);

function exitBatchSelect() {
  batchSelect.value = {
    status: false,
    list: []
  }
}

function handleItemClick(item: {id: string}, pos: number) {
  console.log(item, pos)
  if (batchSelect.value.status) {
    const index = batchSelect.value.list.findIndex(i => i === item.id);
    if (index >= 0) {
      batchSelect.value.list.splice(index, 1);
    } else {
      batchSelect.value.list.push(item.id);
    }
    console.log('batchSelect', batchSelect.value);
  } else {
    imagePreviewRef.value?.show(searchResult.value.tableData as any, pos)
  }
}


async function handleBatchDelete() {
  if (batchSelect.value.list) {
    const dataInfoList = searchResult.value.tableData.filter(item => batchSelect.value.list.includes(item.id))
      .map(item => item.dataInfo);
    for (const dataInfo of dataInfoList) {
      await ScreenshotDB.deleteFile(dataInfo);
    }
  }
  batchSelect.value.list.length = 0;
  NotifyPlugin.success({
    title: '提示',
    content: '批量删除完成',
    duration: 1000
  });
  requestList(searchParams.value.page, false).then(() => {})
}

</script>

<template>
  <ImagePreview ref="imagePreviewRef"></ImagePreview>
  <FileManagerConfigModal ref="fileManagerConfigModalRef" />
  <div id="fileManager" class="u-pos-rel u-main-content">
    <PageHeader :show-back="false" title="文件盒子" subtitle="这里可以查询到截图产生文件" >
      <template #extra>
        <div v-if="installSQL">
          <div v-if="!localFileManagerEnable">
          </div>
          <div v-else>
            <t-button theme="default"
                      variant="base"
                      @click="() => fileManagerConfigModalRef.show()">
              <template #icon>
                <t-icon class="i-u-config"></t-icon>
              </template>
              存储设置
            </t-button>
          </div>
        </div>
      </template>
    </PageHeader>
    <div v-if="installSQL"
         id="u-main-content-inner"
         class="u-main-content-inner"
         ref="contentInnerRef">
      <div v-if="localFileManagerEnable">
        <!--region 搜索栏-->
        <t-affix :container="() => contentInnerRef">
          <div class="search-header p-10">
            <div v-if="dataInconsistency"
                 class="u-fx u-mb10"
                 style="justify-content: flex-end">
              <div  class="u-fx u-fac u-gap10">
                <div class="u-fx"  style="color: #ff7d00">
                  <div class="i-u-tips w-4 h-4"></div>
                </div>
                <div class="u-font-size-smail">
                  数据出现不一致问题, 请到
                  <t-link theme="primary"
                          size="small"
                          @click="fileManagerConfigModalRef.show()">存储设置</t-link>
                  中刷新本地缓存
                </div>
              </div>
            </div>
            <div class="u-fx u-fac u-f-between">
              <div>
                <t-date-range-picker :shortcuts="timeShortcuts"
                                size="small"
                                v-model:value="searchParams.timeQuery"
                                @change="() => requestList()"
                                presets-placement="left"
                                style="width: 254px"
                                clearable
                                allowInput />
              </div>
              <div class="u-fx u-fac u-gap10">
                <div>
                  <t-select v-model:value="imageDisplayMode"
                            size="small"
                            style="width: 150px"
                            @change="requestList(1, true)">
                    <template #prefixIcon>
                      <span class="u-font-size-smail">优先显示</span>
                    </template>
                    <t-option value="originalSrc" label="原图" ></t-option>
                    <t-option value="editSrc" label="编辑图"></t-option>
                  </t-select>
                </div>
                <div class="u-fx" style="flex-direction: column;align-items: flex-end">
                  <t-button  @click="handleOrderChange"
                             theme="default"
                             size="small">按时间{{searchParams.orderDesc ? '降序' : '升序'}}</t-button>
                </div>
              </div>
            </div>
            <div class="u-fx u-fac u-gap5 u-mt10">
              <t-select v-model:model-value="searchParams.tags"
                        class="u-select"
                        placeholder="筛选标签"
                        style="width: 100%"
                        size="small"
                        multiple
                        clearable
                        filterable
                        @change="() => requestList()">
                <template #value-display="{ value }">
                  <t-tag v-for="(item, index) in value"
                         :key="index"
                         :color="item.color"
                         size="small">
                    {{ item.label }}
                  </t-tag>
                </template>
                <t-option v-for="fileTag in fileTagLibraryList"
                          :key="fileTag.id"
                          :value="fileTag.id"
                          :label="fileTag.label"
                          :color="fileTag.color">
                  <span class="u-font-size-smail" :style="{color: fileTag.color}">
                    {{ fileTag.label }}
                  </span>
                </t-option>
              </t-select>
            </div>
            <div class="u-fx u-fac u-gap10 u-mt10 u-f-between">
              <div class="u-font-size-smail">
                <div v-if="batchSelect.status">已选 <span :style="{color: batchSelectedCount > 0 ? '#1890ff' :''}">{{ batchSelectedCount }}</span>  项</div>
              </div>
              <div class="u-fx u-fac u-gap10">
                <div v-if="batchSelect.status && batchSelect.list.length"
                     class="u-fx u-fac u-gap10">
                  <t-button size="small"
                            @click="handleBatchDelete"
                            theme="danger">
                    <template #icon>
                      <t-icon class="i-u-delete"></t-icon>
                    </template>
                    删除
                  </t-button>
                </div>
                <t-button v-if="batchSelect.status"
                          size="small"
                          theme="default"
                          @click="exitBatchSelect">
                  <template #icon>
                    <t-icon class="i-u-upload w-4 h-4"></t-icon>
                  </template>
                  退出批量
                </t-button>
                <t-button v-else
                          size="small"
                          @click="batchSelect.status = true"
                          theme="default">
                  <template #icon>
                    <t-icon class="i-u-full-selection w-4 h-4"></t-icon>
                  </template>
                  批量操作
                </t-button>
              </div>
            </div>
          </div>
        </t-affix>
        <!--endregion-->
        <div class="u-mt10 body" v-if="searchResult.tableData.length">
          <div>
            <Waterfall :width="300"
                       :list="searchResult.tableData" row-key="id">
              <template #default="{ item, url, index: pos }">
                <PictureFilePreview
                  :pos="pos"
                  :edit="batchSelect.status"
                  :selected="batchSelect.list.includes(item.id)"
                  v-model:modal-value="searchResult.tableData[pos]"
                  v-model:search-params="searchParams"
                  @requestList="requestList"
                  @itemClick="() => handleItemClick(item, pos)" />
              </template>
            </Waterfall>
          </div>

          <!--region 分页区-->
          <div class="u-fx u-fac" style="justify-content: flex-end">
            <t-pagination v-model:current="searchParams.page"
                          v-model:pageSize="searchParams.pageSize"
                          :total="searchResult.total"
                          @currentChange="(current: any) => requestList(current, true)"
                          @pageSizeChange="requestList()"
                          size="small" />
          </div>
          <!--endregion-->
        </div>
        <div v-else class="body" style="margin-top: 50px">
          <t-empty title="未找到收集的文件">
            <template #description>
              <div>需要到
                <t-link theme="primary"
                        class="pl-2 pr-2"
                        @click="handleRouterToScene">场景管理</t-link>
                中配置哪些场景需要收集截图在 场景配置->截图设置 中
              </div>
            </template>
          </t-empty>
        </div>
        <!-- 回到顶部 -->

        <BackTop  size="small"
                  targetContainer=".u-main-content-inner"
                  class="back-top">
        </BackTop>
      </div>
      <div v-else>
        <Result>
          <template #title>存储截图功能</template>
          <template #extra>
            <t-space>
              <t-button theme="primary"
                        @click="handleEnableStorage">
                <template #icon>
                  <t-icon class="i-u-save-one"></t-icon>
                </template>
                开启
              </t-button>
            </t-space>
          </template>
        </Result>
      </div>
    </div>
    <div v-else class="u-main-content-inner">
      <Result>
        <template #default>
          <t-alert>
            <div>
              <div>如果上面安装方式无法进行安装</div>
              <div>1. 请联系开发者支持</div>
<!--              <div>2. 自行安装 sqlite-tools 并设置环境路径</div>-->
            </div>
          </t-alert>
        </template>
        <template #title>需要额外支持库来管理文件</template>
        <template #extra>
          <t-space>
            <t-button v-if="windows"
                      :loading="downloadSqliteStatus"
                      @click="handleDownloadSqlite(x64 ? 'db:sqlite:x64' : 'db:sqlite:x32')">
              <template #icon>
                <icon-download />
              </template>
              下载并启用文件存储
            </t-button>
          </t-space>
        </template>
      </Result>
    </div>
  </div>
</template>
<style scoped lang="less">
:deep(.t-input, .t-range-input) {
  background-color: transparent;
}
:deep(.t-range-input) {
  background-color: transparent;
}
// region 搜索栏样式
.search-header {
  padding: 0 10px;
  background: var(--main-ui-background);
  border-radius: 10px;
}

.t-affix {
  .search-header {
    padding-top: 10px;
    padding-bottom: 10px;
    box-shadow: rgba(14, 63, 126, 0.04) 0 0 0 0,
    rgba(42, 51, 69, 0.04) 0 1px 1px -0.5px,
    rgba(42, 51, 70, 0.04) 0 3px 3px -1.5px,
    rgba(42, 51, 70, 0.04) 0 6px 6px -3px,
    rgba(14, 63, 126, 0.04) 0 12px 12px -6px,
    rgba(14, 63, 126, 0.04) 0 24px 24px -12px;
  }
}
// endregion

:deep(.lazy__resource) {
  img {
    object-fit: contain !important;
  }
}
</style>
