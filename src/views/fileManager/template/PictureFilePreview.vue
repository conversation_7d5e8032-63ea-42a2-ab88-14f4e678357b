<script setup lang="ts">
import { LazyImg } from 'vue-waterfall-plugin-next'
import FileTagSelect from '@/components/FileTagSelect/FileTagSelect.vue'
import ScreenshotDB from '@/utils/ScreenshotDB'
import { ref, toRefs } from 'vue'
import { type FileTagLibrary, useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import { useListElementEditAction } from '@/hooks/ListActionHooks'
import { NotifyPlugin } from 'tdesign-vue-next'
import { screenshotByFile, sendScreenshotFileId } from '@/utils/utoolsCodeProcessor'
import DataUtils from '@/utils/DataUtils'
import { UCheck } from '@/components/common/UCheck'


defineProps<{
  pos: number;
  edit: boolean;
  selected: boolean;
}>();

const emits = defineEmits<{
  deleteFile: [id: string];
  itemClick: [];
  requestList: [page?: number];
}>();

const { tagIdToFileTagLibrary } = toRefs(useFileManagerStore());

const tagEditList = ref<string[]>([]);
const selectedCreateData = ref<FileTagLibrary[]>([]);

const item = defineModel<{ id: string;
  src?: string;
  originalSrc: string;
  editSrc: string;
  dataInfo: {
    fileId: string;
    updateTime: string;
    createTime: string;
    tags: number[];
    deviceCode: string;
  };
}>('modalValue');
const searchParams = defineModel<{tags: number[], page: number}>('searchParams');

// 标签被单击
function handleImageTagClick(tagId: number) {
  console.log('添加...')
  const index = searchParams.value.tags.indexOf(tagId);
  if (index === -1) {
    console.log('添加...')
    searchParams.value.tags.push(tagId);
    emits('requestList', 1);
  }
}

function handleImageTagEnterEdit(dataInfo: any) {
  switchTagEditAction(dataInfo.fileId);
  tagEditList.value = dataInfo.tags;
}

const [tagEditAction, switchTagEditAction] = useListElementEditAction();

function handleSwitchImageSource(item: any, pos: number) {
  let src = '';
  if (item.src === item.editSrc) {
    src = item.originalSrc;
  } else {
    src = item.editSrc;
  }
  console.log('pos', pos)
  item.src = src;
}


// 处理复制图片的功能
function handleCopyPicture(item: any) {
  utools.copyImage(item.src.replace('file://', '').replace(/%20/g, ' '));
  NotifyPlugin.success({
    title: '提示',
    content: '复制成功',
    duration: 1000
  });
}

// 钉住
function handleDingImage(item: any) {
  screenshotByFile(item.src.replace('file://', '').replace(/%20/g, ' '))
    .then(webContentsId => {
      console.log('webContentsId', webContentsId)
    });
}
const { addOrUpdateFileTag } = useFileManagerStore();
// 图片标签保存
function handleImageTagSave(id: string | number) {
  tagEditAction.value = '';
  console.log(selectedCreateData.value, tagEditList.value);
  const createTagIds = tagEditList.value.filter(tagId => Number(tagId) < 0)
    .map((tagId) => tagId.toString());
  const tagIds = selectedCreateData.value
    .filter(item => createTagIds.includes(item.id.toString()))
    .map(item => {
      delete item.id;
      return item;
    }).map(item => addOrUpdateFileTag(item))
    .map(({id}) => id);
  const saveIds = tagEditList.value.filter(tagId => Number(tagId) > 0);
  tagIds.push(...saveIds.map(item => Number(item)));
  const dataInfoPath = window.path.join(DataUtils.getDataScreenshotLibrary(), id.toString(), 'info.json');
  const dataInfo = JSON.parse(window.fs.readFileSync(dataInfoPath, 'utf-8'));
  dataInfo.tags = tagIds;
  window.fs.writeFileSync(dataInfoPath, JSON.stringify(dataInfo), 'utf-8');
  ScreenshotDB.syncLocalTagsDB(dataInfo);
  item.value.dataInfo.tags = tagIds;
  // 刷新列表
  emits('requestList', searchParams.value.page);
}

async function handleDeleteFile(item: any) {
  await ScreenshotDB.deleteFile(item.dataInfo);
  // 刷新列表
  emits('requestList', searchParams.value.page);
}

function handleEditImage(item: any) {
  sendScreenshotFileId(item.id);
}
</script>

<template>
  <div class="u-pos-rel image-container"
       @click.stop="() => emits('itemClick')">
    <LazyImg :url="item.src" />
    <!-- TODO 图片上操作 -->
    <div class="top">
      <UCheck v-if="edit"
              @click="() => emits('itemClick')"
              :modal-value="selected" />
    </div>
    <!--                  <div class="bottom">11</div>-->
  </div>
  <!--region 图片下面操作栏-->
  <div class="u-fx u-fac u-f-between file-info-bar"
       style="padding: 10px 0">
    <div class="u-fx u-fac u-gap5">
      <template v-if="item.dataInfo.tags">
        <template v-for="(tagId, index) in item.dataInfo.tags"
                  :key="index">
          <t-tag v-if="tagIdToFileTagLibrary[tagId]"
                 v-show="tagEditAction !== item.id"
                 class="u-pointer"
                 style="border-radius: 10px"
                 :color="tagIdToFileTagLibrary[tagId].color"
                 size="small"
                 @click="handleImageTagClick(tagId)">
            {{tagIdToFileTagLibrary[tagId].label}}
          </t-tag>
        </template>
        <t-tooltip v-if="tagEditAction !== item.id"
                   content="编辑标签">
          <t-button size="small"
                    shape="circle"
                    variant="text"
                    theme="primary"
                    @click="handleImageTagEnterEdit(item.dataInfo)">
            <div class="i-u-edit w-4 h-4"></div>
          </t-button>
        </t-tooltip>
        <div class="flex gap-1" v-else>
          <div style="min-width: 180px">
            <file-tag-select v-model:modal-value="tagEditList"
                             v-model:create-tags="selectedCreateData">
            </file-tag-select>
          </div>
          <t-button size="small"
                    variant="outline"
                    @click="handleImageTagSave(item.id)">
            <div class="i-u-save-one w-4 h-4"></div>
          </t-button>
        </div>
      </template>
    </div>
    <!--                  {{ dayjs(item.dataInfo.createTime).format('YY-MM-DD HH:mm') }}-->
    <div v-if="tagEditAction !== item.id"
         class="u-fx u-fac u-gap5">
      <t-popconfirm content="确认删除这张图片吗?"
                    @confirm="() => handleDeleteFile(item, pos)">
        <t-button shape="circle"
                  theme="danger"
                  size="small">
          <div class="i-u-delete w-3 h-3"></div>
        </t-button>
      </t-popconfirm>
      <!--  文件管理:图片类型切换 -->
      <div v-if="item.editSrc">
        <t-tooltip mini>
          <template #content>
            <span class="u-font-size-smail">切换到「{{item.editSrc === item.src ? '原图' : '编辑图'}}」</span>
          </template>
          <t-button shape="circle"
                    size="small"
                    theme="default"
                    @click="handleSwitchImageSource(item, pos)">
            <div class="i-u-swap w-4 h-4"></div>
          </t-button>
        </t-tooltip>
      </div>
      <!--  文件管理:复制  -->
      <t-button shape="circle"
                size="small"
                theme="default"
                @click="handleCopyPicture(item)">
        <div class="i-u-copy w-4 h-4"></div>
      </t-button>
      <!--  文件管理:钉住  -->
      <t-dropdown trigger="hover"
                  size="mini"
                  shape="circle"
                  popup-container="#fileManager">
        <t-button shape="circle"
                  size="small"
                  theme="default">
          <div class="i-u-pin w-4 h-4"></div>
        </t-button>
        <t-dropdown-menu size="small">
          <t-dropdown-item  class="u-font-size-smail" @click="() => handleDingImage(item)">
            原图
          </t-dropdown-item>
          <t-dropdown-item  class="u-font-size-smail" @click="() => handleEditImage(item)">
            编辑图
          </t-dropdown-item>
        </t-dropdown-menu>
      </t-dropdown>
    </div>
  </div>
  <!--endregion-->
</template>

<style scoped lang="less">
// region 图片容器
.image-container {
  .top, .bottom {
    padding: 6px;
    position: absolute;
    width: 100%;
    color: #ffffff;
  }
  .top {
    top: 0;
  }
  .bottom {
    bottom: 0;
    opacity: 0;
    pointer-events: none;
    transition: opacity 200ms linear;
  }
  &:hover {
    .bottom {
      opacity: 1;
      pointer-events: auto;
    }
  }
}
// endregion
</style>
