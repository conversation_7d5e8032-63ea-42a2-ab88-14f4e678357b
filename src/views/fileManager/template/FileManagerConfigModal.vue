<script setup lang="tsx">
import { computed, inject, ref, toRefs } from 'vue'
import type { FileManagerConfigModalInstance } from '@/views/fileManager/template/type'
import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import ScreenshotDB from '@/utils/ScreenshotDB'
import { useSingleLoading } from '@/hooks/useLoading'
import DataUtils from '@/utils/DataUtils'
import { useRouter } from 'vue-router'
import { type FileManagerProvide, fileManagerProvideCode } from '@/views/fileManager'
import { DialogPlugin } from 'tdesign-vue-next'
import FileUtils from '@/utils/FileUtils'

const model = ref({});
const { fileManagerConfig, localFileManagerEnable } = toRefs(useFileManagerStore());

const visible = ref(false);

const screenshotLibraryPath =  computed(() => DataUtils.getDataScreenshotLibrary());

function show() {
  visible.value = true;
}
function hide() {
  visible.value = false;
}
const fileManagerInject = inject<FileManagerProvide>(fileManagerProvideCode);
const [handleRefreshLocalDataDB, refreshLocalDataDB] = useSingleLoading(async () => {
  await ScreenshotDB.localSyncDB();
  fileManagerInject.requestList(1, true).then(() => {});
});

function handleCloseLocalDataDB() {
  localFileManagerEnable.value = false;
  hide();
}

function handleOpenScreenshotLibraryPath() {
  window.utools.shellOpenPath(screenshotLibraryPath.value);
}
const router = useRouter();

function handleToDataManagerSetting() {
  router.replace({ name: 'dataSetting' })
}
function handleDeleteFileBoxData() {
  DialogPlugin.confirm({
    header: '二次确认',
    body: () => (
      <div>
        <div>确认要删除文件盒子所有的文件数据吗?</div>
        <div style={{ color: '#f18' }}>删除后将无法还原数据</div>
      </div>
    ),
    onConfirm: async () => {
      window.fs.rmSync(screenshotLibraryPath.value, {
        force: true,
        recursive: true,
      });
      FileUtils.createDirIfAbsent(DataUtils.getDataScreenshotLibrary());
      await ScreenshotDB.clearAllData();
    }
  });
}
defineExpose<FileManagerConfigModalInstance>({
  show
});
</script>

<template>
  <t-dialog v-model:visible="visible"
           title="存储配置"
           :width="500"
           modal-class="file-manager-modal"
           simple>
    <t-form :model="model"
            labelAlign="left"
            :labelWidth="80">
      <t-form-item label="存储位置">
        <template #help>
          <span> 存储位置将在插件数据目录下, 如果需要变动请修改插件数据目录
            <t-link size="small"
                    theme="primary"
                    class="u-font-size-smail"
                    @click="handleToDataManagerSetting">
              点击修改
            </t-link>
          </span>
        </template>
        <t-input-group class="w-full">
          <t-input v-model:value="screenshotLibraryPath"
                   size="small"
                   readonly>
          </t-input>
          <t-button theme="default"
                    size="small"
                    @click="handleOpenScreenshotLibraryPath">
            <template #icon>
              <div class="i-u-folder-open w-5 h-5"></div>
            </template>
            打开
          </t-button>
        </t-input-group>
        <!-- TODO 这里要新增一个跳转到全局路径配置的地方  -->
      </t-form-item>
      <t-form-item label="数据同步">
        <t-radio-group v-model:model-value="fileManagerConfig.sync">
          <t-radio value="no-sync">不同步</t-radio>
        </t-radio-group>
      </t-form-item>
    </t-form>
    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <t-link style="font-size: 12px"
                  theme="danger"
                  type="text"
                  size="small" @click="handleDeleteFileBoxData">
            <span style="padding-right: 6px;">
              <icon-delete />
            </span>
              清空数据
          </t-link>
        </div>
        <div class="flex gap-1">
          <t-button theme="danger"
                    @click="handleCloseLocalDataDB">
            <template #icon>
              <t-icon class="i-u-close"></t-icon>
            </template>
            关闭存储
          </t-button>
         <t-tooltip content="当文件信息检索和文件信息不一致使用">
           <t-button theme="default"
                     :loading="refreshLocalDataDB"
                     @click="handleRefreshLocalDataDB">
             <template #icon>
               <t-icon class="i-u-refresh"></t-icon>
             </template>
             刷新本地存储
           </t-button>
         </t-tooltip>
       </div>
      </div>
    </template>
  </t-dialog>
</template>
