<script setup lang="tsx">
import { computed, nextTick, onMounted, ref, useTemplateRef, render, h } from 'vue'
import type { ImageItemType, ImagePreviewInterface } from './ImagePreview'
import { screenshotByFile, sendScreenshotFileId } from '@/utils/utoolsCodeProcessor'
import { NotifyPlugin, Dropdown } from 'tdesign-vue-next'

const visible = ref<boolean>(false)
const current = ref(0)
const fileItemList = ref<ImageItemType[]>([])


const dingElement = () => (
    <Dropdown trigger="hover"
              options={[
                { content: '原图', value: 'original', onClick: handleDingImage },
                { content: '编辑', value: 'edit', onClick: handleEditImage }
              ]}>
      <div class="t-image-viewer__modal-icon">
        <div class="w-4 h-4 font-bold i-u-pin"></div>
      </div>
    </Dropdown>
)
const copyElement = () => (
  <div class="t-image-viewer__modal-icon"
       onClick={ handleCopyImage }>
    <div class="w-4 h-4 font-bold i-u-copy"></div>
  </div>
)
function show(itemList: ImageItemType[], index: number) {
  fileItemList.value = itemList
  current.value = index
  visible.value = true;
  nextTick(() => {
    const element = document.querySelector('.t-image-viewer__utils-content')
    console.log('element', element);
    const dingTool = document.createElement('div');
    render(h(dingElement()), dingTool)
    element.appendChild(dingTool);
    const copyTool = document.createElement('div');
    render(h(copyElement()), copyTool)
    element.appendChild(copyTool);
  })
}
const imageList = computed(() => fileItemList.value.map(item => item.src));

defineExpose<ImagePreviewInterface>({
  show
})

const currentUrl = computed(() => {
  if (fileItemList.value.length === 0) {
    return ''
  }
  return fileItemList.value[current.value].src
})

// 钉住
function handleDingImage() {
  const item = fileItemList.value[current.value];
  screenshotByFile(item.originalSrc.replace('file://', '').replace(/%20/g, ' '))
    .then(webContentsId => {
      console.log('webContentsId', webContentsId)
    });
}

// 编辑图片
function handleEditImage() {
  const item = fileItemList.value[current.value];
  sendScreenshotFileId(item.id);
}

// 复制图片
function handleCopyImage() {
  let url = currentUrl.value;
  if (url.startsWith('file://')) {
    url =  url.replace('file://', '').replace(/%20/g, ' ')
  }
  utools.copyImage(url);
  NotifyPlugin.success({
    title: '操作提示',
    content: '复制成功',
    closeBtn: true,
    duration: 700
  });
}
</script>

<template>
  <t-image-viewer v-model:visible="visible"
                  v-model:index="current"
                  :images="imageList"
                  :imageScale="{ step: 0.1 }"
                  draggable>
  </t-image-viewer>
<!--  <a-image-preview-->
<!--    :src="currentUrl"-->
<!--    v-model:visible="visible"-->
<!--    :default-scale="0.8"-->
<!--  >-->
<!--    <template #actions>-->
<!--      <a-dropdown trigger="hover"-->
<!--                  size="mini"-->
<!--                  shape="circle"-->
<!--                  popup-container="#fileManager">-->
<!--        <div class="arco-image-preview-toolbar-action">-->
<!--              <span class="arco-image-preview-toolbar-action-content">-->
<!--                <iconpark-icon name="pin"></iconpark-icon>-->
<!--              </span>-->
<!--        </div>-->
<!--        <template #content>-->
<!--          <a-doption class="u-font-size-smail"-->
<!--                     @click="handleDingImage(fileItemList[current])">-->
<!--            原图-->
<!--          </a-doption>-->
<!--          <a-doption class="u-font-size-smail"-->
<!--                     @click="handleEditImage(fileItemList[current])">-->
<!--            编辑图-->
<!--          </a-doption>-->
<!--        </template>-->
<!--      </a-dropdown>-->
<!--      <a-image-preview-action name="复制" @click="handleCopyImage" >-->
<!--        <iconpark-icon name="copy"></iconpark-icon>-->
<!--      </a-image-preview-action>-->
<!--    </template>-->
<!--  </a-image-preview>-->
<!--  <div ref="imagePreviewRef" class="u-pos-abs image-preview" v-if="visible" @click.self="hide">-->
<!--    <div class="controls">-->
<!--      <div class="prev" @click="prev" v-show="current > 0">-->
<!--        <icon-left />-->
<!--      </div>-->
<!--      <div class="next" @click="next" v-show="current < fileItemList.length - 1">-->
<!--        <icon-right />-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
</template>
<style>
.arco-image-preview-img-container .arco-image-preview-img {
  object-fit: contain;
}
</style>
<style scoped lang="less">
.image-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
}
.controls {
  width: 100vw;
  height: 100vh;
  position: absolute;
  z-index: 9999;
  pointer-events: none;
  .prev,.next {
    pointer-events: auto;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #ffffff;
  }
  .prev {
    left: 30px;
  }
  .next {
    right: 30px;
  }
}
</style>
<style>
.arco-image-preview-wrapper, .arco-image-preview {
  border: none !important;
}
:focus-visible {
  outline: none !important;
}
</style>
