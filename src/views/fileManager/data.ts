
import dayjs from "dayjs";

export const timeShortcuts:  any[] = [
    {
        label: '今天',
        value: () => [dayjs(), dayjs()] as any[]
    },
    {
        label: '昨天',
        value: () => [dayjs().subtract(1, 'day'), dayjs()] as any[]
    },
    {
        label: '近 7 天',
        value: () => [dayjs().subtract(7, 'day'), dayjs()] as any[]
    },
    {
        label: '上周',
        value: () => [
            dayjs().subtract(1, 'week').startOf('week').add(1, 'day'),
            dayjs().subtract(1, 'week').endOf('week').add(1, 'day')
        ] as any[]
    },
    {
        label: '近 30 天',
        value: () => [dayjs().subtract(30, 'day'), dayjs()] as any[]
    },
    {
        label: '上月',
        value: () => [
            dayjs().subtract(1, 'month').startOf('month'),
            dayjs().subtract(1, 'month').endOf('month')
        ] as any[]
    },
]
