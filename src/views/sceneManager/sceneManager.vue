<script setup lang="ts">
import { getToolConfigList } from '@/components/toolbox/ToolBoxHelper'
import { computed, defineAsyncComponent, onMounted, ref, useTemplateRef } from 'vue'
import { type ISceneConfig, useSceneStore } from '@/stores/SceneStore/SceneStore'
import AddSceneModal from '@/views/sceneManager/template/AddSceneModal.vue'
import type { AddSceneModalInstance, EditSceneModalInstance } from '@/views/sceneManager/template/type'
import FileTagSelect from '@/components/FileTagSelect/FileTagSelect.vue'
import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import { NotifyPlugin } from 'tdesign-vue-next'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import EditSceneModal from './template/EditSceneModal.vue'
import MediaUtils from '@/script/media/MediaUtils'
import { PageHeader, SwitchPlusEnable } from '@xiaou66/u-web-ui';

const toolConfigList = getToolConfigList().filter((item) => !item.hideScene)
const sceneStore = useSceneStore()
const openSceneConfig = ref('default')
const toolConfigActiveKey = ref('screenshotDrag');
const route = useRoute()
const sceneRef = useTemplateRef<HTMLDivElement[]>('sceneRef');

function setSceneCodeAndTool(sceneCode: string, toolCode: string) {
  openSceneConfig.value = sceneCode;
  toolConfigActiveKey.value = toolCode;
  setTimeout(() => {
    const currentSceneRef = sceneRef.value.find(item => item.getAttribute('data-scene-code') === sceneCode);
    if (currentSceneRef) {
      const rect = currentSceneRef.getBoundingClientRect();
      const scrollContainer = currentSceneRef.closest('.u-main-content-inner'); // 替换为实际的滚动容器类名

      if (scrollContainer) {
        const containerRect = scrollContainer.getBoundingClientRect();
        const scrollTop = scrollContainer.scrollTop;

        // 计算需要滚动的距离
        const targetScrollTop = scrollTop + rect.top - containerRect.top - 10; // 10px 的偏移量

        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });
      }
    }
  })
}
onMounted(() => {
  const { sceneCode = 'default',  toolCode = 'screenshotDrag'} = route.query as { sceneCode: string, toolCode: string };
  setSceneCodeAndTool(sceneCode, toolCode);
});

onBeforeRouteUpdate((to, from, next) => {
  const { sceneCode = 'default',  toolCode = 'screenshotDrag'} = to.query as { sceneCode: string, toolCode: string };
  setSceneCodeAndTool(sceneCode, toolCode);
  next();
})

function handleExpandConfig(sceneCode: string) {
  openSceneConfig.value = ''
  openSceneConfig.value = sceneCode
}

// 切换是否禁用工具列表
function handleDisableCodeSwitchover(scene: ISceneConfig, toolCode: string) {
  const index = scene.disableCodeTools.findIndex((code) => code === toolCode)
  if (index !== -1) {
    scene.disableCodeTools.splice(index, 1)
  } else {
    scene.disableCodeTools.push(toolCode)
  }
}

// 切换工具配置存储保存模式
function handleDisableAutoSaveSwitchover(scene: ISceneConfig, toolCode: string) {
  const index = scene.disableAutoSaveTools.findIndex((code) => code === toolCode)
  if (index !== -1) {
    scene.disableAutoSaveTools.splice(index, 1)
  } else {
    scene.disableAutoSaveTools.push(toolCode)
  }
}

const sceneConfigTab = ref<'toolConfig' | 'screenshotConfig'>('toolConfig')

const sceneFormModel = ref({})

const addSceneModalRef = ref<AddSceneModalInstance>()
const editSceneModalRef = ref<EditSceneModalInstance>()
function handleOpenSceneEdit(scene: ISceneConfig) {
  if (scene.system) {
    NotifyPlugin.warning({
      title: '提示',
      content: '系统内置场景无法修改',
      duration: 1000
    });
    return
  }
  editSceneModalRef.value!.show(scene)
}
const plugInCodeError = ref([])

function handleSceneEnableSwitchover(scene: ISceneConfig) {
  const commandPrefix = 'screenshot.capture'
  if (!scene.enable) {
    // 启用
    if (scene.plugInCode === '') {
      plugInCodeError.value.push(scene.sceneCode)
      setTimeout(() => {
        const index = plugInCodeError.value.findIndex((sceneCode) => sceneCode === scene.sceneCode)
        if (index >= 0) {
          plugInCodeError.value.splice(index, 1)
        }
      }, 3000)
      return
    }
    window.utools.setFeature({
      code: `${commandPrefix}?sceneCode=${scene.sceneCode}`,
      explain: `${scene.title}-${scene.desc}`,
      cmds: [scene.plugInCode]
    })
  } else {
    // 禁用
    window.utools.removeFeature(`${commandPrefix}?sceneCode=${scene.sceneCode}`)
  }
  scene.enable = !scene.enable
}

function handleDeleteScene(sceneCode: string) {
  sceneStore.deleteScene(sceneCode);
}
const fileManagerStore = useFileManagerStore();
const router = useRouter();
function handleSceneFileBoxEnable(val: boolean, scene: ISceneConfig) {
  if (!val) {
    scene.fileBoxEnable = val;
    return;
  }
  if (!fileManagerStore.localFileManagerEnable) {
    NotifyPlugin.info({
      title: '提醒',
      content: '需要先在文件盒子中心开启存储, 正在自动跳转',
    });
    router.replace({ name: 'fileManager' });
    return;
  }
  scene.fileBoxEnable = true;
}
async function handleSuspendMediaChance() {
  await MediaUtils.install();
}
const purchasedUser = ref(utools.isPurchasedUser());
const suspendMediaVisible = computed(() => utools.isWindows() || utools.isMacOS());
</script>

<template>
  <EditSceneModal ref="editSceneModalRef" />
  <AddSceneModal ref="addSceneModalRef"
                 @success="handleExpandConfig" />

  <div class="u-main-content">
    <PageHeader title="场景管理"
                subtitle="这里针对不同场景自动加载配置不同工具的配置">
      <template #extra>
        <t-button
          v-if="purchasedUser"
          theme="default"
          @click="() => addSceneModalRef.show()"
        >
          <template #icon>
            <t-icon class="i-u-plus"></t-icon>
          </template>
          新增场景
        </t-button>
        <t-button
          v-else
          theme="default"
          @click="() => router.replace({ name: 'chargeStore' })"
        >
          <template #icon>
            <t-icon class="i-u-plus"></t-icon>
          </template>
          新增场景(购买)
        </t-button>
      </template>
    </PageHeader>

    <div class="u-main-content-inner">
      <div>
        <div v-for="scene in sceneStore.sceneConfigList" :key="scene.sceneCode" class="scene-item">
          <div ref="sceneRef"
               :data-scene-code="scene.sceneCode"
               class="u-fx u-fac scene-header"
               @click.self="handleExpandConfig(scene.sceneCode)">
            <t-tooltip mini content="单击编辑">
              <div class="title u-pointer"
                   @click.stop="handleOpenSceneEdit(scene)">
                <span class="u-font-strong main-title">
                  {{ scene.title }}
                </span>
                <span class="u-font-size-smail sub-title">
                  {{ scene.desc }}
                </span>
              </div>
            </t-tooltip>
            <div class="u-fx u-fac u-gap10">
              <div v-if="!scene.system" class="u-fx u-fac u-gap5">
                <div>
                  <t-tag :color="scene.enable ? 'green' : 'gray'"
                         size="small"
                         variant="outline">
                    {{ scene.enable ? '启用' : '禁用' }}
                  </t-tag>
                </div>
                <div>
                  <SwitchPlusEnable size="mini"
                                    :value="scene.enable"
                                    @change="() => handleSceneEnableSwitchover(scene)" />
                </div>
              </div>
              <t-radio-group v-model:model-value="scene.saveMode"
                             size="small"
                             variant="default-filled">
                <t-tooltip>
                  <template #content> 使用这个场景截图工具配置做任何的修改均会保存 </template>
                  <t-radio-button value="auto">自动</t-radio-button>
                </t-tooltip>
                <t-tooltip>
                  <template #content> 使用这个场景截图工具配置做任何的修改均不会保存 </template>
                  <t-radio-button value="manual">手动</t-radio-button>
                </t-tooltip>
              </t-radio-group>
              <!--              <a-button size="mini" shape="round" disabled>默认</a-button>-->
            </div>
          </div>
          <div class="u-fx u-fac utools-panel">
            <div class="u-fx u-fac u-gap10">
              <div>utools 指令</div>
              <div>
                <span v-if="scene.system">
                  <div class="u-font-size-smail u-fx u-gap10">
                    <t-tag >sc</t-tag>
                    <t-tag >dsc</t-tag>
                    <t-tag >截图编辑</t-tag>
                    <t-tag >悬浮</t-tag>
                  </div>
                </span>
                <t-tooltip
                  v-else
                  :visible="plugInCodeError.includes(scene.sceneCode)"
                  content="请先输入指令后再开启"
                >
                  <t-input
                    size="small"
                    v-model:model-value="scene.plugInCode"
                    style="border-radius: 10px"
                    :class="plugInCodeError.includes(scene.sceneCode) ? 'u-input-error' : ''"
                    :readonly="scene.system || scene.enable"
                  />
                </t-tooltip>
              </div>
            </div>
            <div class="u-fx u-fac u-gap10">
              <t-popconfirm
                v-if="!scene.system && !scene.enable"
                content="确认要删除这个场景吗？"
                @confirm="() => handleDeleteScene(scene.sceneCode)"
              >
                <t-button size="small" theme="danger" variant="text">
                  <template #icon>
                    <div class="u-fx u-fac">
                      <t-icon class="i-u-delete"></t-icon>
                    </div>
                  </template>
                  <div class="pl-1">删除</div>
                </t-button>
              </t-popconfirm>
              <t-button
                v-if="openSceneConfig !== scene.sceneCode"
                theme="default"
                size="small"
                @click="() => handleExpandConfig(scene.sceneCode)"
              >
                <template #icon>
                  <div class="u-fx u-fac">
                    <t-icon class="i-u-expand-down-one"></t-icon>
                  </div>
                </template>
                <div class="pl-1">展开配置</div>
              </t-button>
              <t-button
                v-else
                theme="default"
                size="small"
                @click="() => (openSceneConfig = '')"
              >
                <template #icon>
                  <div class="u-fx u-fac">
                    <t-icon class="i-u-fold-up-one"></t-icon>
                  </div>
                </template>
                <div class="pl-1">收起配置</div>
              </t-button>
            </div>
          </div>
          <div v-if="openSceneConfig === scene.sceneCode"
               class="config">
            <div class="config-header">
              <div class="u-fx u-fac tool-config-title">
                <div class="u-fx u-fac u-gap10">
                  <div
                    class="u-font-strong u-pointer scene-config-tab"
                    :class="sceneConfigTab === 'toolConfig' ? 'active' : ''"
                    @click="() => (sceneConfigTab = 'toolConfig')"
                  >
                    工具配置
                  </div>
                  <div
                    class="u-font-strong u-pointer scene-config-tab"
                    :class="sceneConfigTab === 'screenshotConfig' ? 'active' : ''"
                    @click="() => (sceneConfigTab = 'screenshotConfig')"
                  >
                    截图配置
                  </div>
                </div>
                <div class="u-fx u-fac u-gap5 u-tips">
                  <div>场景配置均是操作即保存, 无需保存按钮</div>
                  <div class="i-u-tips w-4 h-4"></div>
                </div>
              </div>
            </div>
            <div v-show="sceneConfigTab === 'toolConfig'" class="tool-config">
              <t-tabs v-model:value="toolConfigActiveKey"
                      class="u-web-tabs-transparency"
                      destroy-on-hide>
                <t-tab-panel v-for="toolConfig in toolConfigList"
                             :key="toolConfig.code"
                             :value="toolConfig.code"
                             lazy>
                  <template #label>
                    <div class="u-fx u-fac u-gap5">
                      <div class="u-fx u-fac">
                        <div :class="toolConfig.toolIcon"></div>
                      </div>
                      <div>{{ toolConfig.title }}工具</div>
                    </div>
                  </template>
                  <div class="pl-1 pt-1 tool-common-config">
                    <div>
                      <div class="u-fx u-fac u-gap10">
                        <div class="u-font-size-smail">
                          工具{{
                            !scene.disableCodeTools.includes(toolConfig.code) ? '启用' : '禁用'
                          }}
                        </div>
                        <SwitchPlusEnable :value="!scene.disableCodeTools.includes(toolConfig.code)"
                                          size="mini"
                                          @change="() => handleDisableCodeSwitchover(scene, toolConfig.code)" />
                      </div>
                    </div>
                    <div class="u-fx u-fac u-gap10">
                      <t-radio-group v-if="toolConfig.sceneSetting"
                                     variant="default-filled"
                                     size="small"
                                     :model-value="scene.disableAutoSaveTools.includes(toolConfig.code) ? 'manual' : 'global'"
                                     @change="() => handleDisableAutoSaveSwitchover(scene, toolConfig.code)">
                        <t-tooltip content="根据全局的配置">
                          <t-radio-button value="global">全局</t-radio-button>
                        </t-tooltip>
                        <t-tooltip>
                          <template #content>
                            全局配置自动后生效 <br />
                            想当前工具不要保存请设置为手动
                          </template>
                          <t-radio-button value="manual">手动</t-radio-button>
                        </t-tooltip>
                      </t-radio-group>
                    </div>
                  </div>
                  <div class="tool-config-content">
                    <component
                      v-if="toolConfig.sceneSetting"
                      :is="defineAsyncComponent(toolConfig.sceneSetting)"
                      :sceneCode="scene.sceneCode"
                    >
                    </component>
                    <t-empty v-else title="当前工具暂无配置项" />
                  </div>
                </t-tab-panel>
              </t-tabs>
            </div>
            <div v-show="sceneConfigTab === 'screenshotConfig'">
              <div style="width: 66%">
                <t-form :model="sceneFormModel"
                        :label-width="120">
                  <t-divider class="u-web-divider-mini">文件盒子</t-divider>
                  <t-form-item label="收集文件">
                    <template #help>
                      开启后当前场景将可以将截图收集到
                      <t-link
                        class="pl-2 pr-2"
                        style="font-size: 12px"
                        theme="primary"
                        @click="() => router.replace({ name: 'fileManager' })"
                      >
                        文件盒子 </t-link
                      >中
                    </template>
                    <SwitchPlusEnable :value="scene.fileBoxEnable"
                                      style="width: 48px;"
                                      @change="(val: any) => handleSceneFileBoxEnable(val as boolean, scene)" />
                  </t-form-item>
                  <t-form-item v-if="scene.fileBoxEnable"
                               label="文件标签">
                    <template #help> 设置后这个场景收集到文件将自动打上这个文件标签 </template>
                    <file-tag-select
                      v-model:modal-value="scene.tags"
                      :select-props="{ minCollapsedNum: 3, size: 'small' }"
                      auto-create
                    />
                  </t-form-item>
                  <t-divider class="u-web-divider-mini">截图设置</t-divider>
                  <t-form-item label="延迟截图">
                    <t-input-number
                      v-model:model-value="scene.delayScreenshot"
                      :min="0"
                      :max="600000"
                      :default-value="0"
                      theme="normal"
                      style="width: 130px"
                      size="small"
                    >
                      <template #suffix>毫秒</template>
                    </t-input-number>
                    <template #help>
                      <div class="flex gap-2">
                        <t-link style="font-size: 12px"
                                theme="primary"
                                @click="() => (scene.delayScreenshot = 0)">
                          无延迟
                        </t-link>
                        <t-link style="font-size: 12px"
                                theme="primary"
                                @click="() => (scene.delayScreenshot = 2000)">
                          2 秒
                        </t-link>
                        <t-link style="font-size: 12px"
                                theme="primary"
                                @click="() => (scene.delayScreenshot = 3000)">
                          3 秒
                        </t-link>
                        <t-link style="font-size: 12px"
                                theme="primary"
                                @click="() => (scene.delayScreenshot = 4000)">
                          4 秒
                        </t-link>
                      </div>
                    </template>
                  </t-form-item>
                  <t-form-item v-if="suspendMediaVisible"
                               :disabled="!purchasedUser">
                    <template #label>
                      截图时暂停音频
                      <t-badge size="small" :offset="[6, -8]">
                        <template #content>
                          <div style="top: -10px">
                            <img src="/vip.png" style="width: 14px;" alt="">
                          </div>
                        </template>
                      </t-badge>
                    </template>
                    <SwitchPlusEnable v-model:value="scene.suspendMedia"
                                      style="width: 48px;"
                                      @change="(val: any) => handleSuspendMediaChance(val as boolean)" />
                    <t-button v-if="!purchasedUser"
                              size="small"
                              variant="base"
                              style="margin-left: 6px;"
                              @click="() => router.replace({ name: 'chargeStore' })">购买</t-button>
                    <template #help>
                      <div style="color: #fa8c16;">注意: 未判断是否在播放,会导致已经暂停媒体打开播放</div>
                      <div>开启后截图时将暂停音频播放，截图完成后恢复播放, 建议使用多场景不同指令打开这个功能</div>
                    </template>
                  </t-form-item>
                </t-form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.u-web-tabs-transparency {
  background: transparent;
}

.scene-item {
  background-color: var(--main-background);
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px;
  .scene-header {
    height: 32px;
    justify-content: space-between;
    border-bottom: 1px solid var(--select-hover);
    padding-bottom: 4px;
  }
  .title {
    justify-content: flex-start;
    .main-title {
      padding-right: 4px;
    }
  }
  .utools-panel {
    margin-top: 10px;
    justify-content: space-between;
  }
  .config {
    padding: 12px;
  }
  .config-header {
    padding-top: 16px;
    height: 50px;
    .tool-config-title {
      justify-content: space-between;
      margin-bottom: 16px;
      font-size: 13px;
      .scene-config-tab {
        transition: all 166ms linear;
      }
      .active {
        position: relative;
        //color: rgb(var(--primary-6));
        font-size: 15px;
        &::after {
          content: '';
          position: absolute;
          bottom: -6px;
          left: 0;
          height: 1.5px;
          width: 100%;
          background-color: rgb(var(--primary-6));
        }
      }
    }
  }
  .tool-config {
    .tool-common-config {
      display: flex;
      justify-content: space-between;
      margin-bottom: 18px;
    }
    .tool-config-content {
      padding-left: 32px;
    }
  }
}
</style>
