<script setup lang="ts">
import { computed, ref } from 'vue'
import type { AddSceneModalInstance } from '@/views/sceneManager/template/type'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'

const model = ref({
  sceneTemplateCode: '',
  title: '',
  desc: '',
});

const visible = ref(false);
function show() {
  model.value.sceneTemplateCode = '';
  model.value.title = '';
  model.value.desc = '';
  visible.value = true;
}
const sceneStore = useSceneStore();
const sceneSelectOptions = computed(() => {
  return sceneStore.sceneConfigList.map(({ sceneCode, title }) => {
    return {sceneCode, title}
  })
});

const emits = defineEmits<{(e: 'success', sceneCode: string): void}>()
function handleOk() {
  const sceneCode = sceneStore.addScene(model.value);
  visible.value = false;
  emits('success', sceneCode);
}

defineExpose<AddSceneModalInstance>({
  show,
});
</script>

<template>
  <t-dialog v-model:visible="visible"
            header="新增场景"
            unmount-on-close
            confirmBtn="创建"
            :onConfirm="handleOk">
    <div>
      <t-form :data="model" auto-label-width>
        <t-form-item label="场景模版" name="sceneTemplateCode">
          <t-select v-model:model-value="model.sceneTemplateCode"
                    placeholder="不选默认取所有配置默认值"
                    allow-clear>
            <t-option v-for="(item) in sceneSelectOptions"
                      :key="item.sceneCode"
                      :value="item.sceneCode"
                      :label="item.title">
            </t-option>
          </t-select>
          <template #help>
            新场景配置将完全复制这个模版
          </template>
        </t-form-item>
        <t-form-item label="场景名称">
          <t-input v-model:model-value="model.title"
                   placeholder="选填"></t-input>
        </t-form-item>
        <t-form-item label="场景描述">
          <t-input v-model:model-value="model.desc"
                   placeholder="选填"></t-input>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
