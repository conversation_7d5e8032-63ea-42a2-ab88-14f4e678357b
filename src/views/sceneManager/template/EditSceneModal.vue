<script setup lang="ts">
import { ref } from 'vue'
import type { EditSceneModalInstance } from './type'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'
import type { ISceneConfig } from '@/stores/SceneStore/SceneStore'

const model = ref({
  sceneCode: '',
  title: '',
  desc: '',
});

const visible = ref(false);
function show(sceneConfig: ISceneConfig) {
  model.value.sceneCode = sceneConfig.sceneCode;
  model.value.title = sceneConfig.title;
  model.value.desc = sceneConfig.desc;
  visible.value = true;
}

const sceneStore = useSceneStore();

function handleOk() {
  sceneStore.addScene(model.value);
  visible.value = false;
}

defineExpose<EditSceneModalInstance>({
  show,
});
</script>

<template>
  <t-dialog v-model:visible="visible"
            header="编辑场景"
            unmount-on-close
            confirmBtn="修改"
            :onConfirm="handleOk">
    <div>
      <t-form :model="model" auto-label-width>
        <t-form-item label="场景名称">
          <t-input v-model:model-value="model.title"
                   placeholder="选填"></t-input>
        </t-form-item>
        <t-form-item label="场景描述">
          <t-input v-model:model-value="model.desc"
                   placeholder="选填"></t-input>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
