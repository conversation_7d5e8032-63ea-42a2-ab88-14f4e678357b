<script setup lang="tsx">
import { type FaqCategoryItem, type FaqItem, getFaqCategories, getFaqList } from '@/api/tucao'
import { onMounted, ref, watch } from 'vue'
import type { FrequentlyAskedQuestionDetailInstance } from './templates'
import FrequentlyAskedQuestionDetail from './templates/FrequentlyAskedQuestionDetail.vue'
import { useRoute } from 'vue-router'
import { PageHeader } from '@xiaou66/u-web-ui';
import { NotifyPlugin, Button, Icon } from 'tdesign-vue-next'

const categoriesList = ref<FaqCategoryItem[]>([]);
const currentCategory = ref<number>();
const faqList = ref<FaqItem[]>([]);

const frequentlyAskedQuestionRef = ref<FrequentlyAskedQuestionDetailInstance>();

function openDetail(feqId: number) {
  frequentlyAskedQuestionRef.value?.onShow(feqId);
}

watch(currentCategory, (val) => {
  if (val) {
    getFaqList(val).then(res => {
      faqList.value = res;
      handleRouterJump();
    });
  }
});

onMounted(() => {
  getFaqCategories().then(res => {
    categoriesList.value = res.filter(item => item.title);
    currentCategory.value = categoriesList.value[0].id;
  });
});
const route = useRoute();

async function handleRouterJump() {
  if (route.query.new) {
    const notificationInstance = await NotifyPlugin.info({
      title: '欢迎使用',
      duration: 60 * 1000,
      closeBtn: true,
      content: () => (
        <div>
          <span>欢迎第一次使用本插件, 如果不是第一次使用可退出插件再次进入即可截图</span>
          <div class="welcome-tips-action">
            <Button
              shape="round"
              theme="base"
              onClick={() => utools.outPlugin()}
              v-slots={{
                icon: () => <Icon class="i-u-poweroff" />,
                default: () => '退出插件'
              }}
            />
            <Button
              shape="round"
              type="outline"
              onClick={() => {
                frequentlyAskedQuestionRef.value?.onShow(faqList.value[0].faq_id);
                notificationInstance.close();
              }}
              v-slots={{
                icon: () => <Icon class="i-u-right-one" />,
                default: () => '快速教程'
              }}
            />
          </div>
        </div>
      ),
    })
    delete route.query.new
  }
  if (route.query.title) {
    const title = route.query.title;
    const result = faqList.value.find(item => item.title === title);
    if (result) {
      frequentlyAskedQuestionRef.value?.onShow(result.faq_id);
    }
    delete route.query.title
  }
}

</script>

<template>
  <FrequentlyAskedQuestionDetail ref="frequentlyAskedQuestionRef" />
  <div class="u-main-content">
    <PageHeader title="帮助中心"
                subtitle="在这里你可以找到一些常见问题和工具帮助使用的教程" />

    <div class="u-main-content-inner">
      <div class="u-gap10 feq-header">
        <div :class="currentCategory === item.id ? 'active' : ''"
             class="u-fx u-fc u-fac card-header-item"
             v-for="(item, idx) in categoriesList" :key="idx"
             @click="() => currentCategory = item.id">
          {{ item.title }}
        </div>
      </div>
      <div class="feq-body">
        <div class="u-fx u-fac feq-item"
             v-for="(item, idx) in faqList" :key="idx"
             @click="openDetail(item.faq_id)">
          <div class="u-fx" style="justify-content: space-between; width: 100%;">
            <div>{{ item.title }}</div>
            <div>
              <span class="i-u-share w-5 h-5" style="color: var(--text-color)"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.welcome-tips-action {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 10px;
}
</style>

<style scoped lang="less">
.u-main-content-inner {
  padding: 0 1px;
}
.feq-header {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  .card-header-item {
    width: 160px;
    height: 100px;
    border-radius: 10px;
    font-weight: 700;
    background: rgba(222,222,222, 0.4);
    transition: all 400ms ease-out;
    cursor: pointer;
    color: var(--text-color);
    &:hover {
      background: var(--faq-background-hover-color);
      color: #ffffff;
    }
    &.active {
      background: var(--faq-background-color);
      color: #ffffff;
    }
  }
}
.feq-body {
  display: grid;
  flex-direction: column;
  gap: 10px;
  max-height: calc(100vh - 242px);
  padding: 10px 12px;
  overflow-y: auto;
  .feq-item {
    width: 100%;
    height: 52px;
    padding: 0 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 240ms linear;
    background-color: var(--utools-background);
    color: var(--text-color);
    &:hover {
      background-color: var(--select-hover);
      box-shadow: rgba(0, 0, 0, 0.1) 0 0 0 2px;
    }
  }
}
.desc {
  padding: 10px 0;
}
</style>
