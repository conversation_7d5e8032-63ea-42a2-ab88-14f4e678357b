<script setup lang="ts">
import { ref } from 'vue'
import type { FrequentlyAskedQuestionDetailInstance } from '@/views/frequentlyAskedQuestion/templates'
import { type FaqContentDetail, getFaqContentDetail } from '@/api/tucao'


const faqContentDetail = ref<FaqContentDetail>();
const visible = ref(false);
function onShow(faqId: number) {
  getFaqContentDetail(faqId)
    .then(res => {
      console.log('res', res)
      faqContentDetail.value = res;
      visible.value = true;
      setTimeout(() => {
        document.querySelectorAll('#frequentlyAskedQuestion a')
          .forEach(value => {
            const aElement = value as HTMLAnchorElement;
            aElement.addEventListener('click', () => {
              utools.shellOpenExternal(aElement.href);
            })
          });
      })
    });
}

defineExpose<FrequentlyAskedQuestionDetailInstance>({
  onShow
})
</script>

<template>
  <t-drawer v-model:visible="visible"
            :title="faqContentDetail?.title"
            size="70vw"
            :footer="false">
    <div id="frequentlyAskedQuestion"
         class="faq-content"
         v-html="faqContentDetail?.content" />
  </t-drawer>
</template>

<style lang="less">
.faq-content {
  ul, ol {
    padding-left: 30px;
    line-height: 32px;
  }
  p {
    font-size: 15px;
    line-height: 1.5rem;
    padding-bottom: 15px;
  }
  blockquote {
    padding: 10px 15px;
    background: var(--faq-highlight-background-color);
    margin-bottom: 15px;
    border-radius: 10px;
    p {
      padding-bottom: 0;
    }
  }
  img {
    width: 80%;
    max-width: 550px;
    border-radius: 10px;
    background: linear-gradient(135deg,#fccf31,#f55555);
  }
}
</style>
