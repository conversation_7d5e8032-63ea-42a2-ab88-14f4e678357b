<script setup lang="ts">
import { PageHeader } from '@xiaou66/u-web-ui'
import FuncKeyboardConfig from '@/views/keyboardShortcut/templates/FuncKeyboardConfig.vue'
import GlobalKeyboardConfig from '@/views/keyboardShortcut/templates/GlobalKeyboardConfig.vue'
</script>

<template>
  <div class="u-main-content">
    <PageHeader title="快捷键管理"
                subtitle="可以配置截图一些隐藏使用快捷键和截图全局快捷键" />
    <div class="u-main-content-inner">
      <FuncKeyboardConfig />
      <GlobalKeyboardConfig />
    </div>
  </div>
</template>

<style scoped lang="less">
.header {
  margin-bottom: 10px;
}
:deep(.arco-divider-text) {
  padding: 0;
}
:deep(.arco-divider-text) {
  background: var(--main-background-transparent);
}
:deep(.header-cell) {
  background: transparent;
}
.u-main-content-inner {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
