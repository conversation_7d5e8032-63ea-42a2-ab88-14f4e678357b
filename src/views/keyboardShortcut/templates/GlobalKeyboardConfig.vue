<script setup lang="ts">
import { KEYBOARD } from '@/core/constants/StrConstants'
import { DEFAULT_GLOBAL_KEYBOARD_CONFIG } from '@/core/constants/KeyboardsConstants'
import { computed } from 'vue'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import type { KeyboardConfig } from '@/utils/GlobalKeyboardManager'
import { SettingGroup, SettingItem, SwitchPlusEnable } from '@xiaou66/u-web-ui'

const keyboardConfigKeys = Object.keys(DEFAULT_GLOBAL_KEYBOARD_CONFIG)
  .map(key => ({
    key,
  }));

const isMacOs = computed(() => utools.isMacOS());

const keyboardConfigStore = useKeyboardConfigStore();
const getGlobalKeyboardConfig = computed(() => {
  return (code: string) => {
    const config = keyboardConfigStore.getGlobalKeyboardConfig(code);
    return config ? config : DEFAULT_GLOBAL_KEYBOARD_CONFIG[code].keyboard;
  }
});


function handleMetaChange(code: string, val: boolean, key: 'alt' | 'ctrl') {
  const keyboardConfig = keyboardConfigStore.addGlobalKeyboardConfig({
    ...getGlobalKeyboardConfig.value(code),
    code,
    [key]: val,
  });
  window.globalKeyboardManager.refreshKeyboard(code, keyboardConfig as KeyboardConfig);
}

function handleKeyboardChange(code: string, key: string) {
  const keyboardConfig = keyboardConfigStore.addGlobalKeyboardConfig({
    ...getGlobalKeyboardConfig.value(code),
    code,
    key,
  });
  window.globalKeyboardManager.refreshKeyboard(code, keyboardConfig as KeyboardConfig);
}
const disable = computed(() => !keyboardConfigStore.globalKeyboardEnable);

const options: SelectProps['options'] = KEYBOARD.map(item => (
  {
    label: item,
    value: item,
  }
))
</script>


<template>
  <SettingGroup title="全局快捷键">
    <template #desc>
      <t-alert class="u-alert" theme="warning" show-icon>
        <div>
          开启本功能会导致插件监听用户的全局按键而且需要保证插件需要在后台运行
        </div>
        <div style="font-size: 12px;">
          插件仅会处理按键其响应对应功能, 不会收集其按键用于其他用途, 如果介意就不要开启该功能
        </div>
        <div style="font-size: 12px;"> 注意: uTools 32 位版本不支持</div>
        <template #operation>
          <div class="w-full h-full flex items-center justify-end">
            <SwitchPlusEnable v-model:value="keyboardConfigStore.globalKeyboardEnable"
                              style="width: 46px"
                              @click.stop />
          </div>
        </template>
      </t-alert>
    </template>
    <template v-for="({ key }) in keyboardConfigKeys" :key="key">
      <SettingItem :name="DEFAULT_GLOBAL_KEYBOARD_CONFIG[key].title">
        <div>
          <div class="u-fx">
            <div>
              <t-select size="small"
                        :model-value="getGlobalKeyboardConfig(key).key"
                        :options="options"
                        style="width: 120px"
                        @change="(val: any) => handleKeyboardChange(key, val as any)"
                        :disabled="disable"
                        filterable
                        clearable>
                <a-option v-for="(item, idx) in KEYBOARD" :key="idx">
                  {{item}}
                </a-option>
              </t-select>
            </div>
          </div>
          <div class="u-fx u-fac u-gap5" style="margin-top: 4px;">
            <t-checkbox :model-value="getGlobalKeyboardConfig(key).ctrl"
                        @change="(val: any) => handleMetaChange(key, val, 'ctrl')"
                        :disabled="disable">
                    <span v-if="isMacOs" class="flex items-center">
                      <span class="i-u-command w-3 h-3"></span>
                    </span>
              <span v-else style="font-size: 12px">CTRL</span>
            </t-checkbox>
            <t-checkbox :model-value="getGlobalKeyboardConfig(key).alt"
                        @change="(val: any) => handleMetaChange(key, val, 'alt')"
                        :disabled="disable">
                    <span v-if="isMacOs" class="flex items-center">
                      <span class="i-u-CiOption w-4 h-4"></span>
                    </span>
              <span v-else style="font-size: 12px">ALT</span>
            </t-checkbox>
          </div>
        </div>
      </SettingItem>
    </template>
  </SettingGroup>
</template>

<style  lang="less">
.u-alert {
  .t-alert__title, .t-alert__message {
    justify-content: space-between !important;
  }
}
</style>
