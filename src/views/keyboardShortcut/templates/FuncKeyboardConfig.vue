<script setup lang="ts">
import { KEYBOARD } from '@/core/constants/StrConstants'
import { DEFAULT_FUNC_KEYBOARD_CONFIG } from '@/core/constants/KeyboardsConstants'
import { computed } from 'vue'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import { SettingDivision, SettingGroup, SettingItem } from '@xiaou66/u-web-ui'

const keyboardConfigKeys =  Object.keys(DEFAULT_FUNC_KEYBOARD_CONFIG)
  .map(key => ({
    key,
  }));

const keyboardConfigStore = useKeyboardConfigStore();
const getFuncKeyboardConfig = computed(() => {
  return (code: string) => {
    const config = keyboardConfigStore.getFuncKeyboardConfig(code);
    return config ? config : DEFAULT_FUNC_KEYBOARD_CONFIG[code].keyboard;
  }
});

function handleMetaChange(code: string, val: boolean) {
  keyboardConfigStore.addFuncKeyboardConfig({
    ...getFuncKeyboardConfig.value(code),
    code,
    ctrl: val,
  });
}

function handleKeyboardChange(code: string, key: string) {
  keyboardConfigStore.addFuncKeyboardConfig({
    ...getFuncKeyboardConfig.value(code),
    code,
    key,
  });
}
const isMacOs = computed(() => utools.isMacOS());
const options: SelectProps['options'] = KEYBOARD.map(item => (
  {
    label: item,
    value: item,
  }
))
</script>

<template>
  <setting-group title="截图工具快捷键" desc="这里除了工具快捷键全部的快捷键设置">
    <template #desc>
      <t-alert style="background-color: var(--color-neutral-2)">
        <template #icon></template>
        这里除了工具快捷键全部的快捷键设置
      </t-alert>
    </template>
    <template v-for="item in keyboardConfigKeys"
              :key="item.key">
      <setting-item :title="DEFAULT_FUNC_KEYBOARD_CONFIG[item.key].title">
        <div>
          <div class="u-fx">
            <div>
              <t-select size="small"
                        :model-value="getFuncKeyboardConfig(item.key).key"
                        style="width: 120px"
                        :options="options"
                        @change="(val: any) => handleKeyboardChange(item.key, val as any)"
                        filterable
                        clearable>
                <a-option v-for="(item) in KEYBOARD" :key="item" :value="item">
                  {{item}}
                </a-option>
              </t-select>
            </div>
          </div>
          <div style="margin-top: 4px;">
            <t-checkbox :model-value="getFuncKeyboardConfig(item.key).ctrl"
                        @change="(val: any) => handleMetaChange(item.key, val)">
              <span v-if="isMacOs" class="flex items-center">
                <span class="i-u-command w-3 h-3"></span>
              </span>
              <span v-else style="font-size: 12px">CTRL</span>
            </t-checkbox>
          </div>
        </div>
      </setting-item>
      <SettingDivision />
    </template>
  </setting-group>
</template>

<style scoped lang="less">

</style>
