<script setup lang="ts">
import { onMounted, ref } from 'vue'
import '@/assets/captureContainer.less'
import { WheelWaveContainer } from '@/components/common/container/WheelWaveContainer'
import { LeftMenu } from '@/components/common/LeftMenu'
import { UtoolsLayout } from '@xiaou66/u-web-ui';
import { homeRoutes } from '@/router'

const useUserCount = ref(0)
onMounted(async () => {
  useUserCount.value = await window.plugInUtils.getPlugUserCount()
})

function handlePlugInHome() {
  utools.shellOpenExternal('utools://截图工具 Plus')
}
const avatar = utools.getUser().avatar || '/logo.png';

function handleLoadRouter() {
  console.log('homeRoutes', homeRoutes)
  return homeRoutes;
}
const appName = import.meta.env.VITE_NAME;
</script>

<template>
  <UtoolsLayout :title="appName"
                :avatar="avatar"
                :load-router="handleLoadRouter" />
<!--  <div id="app">-->
<!--    <LeftMenu />-->
<!--    <div id="content-wrapper">-->
<!--      &lt;!&ndash; 全局 header &ndash;&gt;-->
<!--      <div id="global-header">-->
<!--        <div></div>-->
<!--        <div v-if="useUserCount" id="use-user-count" @click="handlePlugInHome">-->
<!--          <WheelWaveContainer :size="2">-->
<!--            <template #0>-->
<!--              <div class="u-fx" style="justify-content: flex-end">-->
<!--                <icon-fire size="14" style="color: #c12c1f" />-->
<!--                你正在与-->
<!--                <a-tooltip>-->
<!--                  <template #content>-->
<!--                    <span style="font-size: 12px">数据来自 uTools 插件统计提供</span>-->
<!--                  </template>-->
<!--                  <span class="gradient-text person-count">{{ useUserCount }}</span>-->
<!--                </a-tooltip>-->
<!--                位小伙伴一起使用-->
<!--              </div>-->
<!--            </template>-->
<!--            <template #1>-->
<!--              <div class="u-fx gradient-text slogan" style="justify-content: flex-end">-->
<!--                用户的声音，简单的操作，携手打造理想工具-->
<!--              </div>-->
<!--            </template>-->
<!--          </WheelWaveContainer>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div id="content">-->
<!--        <router-view />-->
<!--      </div>-->
<!--      <div id="global-footer">-->
<!--        <div class="u-fx">-->
<!--          <div>© {{ new Date().getFullYear() }} [xiaou]。保留所有权利</div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
</template>

<style lang="less">
</style>
<style lang="less" scoped>

#app {
  display: flex;
}
#content-wrapper {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  padding: 0px 10px 10px 0;
  display: grid;
  grid-template-rows: 20px 1fr 15px;
  #global-header {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px 0 20px;
    position: relative;
    top: 0;
    #use-user-count {
      width: 250px;
      display: flex;
      justify-content: flex-end;
      font-size: 12px;
      span {
        padding: 0 2px;
        font-weight: 700;
      }
    }
  }
  #content {
    padding: 6px 0 6px 0;
    box-sizing: border-box;
    background: var(--main-background-transparent);
    border-radius: 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
    max-height: 100%;
    overflow-y: auto;
  }
  #global-footer {
    position: relative;
    bottom: -5px;
    > div {
      justify-content: center;
      font-size: 12px;
    }
  }
}

.slogan {
  background: linear-gradient(to right, #ffd700, #ffa500, #ff8c00);
}
.person-count {
  background: linear-gradient(to right, #ff3019, #ff8e53, #ffa500);
  animation: shine 2s linear infinite;
}
.gradient-text {
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  background-size: 200% auto;
  animation: shine 3s linear infinite;
}

@keyframes shine {
  to {
    background-position: 200% center;
  }
}
</style>
