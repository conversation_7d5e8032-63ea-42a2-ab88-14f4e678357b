<script lang="ts" setup>
import UColorPicker from '@/components/common/UColorPicker/UColorPicker.vue'
import { toRefs } from 'vue'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { SettingDivision, SettingGroup, SettingItem, PageHeader, SwitchPlusEnable } from '@xiaou66/u-web-ui'

const { generalSetting } = toRefs(useSettingUserStore());

const isPurchasedUser = utools.isPurchasedUser();
</script>
<template>
  <div class="u-main-content">
    <PageHeader title="样式设置"
                subtitle="截图整体样式设置" />
    <div class="u-main-content-inner">
      <SettingGroup title="截图边框">
        <SettingItem title="截图悬浮边框">
          <t-radio-group v-model:model-value="generalSetting.screenshotBorder"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="color">颜色边框</t-radio-button>
            <t-radio-button value="shadow">阴影边框</t-radio-button>
            <t-radio-button value="none">无</t-radio-button>
          </t-radio-group>
        </SettingItem>
        <SettingItem v-if="generalSetting.screenshotBorder !=='none'"
                     title="截图边框颜色" >

          <u-color-picker v-model:color="generalSetting.screenshotBorderColor" />
        </SettingItem>
        <SettingDivision></SettingDivision>
        <SettingItem title="截图 mini 模式边框" desc="在截图中按 R 进入 mini 模式">
          <t-radio-group v-model:model-value="generalSetting.screenshotMiniBorder"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="color">颜色边框</t-radio-button>
            <t-radio-button value="none">无</t-radio-button>
          </t-radio-group>
        </SettingItem>
        <SettingItem v-if="generalSetting.screenshotMiniBorder !=='none'"
                     title="截图 mini 模式边框颜色" >
          <u-color-picker v-model:color="generalSetting.screenshotMiniBorderColor" />
        </SettingItem>
        <SettingDivision></SettingDivision>
        <SettingItem title="系统窗口边框阴影"
                     desc="消除系统自带的阴影建议关闭"
                     :click="true"
                     @click="generalSetting.systemHasShadow = !generalSetting.systemHasShadow">
          <SwitchPlusEnable v-model:value="generalSetting.systemHasShadow"
                            style="width: 46px"
                            @click.stop />
        </SettingItem>
      </SettingGroup>

      <SettingGroup title="工具条">
        <SettingItem title="工具条主题"
                     :vip="true">
          <t-radio-group v-model:model-value="generalSetting.toolTheme"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-tooltip>
              <template #content>
                跟随 utools 设置，如果 utools 当前暗黑那么工具条主题也为暗黑主题
              </template>
              <t-radio-button value="utools">跟随 uTools 主题</t-radio-button>
            </t-tooltip>
            <t-tooltip>
              <template #content>
                智能识别图片是暗色图还是亮色图，显示对应主题
              </template>
              <t-radio-button value="imageColor" :disabled="!isPurchasedUser">
                跟随截图主颜色
              </t-radio-button>
            </t-tooltip>
          </t-radio-group>
        </SettingItem>
        <SettingDivision />
        <SettingItem title="工具条大小" :vip="true">
          <t-radio-group v-model:model-value="generalSetting.toolbarSize"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="default">默认</t-radio-button>
            <t-radio-button value="medium" :disabled="!isPurchasedUser">中</t-radio-button>
          </t-radio-group>
        </SettingItem>
        <SettingDivision />
        <SettingItem title="工具条边框">
          <t-radio-group v-model:model-value="generalSetting.toolBorder"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="color">颜色边框</t-radio-button>
            <t-radio-button value="none">无</t-radio-button>
          </t-radio-group>
        </SettingItem>
        <SettingItem v-if="generalSetting.toolBorder !=='none'"
                     title="工具条边框颜色">
          <u-color-picker v-model:color="generalSetting.toolBorderColor" />
        </SettingItem>
      </SettingGroup>
      <SettingGroup title="元素被选择样式">
        <SettingItem title="样式预览">
          <div :style="{border: `${generalSetting.toolSelectedBorderSize}px solid ${generalSetting.toolSelectedBorderColor}`}"
               style="width: 120px;height: 18px;"></div>
        </SettingItem>
        <SettingItem title="边框颜色">
          <u-color-picker v-model:color="generalSetting.toolSelectedBorderColor" />
        </SettingItem>
        <SettingItem title="边框大小">
          <t-input-number style="width: 80px"
                          :min="1"
                          :max="10"
                          v-model:model-value="generalSetting.toolSelectedBorderSize"
                          theme="column"  />
        </SettingItem>
      </SettingGroup>
    </div>
  </div>

</template>
<style scoped lang="less">
.u-main-content {
  .u-main-content-inner {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}
.setting-item-group {
  >div:last-child {
    border-radius: 10px;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }
}
</style>
