<script setup lang="tsx">
import type { RenderFunction } from 'vue'
import { computed, onMounted, ref, useTemplateRef } from 'vue'
import { EnvelopeContainer } from '@/components/common/container'
import { DialogPlugin, NotifyPlugin } from 'tdesign-vue-next'
import { getGoodDetailApi } from '@/api/ucloud/SysGoodApi'

interface PriceOption {
  sysGoodId: number
  title: string
  price: number
  description: string
  remaining?: number;
  headerExtend?: RenderFunction;
  features?: {
    name: string;
    list?: string[];
    content?: RenderFunction;
  }[];
  unit?: string
  deadline?: number
  remainingTime?: number
  goodId?: string;
}

const priceOptionActiveId = ref<number>();
const priceOptions = ref<PriceOption[]>([
  {
    sysGoodId: 2,
    title: '永久插件会员',
    price: 20,
    description: '',
    remaining: 1888,
    goodId: 'zzmp9djv2ruvo0xukfxpwhcsoerb3j37',
    features: [
      {
        name: '一次性付费，终身享受本插件内功能',
        list: [
          '截图多场景使用和管理',
          '截图盒子功能使用',
          '截图时暂停音频',
          '工具条主题: 跟随截图主颜色',
          '工具条主题: 调整工具条大小',
          '文件盒子独立列表: 切换为编辑图功能',
          '后续所有会员功能',
        ],
      },
      {
        name: '一轮售空进行福利抽奖',
        list: [
          '12 个 uTools 年会员',
          '16 个 uTools 半年会员',
          'uTools 会员可换取其他礼品',
        ]
      },
      {
        name: '专属会员服务支持和社群服务',
      }
    ]
  }
]);
const priceOptionActive = computed(() => {
  return priceOptions.value.find(item => item.sysGoodId === priceOptionActiveId.value)!;
})

onMounted(() => {
  priceOptionActiveId.value = priceOptions.value[0].sysGoodId;
})

const envelopeContainerInstance = useTemplateRef<{show: () => void}>('envelopeContainerRef');
const purchasedUser = ref(utools.isPurchasedUser());
async function updateRemaining() {
  purchasedUser.value = utools.isPurchasedUser();
  for (const priceOption of priceOptions.value) {
    const detailRespVo = await getGoodDetailApi(priceOption.sysGoodId);
    priceOption.remaining = detailRespVo.goodInventory;
  }
}
function afterSaleInfo() {
  updateRemaining();
  const dialogInstance = DialogPlugin.alert({
    header: '加我微信, 邀请你加入群',
    top: 50,
    width: 300,
    confirmBtn: '关闭',
    closeBtn: false,
    onConfirm: () => {
      dialogInstance.hide();
      dialogInstance.destroy();
    },
    body: () => (
      <div class="flex items-center justify-center flex-col w-full">
        <div style={{
        }}>
          添加时可以备注 sc 即可
        </div>
        <img
          alt=""
          src="https://on-u.cn/upload/xiaou-wx.jpg"
          class="overflow-hidden"
          style={{
            borderRadius: '10px',
            width: '200px',
          }}
        />
      </div>
    )
    },
  )
}
function handleBuy(goodsId: string) {
  if (purchasedUser.value) {
    afterSaleInfo();
    return;
  }
  // 重新从服务器获取已购买商品量
  const dialogInstance = DialogPlugin.confirm({
    header: '购买须知',
    width: 600,
    confirmBtn: '继续购买',
    cancelBtn: '放弃',
    onConfirm: () => {
      utools.openPayment({ goodsId }, () => {
        console.log("支付成功");
        afterSaleInfo();
        updateRemaining();
        NotifyPlugin.success({
          title: '感谢支持',
          content: '购买成功啦, 如果购买后对应功能还是无法使用, 可以重启 uTools'
        })
      });
      dialogInstance.hide();
      dialogInstance.destroy();
    },
    body: () => (
      <div>
        <div>1.本插件不会主动收集用户的使用数据和截图信息，具备能力的用户可以自行抓包进行安全审计。</div>
        <div>2.购买后不支持退款，请在充分体验后再进行购买。</div>
        <div>3.未成年人需在监护人同意下方可购买。</div>
        <div>4.购买后将与 uTools 账号绑定，后期无法转移至其他 uTools 账号。</div>
        <div>5.本插件中的买断制, 是指插件内所有的离线功能, 云服务的功能除外因为有额外的成本</div>
        <div>6.本插件欢迎任何使用建议和反馈，但最终是否采纳相关建议并进行调整，将由插件作者决定。</div>
        <div>7.最终解释权归插件作者所有。</div>
      </div>
    )
  })
}

onMounted(() => {
  updateRemaining();
});
</script>

<template>
  <EnvelopeContainer ref="envelopeContainerRef" />
  <div class="charge-store">
    <div class="good-list">
      <div v-for="priceOption in priceOptions" :key="priceOption.sysGoodId"
           class="good selected"
           :class="{ selected: priceOptionActiveId === priceOption.sysGoodId }">
        <div>{{ priceOption.title }}</div>
        <div class="price">¥{{ priceOption.price }}</div>
        <div v-if="priceOption.description">
          {{ priceOption.description }}
        </div>
        <div v-else-if="priceOption.remaining">
          仅剩 {{priceOption.remaining}} 份
        </div>
      </div>
    </div>
    <div v-if="priceOptionActive"
         class="good-detail">
      <div class="good-desc">
        <div class="good-desc-title">权益说明</div>
        <div class="content">
          <div v-for="(feature, index) in priceOptionActive.features"
               :key="index">
            <div class="desc flex items-center">
              <div class="serial-number">
                <span class=" i-u-check-small"></span>
              </div>
              <div>{{ feature.name }}</div>
            </div>
            <ul v-if="feature.list">
              <li v-for="(item, index) in feature.list"
                  :key="index">
                {{ item }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="u-fx u-fac u-f-between good-buy">
        <div>
          <div v-if="Math.floor((20 * 100 * 0.85 * 0.15) / 100)"
               style="font-size: 12px; text-align: center; padding-right: 6px;">
            你的购买将支持
            <t-tooltip content="给本插件提供了最基础的国产优秀绘画框架">
              <t-link theme="primary"
                      size="small">Leafer</t-link>
            </t-tooltip>
            15% ({{ Math.floor((20 * 100 * 0.85 * 0.15) / 100) }}元)
          </div>
        </div>
        <div></div>
        <div class="u-fx u-fac"
             style="align-items: flex-end; flex-direction: column; gap: 5px">
          <div class="u-fx u-fac" style="gap: 10px">
            <t-link size="small"
                    theme="primary"
                    @click="envelopeContainerInstance.show()">给用户一封信</t-link>
            <a-button type="primary" shape="round"
                      @click="handleBuy(priceOptionActive.goodId)">
              <template #icon>
                <div class="u-fx u-fac i-u-buy w-4 h-4">
                </div>
              </template>
              {{ purchasedUser ? '售后联系' : '立即购买' }}
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.charge-store {
  min-height: 100%;
  width: 100%;
  background: var(--main-background-transparent);
  padding: 10px;
  display: flex;
  gap: 10px;
  .letter {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    padding: 2px 10px;
    transition: color 300ms linear;

    &:hover {
      color: rgb(var(--arcoblue-5));
    }
  }
  .good-list {
    display: flex;
    flex-direction: column;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 10px;
    .good {
      padding: 16px;
      width: 180px;
      border: 2px solid var(--color-neutral-2);
      background: var(--color-bg-3);
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      cursor: pointer;

      &.selected {
        border: 2px solid rgb(var(--arcoblue-5));
        background: rgba(var(--arcoblue-1), 0.6);
      }

      .price {
        font-size: 24px;
        line-height: 2.25rem;
        font-weight: 700;
      }
    }
  }
  .good-detail {
    width: 100%;
    display: grid;
    grid-template-rows: 1fr auto;
    gap: 14px;
  }
  .good-desc {
    background: var(--color-bg-3);
    padding: 15px;
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
    .good-desc-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-neutral-10);
    }

    .content {
      padding: 20px 0 10px;

      > div {
        margin-bottom: 12px;
      }

      .desc {
        display: flex;
        gap: 10px;
        font-size: 14px;
        color: rgb(var(--arcoblue-5));
      }

      ul {
        margin-left: 40px;
        margin-top: 8px;

        li {
          &::marker {
            color: rgb(var(--arcoblue-5));
          }

          font-size: 12px;
          line-height: 18px;
        }
      }
    }
    .serial-number {
      background: rgb(var(--arcoblue-5));
      color: #ffffff;
      border-radius: 50%;
      font-size: 8px;
      height: 16px;
      width: 16px;
      padding: 3px;
      display: flex;
      >span {
        width: 12px;
        height: 12px;
      }
    }
  }

  .good-buy {
    padding: 16px;
    background: var(--color-bg-3);
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
  }
}

</style>
