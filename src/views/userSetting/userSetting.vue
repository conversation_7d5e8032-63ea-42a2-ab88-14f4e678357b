<script setup lang="ts">
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { computed, toRefs } from 'vue'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { PageHeader, SettingGroup, SettingDivision, SettingItem, SwitchPlusEnable } from '@xiaou66/u-web-ui'

const { generalSetting, scScreenshotWay, scScreenshotCustomExecute, mouseWheelZoom, dragMode, doubleClickCloseCopy } = toRefs(useSettingUserStore());




const isMacOs = computed(() => utools.isMacOS());
const isWindows = computed(() => utools.isWindows());

async function handleEnhanceScreenshot(status: boolean) {
  if (status) {
    utools.setFeature({
      code: 'screenshot.capture.oneself',
      platform: ['darwin', 'win32', 'linux'],
      explain: '截图增强',
      mainHide: true,
      cmds: ['xsc'],
    });
    if (!EnvironmentUtils.haveOpenCvEnvironment()) {
      await EnvironmentUtils.installOpenCv();
    }
  } else {
    utools.removeFeature('screenshot.capture.oneself');
  }
  generalSetting.value.enhanceScreenshot = status;
}

function handleWindowDownloadLibrary() {
  if (!EnvironmentUtils.isWindowsCaptureLibrary()) {
    EnvironmentUtils.installWindowsCaptureLibrary();
  }
}

function isLinux() {
  return utools.isLinux();
}
</script>

<template>
  <div class="u-main-content">
    <page-header title="截图设置"
                 subtitle="可以配置截图方式/截图后样式/全局的功能和主题" />
    <div class="u-main-content-inner">
      <SettingGroup title="截图">
        <SettingItem title="截图方式" desc="sc 指令, 不同操作系统配置将会隔离">
          <t-radio-group v-model:value="scScreenshotWay"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="utools">utools 截图</t-radio-button>
            <t-radio-button v-if="isMacOs" value="local">Mac 自带截图</t-radio-button>
            <t-tooltip content="Windows 10 1607 版本及之后版本">
              <t-radio-button v-if="isWindows"
                       value="local"
                       @click="handleWindowDownloadLibrary">
                Windows 截图工具
              </t-radio-button>
            </t-tooltip>
            <t-radio-button value="custom">自定义</t-radio-button>
          </t-radio-group>
        </SettingItem>
        <SettingItem v-if="scScreenshotWay === 'custom'"
                     title="自定义截图"
                     desc="对接其他的截图工具, 会读取剪贴板图片进行悬浮">
          <t-input v-model:value="scScreenshotCustomExecute"
                          style="width: 380px;"
                          placeholder="自定义截图命令" />
        </SettingItem>
        <SettingDivision />
        <SettingItem title="延迟截图(dsc)" desc="适用于 dsc 指令">
          <div>
            <t-input-number v-model:value="generalSetting.delayScreenshot"
                            :min="0"
                            :max="600000"
                            style="width: 120px;"
                            theme="normal"
                            suffix="毫秒" />
          </div>
          <div style="padding-top: 5px;">
            <a-link class="u-font-size-smail"
                    @click="() => generalSetting.delayScreenshot = 1000">1 秒</a-link>
            <a-link class="u-font-size-smail"
                    @click="() => generalSetting.delayScreenshot = 2000">2 秒</a-link>
            <a-link class="u-font-size-smail"
                    @click="() => generalSetting.delayScreenshot = 3000">3 秒</a-link>
            <a-link class="u-font-size-smail"
                    @click="() => generalSetting.delayScreenshot = 4000">4 秒</a-link>
          </div>
        </SettingItem>
        <SettingDivision />
        <SettingItem title="截图后截图位置" desc="目前截图方式为 utools 截图有效">
          <t-radio-group v-model:value="generalSetting.screenshotLocation"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="center">居中</t-radio-button>
            <t-tooltip content="建议将「全局工具显示」设置为「不显示」">
              <t-radio-button value="mouse">截图位置</t-radio-button>
            </t-tooltip>
          </t-radio-group>
        </SettingItem>
        <SettingDivision />
        <SettingItem title="增强截图(xsc)"
                     desc="开启 xsc 指令, 此截图方式由插件内部实现"
                     :click="true"
                     @click="() => handleEnhanceScreenshot(!generalSetting.enhanceScreenshot)">
          <template #title>
            <div class="u-fx u-fac u-gap5">
              <div>增强截图(xsc)</div>
              <t-tooltip>
                <template #content>当前功能还在测试存在不稳定, 请见谅</template>
                <t-tag class="u-radius10"
                       theme="success"
                       variant="light"
                       style="margin-left: 6px;">测试中</t-tag>
              </t-tooltip>
            </div>
          </template>
          <SwitchPlusEnable v-model:value="generalSetting.enhanceScreenshot"
                            style="width: 46px"
                            @click.stop
                            @change="(val: any) => handleEnhanceScreenshot(val as any)" />
        </SettingItem>
      </SettingGroup>
      <SettingGroup title="兼容">
        <SettingItem title="拖拽模式" desc="这个配置将不会同步到其他设备, 主要解决拖拽问题">
          <t-radio-group v-model:value="dragMode"
                         class="u-web-radio-group"
                         variant="default-filled">
            <t-radio-button value="full">完整版</t-radio-button>
            <t-radio-button value="compatibility">兼容版</t-radio-button>
            <t-radio-button value="NONE">仅拖拽</t-radio-button>
          </t-radio-group>
          <template #extra>
            <div class="u-fx" style="padding-top: 10px;">
              <div class="u-font-size-smail"
                   style="line-height: 20px; color: var(--color-neutral-6)">
                <div><span style="color: #fa541c">完整版</span>: 拖拽体验最好, 但是 windows 下非 100% 缩放拖拽会出现窗口抖动</div>
                <div><span style="color: #1890ff">兼容版</span>: 在指定区域 (截图顶部) 可拖拽, 功能不缺失, 目前不能使用按住空格拖拽</div>
                <div><span style="color: #52c41a">仅拖拽</span>: 拖拽体验好, 但是在使用拖动工具时不能使用其他功能比如双击关闭等</div>
              </div>
            </div>
          </template>
        </SettingItem>
        <SettingDivision />
        <SettingItem title="滚轮缩放"
                     desc="当前配置不会同步, 如果打开这个将可以不用按 CTRL, 开启后将触控板缩放会出现问题"
                     :click="true"
                     @click="() => mouseWheelZoom = !mouseWheelZoom">
          <SwitchPlusEnable v-model:value="mouseWheelZoom"
                            style="width: 46px"
                            @click.stop />
        </SettingItem>
      </SettingGroup>
    </div>
  </div>
</template>

<style lang="less">
</style>
<style scoped lang="less">
.u-main-content-inner {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
:deep(.arco-divider-text) {
  padding: 0;
}
</style>
