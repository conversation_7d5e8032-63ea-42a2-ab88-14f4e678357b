<script setup lang="tsx">
import { h, onBeforeUnmount, onMounted, ref } from 'vue'
import hotkeys from 'hotkeys-js'
import type { ScreenshotInfoData } from '@/utils/ScreenshotDB'
import { cloneDeep } from 'es-toolkit'
import DataHandlerUtils from '@/utils/DataHandlerUtils'
import DataUtils from '@/utils/DataUtils'
import { screenshotByFile, sendScreenshotFileId } from '@/utils/utoolsCodeProcessor'
import { NotifyPlugin, Link } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'
import { UtoolsCodeEvent } from '@/events/UtoolsCodeEvent'
import { useEventListener } from '@vueuse/core'

const searchParams = ref({
  page: 0,
  timeQuery: [],
  pageSize: 100,
  orderDesc: true,
  tags: [],
})
interface TableDataItem {
  id: string
  src?: string
  originalSrc: string
  editSrc: string
  dataInfo: ScreenshotInfoData
}
const searchResult = ref<{
  total: number
  tableData: TableDataItem[]
}>({
  tableData: [],
  total: 0,
})

// 数据不一致
const activeItemId = ref<string>()
const dataInconsistency = ref(false)
const imageDisplayMode = ref<'originalSrc' | 'editSrc'>('originalSrc')
const isAnimating = ref(false)
async function requestList(page = 1, clear = false) {
  if (clear) {
    searchResult.value.tableData.length = 0
  }
  searchParams.value.page = page
  const requestParams: typeof searchParams.value = cloneDeep(searchParams.value)
  // region 处理时间 + 1 天
  requestParams.timeQuery = DataHandlerUtils.handleEndDate(requestParams.timeQuery)
  // endregion
  let where = ''
  if (requestParams.timeQuery && requestParams.timeQuery[0] && requestParams.timeQuery[1]) {
    where += `and fl.create_time >= '${requestParams.timeQuery[0]}' and fl.create_time < '${requestParams.timeQuery[1]}'`
  }
  // file_library_tag
  let join = ''
  if (requestParams.tags.length > 0) {
    join += 'inner join file_library_tag flt on flt.file_id = fl.file_id'
    where += `and flt.tag_id in (${requestParams.tags.join(',')})`
  }
  const sql = `select DISTINCT fl.file_id from file_library fl ${join}
                        where 1=1 ${where} order by create_time ${requestParams.orderDesc ? 'desc' : ''}`
  const result = await window.db.page(sql, {
    page: requestParams.page,
    pageSize: requestParams.pageSize,
  })
  searchResult.value.total = result.total
  dataInconsistency.value = false
  searchResult.value.tableData = result.data
    .map((item) => {
      const originalPath = window.path.join(
        DataUtils.getDataScreenshotLibrary(),
        item.fileId,
        'original.png',
      )
      if (!window.fs.existsSync(originalPath)) {
        dataInconsistency.value = true
        return null
      }

      // 编辑图片
      let editFilePath = window.path.join(
        DataUtils.getDataScreenshotLibrary(),
        item.fileId,
        'edit.png',
      )
      if (!window.fs.existsSync(editFilePath)) {
        editFilePath = ''
      }

      // 数据信息
      const dataInfoPath = window.path.join(
        DataUtils.getDataScreenshotLibrary(),
        item.fileId,
        'info.json',
      )
      let dataInfo = null
      if (window.fs.existsSync(dataInfoPath)) {
        dataInfo = JSON.parse(window.fs.readFileSync(dataInfoPath, 'utf-8'))
      }

      const data = {
        id: item.fileId,
        originalSrc: originalPath ? 'file://' + originalPath.replace(/\s/g, '%20') : '',
        editSrc: editFilePath ? 'file://' + editFilePath.replace(/\s/g, '%20') : '',
        dataInfo,
      }
      // @ts-ignore
      data.src = data[imageDisplayMode.value] || data.originalSrc

      return data
    })
    .filter((item) => item)
  if (searchResult.value.tableData.length) {
    activeItemId.value = searchResult.value.tableData[0].id
  }
  console.log('searchParams', requestParams, searchResult.value)
}
onMounted(() => {
  requestList()
})
useEventListener(window, 'ui.router', (e) => {
  if (e instanceof UtoolsCodeEvent) {
    if (e.getParamsKey('router') && e.getParamsKey('router') === 'historyList') {
      requestList()
    }
  }
})

function handleMouseMove(id: string) {
  activeItemId.value = id
}

const router = useRouter()
async function switchImageDisplayMode() {
  if (isAnimating.value) return
  if (imageDisplayMode.value === 'originalSrc' && !utools.isPurchasedUser()) {
    const messageReturn = await NotifyPlugin.warning({
      duration: 5000,
      title: '提示',
      content: () => (
        <div class="u-fx u-gap5 u-fac">
          <div>需要先买截图会员</div>
          <Link
            style={{ fontSize: '13px', gap: '2px' }}
            class="u-fx u-fac gap-1"
            theme="primary"
            onClick={() => {
              router.replace({ name: 'chargeStore' })
              messageReturn.close()
            }}
          >
            <div class="i-u-buy" />
            购买会员
          </Link>
        </div>
      ),
    })
    return
  }
  isAnimating.value = true
  imageDisplayMode.value = imageDisplayMode.value === 'originalSrc' ? 'editSrc' : 'originalSrc'

  setTimeout(() => {
    isAnimating.value = false
  }, 800)
}
hotkeys('tab', { scope: 'historyList' }, () => {
  switchImageDisplayMode()
})

hotkeys('left', { scope: 'historyList' }, () => {
  const tableData = searchResult.value.tableData
  const index = tableData.findIndex((item) => item.id === activeItemId.value)
  if (index > 0) {
    activeItemId.value = tableData[index - 1].id
  }
})

hotkeys('right', { scope: 'historyList' }, () => {
  const tableData = searchResult.value.tableData
  const index = tableData.findIndex((item) => item.id === activeItemId.value)
  if (index < tableData.length - 1) {
    activeItemId.value = tableData[index + 1].id
  }
})

hotkeys('enter', { scope: 'historyList' }, () => {
  console.log('enter')
  const item = searchResult.value.tableData.find((item) => item.id === activeItemId.value)
  handleSuspendPicture(item)
})

onMounted(() => {
  console.log('historyList::onMounted')
  hotkeys.setScope('historyList')
})

onBeforeUnmount(() => {
  console.log('historyList::onBeforeMount')
  hotkeys.setScope('default')
})
function handleSuspendPicture(item: TableDataItem) {
  NotifyPlugin.success({
    title: '提示',
    content: '正在创建截图窗口...',
    duration: 1000,
  })
  if (imageDisplayMode.value === 'originalSrc') {
    screenshotByFile(item.originalSrc.replace('file://', '').replace(/%20/g, ' '))
  } else if (imageDisplayMode.value === 'editSrc') {
    sendScreenshotFileId(item.id)
  }
}
</script>

<template>
  <div style="padding: 10px">
    <div class="history-list">
      <div
        v-for="item in searchResult.tableData"
        :key="item.id"
        class="u-pointer"
        :class="{ selected: activeItemId === item.id }"
        @mouseenter="handleMouseMove(item.id)"
        @click="() => handleSuspendPicture(item)"
      >
        <img
          :src="
            imageDisplayMode === 'originalSrc' ? item.originalSrc : item.editSrc || item.originalSrc
          "
          alt=""
        />
      </div>
    </div>
  </div>
  <div
    class="all-status u-pointer"
    :class="{ animating: isAnimating }"
    @click.stop="switchImageDisplayMode"
  >
    <a-tooltip content="可以按 TAB 键快捷切换" mini>
      <div>{{ imageDisplayMode === 'originalSrc' ? '原图' : '编辑图' }}</div>
    </a-tooltip>
  </div>
</template>

<style scoped lang="less">
.history-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, 250px);
  grid-auto-rows: 141px;
  gap: 10px;
  > div {
    border-radius: var(--border-radius-medium);
    border: 2px solid transparent;
    padding: 2px;
    transition: border 280ms linear;
    &.selected {
      border: 2px solid rgb(var(--arcoblue-6));
      box-shadow:
        rgba(9, 30, 66, 0.25) 0px 4px 8px -2px,
        rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
    }
    img {
      object-fit: cover;
    }
  }
}
body[arco-theme='dark'] {
  .all-status {
    &.animating {
      color: #ffffff;
    }
  }
}
.all-status {
  position: fixed;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
  color: var(--color-neutral-2);
  background: var(--color-neutral-10);
  backdrop-filter: blur(20px);
  font-size: 13px;
  padding: 6px 18px;
  border-radius: 10px;
  user-select: none;
  box-shadow:
    rgba(17, 17, 26, 0.1) 0 8px 24px,
    rgba(17, 17, 26, 0.1) 0 16px 56px,
    rgba(17, 17, 26, 0.1) 0 24px 80px;
  transition:
    box-shadow 220ms linear,
    background-color 400ms linear,
    color 220ms linear;
  &:hover {
    box-shadow: rgba(3, 102, 214, 0.3) 0px 0px 0px 4px;
  }
  &.animating {
    background-color: rgb(var(--arcoblue-6));
  }
}
</style>
