<script setup lang="tsx">
import ThemeImage from '@/components/common/ThemeImage/ThemeImage.vue'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import { computed, toRefs } from 'vue'
import { FunMouseFunCode } from '@/stores/KeyboardConfigStore/store/FunMouseConfigStore'
import { getToolConfigList } from '@/components/toolbox/ToolBoxHelper'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { SettingDivision, SettingGroup, SettingItem, SwitchPlusEnable } from '@xiaou66/u-web-ui'

const { generalSetting, doubleClickCloseCopy } = toRefs(useSettingUserStore());
const selectDefaultTools = getToolConfigList()
  .filter(item => !item.instant);
const toolBoxObj = selectDefaultTools.reduce((obj: Record<string, ToolboxInfoItemConfig>, item) => {
  obj[item.code] = item;
  return obj;
}, {});

const { getFunMouseConfig, setFunMouseConfig } = useKeyboardConfigStore();
const doubleCloseStatus = computed(() => {
  const funMouseConfig = getFunMouseConfig(FunMouseFunCode.COPY_LOSE);
  if (!funMouseConfig.enable) {
    return "disable";
  }
  if (funMouseConfig.tipsEnable) {
    return 'tipsShow';
  } else {
    return "tipsHide";
  }
});
function handleDoubleClose(code: 'tipsShow' | 'tipsHide' | 'disable') {
  if (code.startsWith('tips')) {
    setFunMouseConfig(FunMouseFunCode.COPY_LOSE, 'enable', true);
    setFunMouseConfig(FunMouseFunCode.COPY_LOSE, 'tipsEnable', code === 'tipsShow');
  } else {
    setFunMouseConfig(FunMouseFunCode.COPY_LOSE, 'enable', false);
  }
}
</script>

<template>
  <SettingGroup title="全局工具">
    <SettingItem title="全局默认工具" desc="当进入截图工具后的默认选择截图工具">
      <t-select v-model:model-value="generalSetting.globalDefaultTool"
                style="width: 138px;"
                allow-search>
        <template #prefixIcon>
          <span class="w-5 h-5" :class="toolBoxObj[generalSetting.globalDefaultTool].toolIcon" />
        </template>
        <t-option v-for="item in selectDefaultTools" :key="item.code" :value="item.code" :label="item.title">
          <div class="flex gap-2 items-center">
            <div class="w-5 h-5" :class="item.toolIcon"></div>
            <div>{{ item.title }}</div>
          </div>
        </t-option>
      </t-select>
    </SettingItem>
    <SettingDivision />
    <SettingItem title="全局工具条显示"
                 desc="截图完成后是否显示工具条"
                 :click="true"
                 @click="generalSetting.toolbarShowMode = generalSetting.toolbarShowMode === 'show' ? 'hide' : 'show'">
      <t-radio-group v-model:value="generalSetting.toolbarShowMode"
                     class="u-web-radio-group"
                     variant="default-filled"
                     @click.stop>
        <t-popup position="top">
          <template #content>
            <div style="width: 200px;">
              <ThemeImage dark-url="https://on-u.cn/upload/toolbox-tool-show-dark.png"
                          default-url="https://on-u.cn/upload/toolbox-tool-show.png" />
            </div>
          </template>
          <t-radio-button value="show">显示</t-radio-button>
        </t-popup>
        <t-popup position="top">
          <template #content>
            <div style="width: 200px;">
              <ThemeImage dark-url="https://on-u.cn/upload/toolbox-tool-hide-dark.png"
                          default-url="https://on-u.cn/upload/toolbox-tool-hide.png" />
            </div>
          </template>
          <t-radio-button value="hide">不显示</t-radio-button>
        </t-popup>
      </t-radio-group>
    </SettingItem>
    <SettingDivision />
    <SettingItem title="工具上快捷键气泡框延迟"
                 desc="鼠标在工具上出现气泡框延迟时间">
      <div class="u-fx"
           style="justify-content: flex-end; flex-direction: column">
        <div class="u-fx" style="justify-content: flex-end">
          <t-input-number v-model:model-value="generalSetting.toolTooltipDelayTime"
                          style="width: 138px;"
                          theme="column"  />
        </div>
        <div class="flex gap-2">
          <t-link  theme="primary"
                   size="small"
                  @click="generalSetting.toolTooltipDelayTime = 300">
            小延迟
          </t-link>
          <t-link theme="primary"
                  size="small"
                  @click="generalSetting.toolTooltipDelayTime = 1000">
            默认
          </t-link>
          <t-link theme="primary"
                  size="small"
                  @click="generalSetting.toolTooltipDelayTime = 3000">
            大延迟
          </t-link>
        </div>
      </div>
    </SettingItem>
    <SettingDivision />
    <SettingItem title="双击关闭截图" desc="鼠标双击后会关闭截图">
      <div class="u-fx" style="flex-direction: column">
        <t-radio-group v-model:value="doubleCloseStatus"
                       class="u-web-radio-group"
                       variant="default-filled">
          <t-radio-button value="tipsShow"
                   @click="handleDoubleClose('tipsShow')">
            启用-提醒
          </t-radio-button>
          <t-radio-button value="tipsHide"
                          @click="handleDoubleClose('tipsHide')">
            启用-关闭提醒
          </t-radio-button>
          <t-radio-button value="disable"
                          @click="handleDoubleClose('disable')">
            禁用
          </t-radio-button>
        </t-radio-group>
        <div v-if="doubleCloseStatus !== 'disable'"
             class="u-fx u-fac u-gap10 u-font-size-smail u-mt10"
             style="justify-content: flex-end">
          <div>双击关闭并复制图片</div>
          <SwitchPlusEnable v-model:value="doubleClickCloseCopy"
                            size="mini">
          </SwitchPlusEnable>
        </div>
      </div>
    </SettingItem>
  </SettingGroup>
</template>

<style scoped lang="less">

</style>
