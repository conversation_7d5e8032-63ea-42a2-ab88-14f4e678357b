import DataUtils from '@/utils/DataUtils'

export interface ILocalOcrModelItem {
  id: string;
  modelName: string;
  modelEnv: string;
  platforms: ('macOS' | 'windows' | 'linux')[];
  provide: string;
  resourceCode: string;
  installDir: string;
  runEnv?: 'java';
  runPlatform?: 'macArm' | 'windowsX64';
  executeFile: string;
}
export const ALLLocalOcrModelList: ILocalOcrModelItem[] = [
  {
    id: '1',
    modelName: 'Apple 内置识别引擎',
    modelEnv: 'macOS 10.13 及以上版本',
    platforms: ['macOS'],
    provide: '官方',
    resourceCode: 'localOcr:apple',
    installDir: 'apple',
    executeFile: 'u-ocr',
  },
  {
    id: '2',
    modelName: 'WindowX64 内置识别引擎',
    modelEnv: 'windows 10 1809及其之后版本, 占用空间小, 准确度低62.5%左右',
    platforms: ['windows'],
    provide: '官方',
    resourceCode: 'localOcr:windows:x64',
    installDir: 'windows',
    runPlatform: 'windowsX64',
    executeFile: 'app-ocr.exe',
  },
  // {
  //   id: '5',
  //   modelName: 'WeChat OCR',
  //   modelEnv: '无',
  //   platforms: ['windows'],
  //   provide: '龙腾九变',
  //   resourceCode: 'ocr:wechatOcr',
  //   installDir: 'wechatOcr',
  //   executeFile: '',
  // },
  {
    id: '3',
    modelName: 'PaddleOcr CPU',
    modelEnv: 'windows x64',
    platforms: ['windows'],
    provide: '官方',
    resourceCode: 'localOcr:windowsX64',
    installDir: 'paddleOcr',
    runEnv: 'java',
    runPlatform: 'windowsX64',
    executeFile: 'u-ocr.jar',
  },
  {
    id: '4',
    modelName: 'PaddleOcr CPU',
    modelEnv: 'APPLE ARM',
    platforms: ['macOS'],
    provide: '官方',
    resourceCode: 'localOcr:macArm',
    installDir: 'paddleOcr',
    runEnv: 'java',
    runPlatform: 'macArm',
    executeFile: 'u-ocr.jar',
  },
];

/**
 * 获取当前系统支持本地 OCR 模型
 */
export function getCurrentSystemLocalOcrModalList() {
  const currentEnv = utools.isMacOS() ? 'macOS' : utools.isWindows() ? 'windows' : 'linux';
  return ALLLocalOcrModelList.filter(item => item.platforms.includes(currentEnv));
}


export function getInstalledLocalOcrModelList(): ILocalOcrModelItem[] {
  return  getCurrentSystemLocalOcrModalList()
    .filter(item => window.fs.existsSync(window.path.join(DataUtils.getDataOcrPath(), item.installDir)));
}
