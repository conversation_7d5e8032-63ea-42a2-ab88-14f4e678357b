<script setup lang="ts">
import { ref } from 'vue'
import type { IOcrSettingModalInstance } from './index'
import { getOcrPlatformInfoList } from '@/leaferApp/ocr'
import type { FormInstanceFunctions } from 'tdesign-vue-next'
import type { IOcrPlatFromConfigItem } from '@/leaferApp/ocr/OcrContentBase'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const visible = ref(false);

const modalInfo = ref({
  title: '新建 OCR 源',
});
const initFormData = {
  platform: '',
  name: '',
};

const formData = ref<Record<string, string>>(initFormData);
function onShow(config?: IOcrPlatFromConfigItem) {
  if (config) {
    formData.value = config as any;
  } else {
    formData.value = {...initFormData};
  }
  visible.value = true;
}

const formRef = ref<FormInstanceFunctions>();

const toolConfigStore = useToolConfigStore()
async function handleAddOcrSource() {
  const errors = await formRef.value.validate();
  if (errors !== true) {
    return false;
  }
  toolConfigStore.ocrCloudAppendConfig(formData.value as any);
  visible.value = false;
}
const ocrPlatformList = getOcrPlatformInfoList();

defineExpose<IOcrSettingModalInstance>({
  onShow
});
</script>

<template>
  <t-dialog v-model:visible="visible"
            destroy-on-close
            :header="modalInfo.title"
           @confirm="handleAddOcrSource">
    <template #body>
      <t-form ref="formRef" :data="formData" :label-width="100" >
        <t-form-item label="平台" :rules="[{required: true}]" name="platform">
          <t-select default-input-value="百度"
                    v-model:value="formData.platform">
            <t-option style="line-height: 30px; font-size: 14px"
                      v-for="(item, index) in ocrPlatformList"
                      :key="index"
                      :label="item.name"
                      :value="item.code" />
          </t-select>
        </t-form-item>
        <t-form-item label="名称" :rules="[{required: true}]" name="name">
          <t-input v-model:value="formData.name"></t-input>
        </t-form-item>
        <div v-if="formData.platform === 'baidu'">
          <t-form-item label="API Key"
                       :rules="[{required: true}]"
                       name="apiKey">
            <t-input v-model:value="formData.apiKey"></t-input>
          </t-form-item>
          <t-form-item label="Secret Key"
                       :rules="[{required: true}]"
                       name="secretKey">
            <t-input type="password" v-model:value="formData.secretKey"></t-input>
          </t-form-item>
        </div>
        <div v-if="formData.platform === '腾讯'">
          <t-form-item label="SecretId"
                       :rules="[{required: true}]"
                       name="secretId">
            <t-input v-model:value="formData.secretId"></t-input>
          </t-form-item>
          <t-form-item label="SecretKey"
                       :rules="[{required: true}]"
                       name="secretKey">
            <t-input type="password"
                     v-model:value="formData.secretKey"></t-input>
          </t-form-item>
        </div>
      </t-form>
    </template>
  </t-dialog>
</template>

<style  lang="less">
.t-dialog__body {
  overflow: hidden;
}
</style>
