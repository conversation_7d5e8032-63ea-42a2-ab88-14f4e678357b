<script lang="tsx" setup>
import { computed, ref } from 'vue'
import type { ILocalOcrModalInstance } from './index'
import type { ILocalOcrModelItem } from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'
import {
  getCurrentSystemLocalOcrModalList,
  getInstalledLocalOcrModelList
} from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { useMultiLoading } from '@/hooks/useLoading'
import hotkeys from 'hotkeys-js'
import type { TableProps } from 'tdesign-vue-next'
import { Button } from 'tdesign-vue-next'

const ocrLocalList = ref(getCurrentSystemLocalOcrModalList());
const installOcrLocalList = ref([]);
function refreshOcrLocalList() {
  installOcrLocalList.value = getInstalledLocalOcrModelList();
}


// 获取已安装的 model 的 id
const installedOcrLocalModelIds = computed(() => {
  return installOcrLocalList.value.map(item => item.id);
});
// 安装 OCR 模型
const [handleInstallOcrLocalModel, installOcrLocalLoading] = useMultiLoading(async (model: ILocalOcrModelItem) => {
  console.log('model', model)
  await EnvironmentUtils.installOcrModel(model);
  refreshOcrLocalList();
}, (model: ILocalOcrModelItem) => model.id);

// 卸载 OCR 模型
function handleUninstallOcrModel(model: ILocalOcrModelItem) {
  EnvironmentUtils.uninstallOcrModel(model);
  refreshOcrLocalList();
}

const visible = ref(false);
function onShow() {
  visible.value = true;
  refreshOcrLocalList();
  hotkeys('esc', (e) => {
    e.stopPropagation();
    visible.value = false;
  })
}
const emits = defineEmits<{
  (e: 'hide'): void
}>()

function handeHide() {
  emits('hide');
  hotkeys.unbind('esc');
}

defineExpose<ILocalOcrModalInstance>({
  show: onShow
});

const columns: TableProps['columns'] = [
  {
    title: '模型名称',
    colKey: 'modelName',
    width: 180
  },
  {
    title: '模型环境',
    colKey: 'modelEnv',
  },
  {
    title: '提供者',
    colKey: 'provide',
    width: 80,
    align: 'center',
  },
  {
    title: '操作',
    width: 100,
    align: 'center',
    cell: (h, { row }) => {
      return (
       <div>
         {installedOcrLocalModelIds.value.includes(row.id) ? (
           <Button size="small"
                   theme="danger"
                   variant="text"
                   onClick={() => handleUninstallOcrModel(row)}>
             卸载
           </Button>
         ) : (
           <Button size="small"
                   theme="primary"
                   variant="text"
                   loading={installOcrLocalLoading.value(row.id)}
                   onClick={() => handleInstallOcrLocalModel(row)}>
             { installOcrLocalLoading.value(row.id) ? "安装中..." : '安装'}
           </Button>
         )}
       </div>
      );
    },
  }
]
</script>
<template>
  <t-dialog v-model:visible="visible"
           :width="650"
           :esc-to-close="false"
           unmount-on-close
           @closed="handeHide">
    <template #header>
      离线 OCR 模型社区
    </template>
    <div>
      <t-table :columns="columns"
               :data="ocrLocalList"
               rowKey="id"
               size="small"
               :pagination="null" />
    </div>
    <template #footer>
      <span class="u-tips">
        除官方模型由官方提供, 其他模型由用户提供如果有侵权请联系我
      </span>
    </template>
  </t-dialog>
</template>
<style lang="less" scoped>

</style>
