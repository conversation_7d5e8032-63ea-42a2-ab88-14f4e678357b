<script setup lang="ts">
import { ref, toRefs } from 'vue'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { SettingDivision, SettingGroup, SettingItem } from '@/components/menu'

const { imageToolsDisabledList } = toRefs(useSettingUserStore());
const data = ref([
  { name: '置顶', code: 'top' },
  { name: '缩放比例', code: 'zoomRatio' },
  { name: '切换 TAB', code: 'tab' },
  { name: '取色', code: 'pickerColor' },
]);
function handleStatusChange(value: boolean, { code }: {code: string}) {
  if (value) {
    if (imageToolsDisabledList.value.indexOf(code) === -1) {
      imageToolsDisabledList.value.push(code);
    }
  } else {
    imageToolsDisabledList.value.splice(imageToolsDisabledList.value.indexOf(code), 1)
  }
}
</script>

<template>
  <SettingGroup title="截图上小工具">
    <template v-for="record in data" :key="record.code">
      <SettingItem :name="record.name"
                   :click="true"
                   @click="handleStatusChange(!imageToolsDisabledList.includes(record.code), record)">
        <t-radio-group :model-value="!!imageToolsDisabledList.includes(record.code)"
                       class="u-web-radio-group"
                       variant="default-filled"
                       @click.stop
                       @change="(val: any) => handleStatusChange(val as boolean, record)">
          <t-radio-button :value="false">显示</t-radio-button>
          <t-radio-button :value="true">隐藏</t-radio-button>
        </t-radio-group>
      </SettingItem>
      <SettingDivision />
    </template>
  </SettingGroup>
</template>

<style scoped lang="less">

</style>
