<script lang="ts" setup>
import ImageTool from '@/views/toolSetting/templates/ImageTool/ImageTool.vue'
import GlobalTool from '@/views/toolSetting/templates/GlobalTool/GlobalTool.vue'
import { PageHeader } from '@xiaou66/u-web-ui'
</script>
<template>
  <div class="u-main-content">
    <PageHeader title="工具设置"
                subtitle="这里是可以调整工具个性化, 打造不一样的工具">
    </PageHeader>
    <div class="u-main-content-inner">
      <GlobalTool />
      <ImageTool />
    </div>
  </div>
</template>
<style lang="less" scoped>
.u-main-content-inner {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
