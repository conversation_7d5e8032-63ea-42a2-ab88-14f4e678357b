(function(){window.__iconpark__=window.__iconpark__||{};var obj=JSON.parse("{\"1020074\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M16 19v-3h16v3M22 34h4M24 18v16\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020075\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M42 8H6a2 2 0 0 0-2 2v28a2 2 0 0 0 2 2h36a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020076\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><circle stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" r=\\\"20\\\" cy=\\\"24\\\" cx=\\\"24\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020077\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M19 11h18v18M11.544 36.456 37 11\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020078\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path fill=\\\"currentColor\\\" d=\\\"M44 36h-8v8h8v-8ZM28 36h-8v8h8v-8ZM12 36H4v8h8v-8ZM44 20h-8v8h8v-8ZM28 20h-8v8h8v-8ZM12 20H4v8h8v-8ZM44 4h-8v8h8V4ZM28 4h-8v8h8V4ZM12 4H4v8h8V4ZM20 12h-8v8h8v-8ZM20 28h-8v8h8v-8ZM36 12h-8v8h8v-8ZM36 28h-8v8h8v-8Z\\\" data-follow-fill=\\\"currentColor\\\"/></g>\"},\"1020081\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><g clip-path=\\\"url(#a)\\\" stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" data-follow-stroke=\\\"currentColor\\\"><path d=\\\"m31 8.999 8 8M8 31.999 36 4l8 7.999-28 28-10 2 2-10ZM31 8.999l8 8M9 31.999l7 7M13 34.999l22-22\\\"/></g><defs><clipPath id=\\\"a\\\"><path fill=\\\"currentColor\\\" d=\\\"M0 0h48v48H0z\\\" data-follow-fill=\\\"currentColor\\\"/></clipPath></defs></g>\"},\"1020082\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m24 4-6 6h-8v8l-6 6 6 6v8h8l6 6 6-6h8v-8l6-6-6-6v-8h-8l-6-6Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 30a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020084\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path d=\\\"M-1-1h2302v1205H-1z\\\"/><path data-follow-stroke=\\\"currentColor\\\" d=\\\"m12.125 38.486 26.75-26.75\\\" stroke=\\\"currentColor\\\" stroke-linecap=\\\"undefined\\\" stroke-linejoin=\\\"undefined\\\" stroke-width=\\\"4\\\"/><ellipse data-follow-stroke=\\\"currentColor\\\" stroke=\\\"currentColor\\\" transform=\\\"rotate(47.112 12.258 38.4)\\\" ry=\\\".917\\\" rx=\\\".989\\\" cy=\\\"38.399\\\" cx=\\\"12.258\\\" stroke-opacity=\\\"null\\\" stroke-width=\\\"2\\\"/><ellipse data-follow-stroke=\\\"currentColor\\\" stroke=\\\"currentColor\\\" transform=\\\"rotate(47.112 38.746 11.883)\\\" ry=\\\".898\\\" rx=\\\"1.008\\\" cy=\\\"11.883\\\" cx=\\\"38.746\\\" stroke-opacity=\\\"null\\\" stroke-width=\\\"2\\\"/></g>\"},\"1020089\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path d=\\\"M-1-1h2302v1205H-1z\\\"/><ellipse data-follow-stroke=\\\"currentColor\\\" stroke-opacity=\\\"null\\\" cx=\\\"24\\\" cy=\\\"24\\\" rx=\\\"21.957\\\" ry=\\\"22.068\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\"/><text data-follow-stroke=\\\"currentColor\\\" data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"0\\\" stroke-opacity=\\\"null\\\" fill-opacity=\\\"null\\\" x=\\\"16.214\\\" y=\\\"33.658\\\" font-size=\\\"28\\\" font-family=\\\"Helvetica, Arial, sans-serif\\\" xml:space=\\\"preserve\\\" font-weight=\\\"bold\\\">1</text></g>\"},\"1020092\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M25.745 44h-.896c-5.21 0-10.07-2.626-12.925-6.984l-2.195-3.35a5.629 5.629 0 0 1 1.072-7.382l2.288-1.936c.26-.22.411-.545.411-.887V7.25a3.25 3.25 0 0 1 6.5 0v9a3.25 3.25 0 0 1 6.5 0v1.5a3.25 3.25 0 0 1 6.5 0v4a3.25 3.25 0 0 1 6.5 0v10.219c0 1.649-.498 3.26-1.43 4.62l-.973 1.423A13.755 13.755 0 0 1 25.745 44Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020093\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 33v6a3 3 0 0 0 3 3h6M33 42h6a3 3 0 0 0 3-3v-6M42 15V9a3 3 0 0 0-3-3h-6M6 15V9a3 3 0 0 1 3-3h6M24 15v20M17 15h14\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020094\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M16 6H8a2 2 0 0 0-2 2v8M16 42H8a2 2 0 0 1-2-2v-8M32 42h8a2 2 0 0 0 2-2v-8M32 6h8a2 2 0 0 1 2 2v8M34 24H14M27 16h-6M27 32h-6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1020095\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 4v32h32\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M20 12h16v16\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 12H4M36 44v-8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1041697\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 28h18M24 20h18M6 25v6M42 17v6M24 42V6M21 6h6M21 42h6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1065052\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path fill=\\\"currentColor\\\" d=\\\"M44 24a2 2 0 1 0-4 0h4ZM24 8a2 2 0 1 0 0-4v4Zm15 32H9v4h30v-4ZM8 39V9H4v30h4Zm32-15v15h4V24h-4ZM9 8h15V4H9v4Zm0 32a1 1 0 0 1-1-1H4a5 5 0 0 0 5 5v-4Zm30 4a5 5 0 0 0 5-5h-4a1 1 0 0 1-1 1v4ZM8 9a1 1 0 0 1 1-1V4a5 5 0 0 0-5 5h4Z\\\" data-follow-fill=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m6 35 10.693-9.802a2 2 0 0 1 2.653-.044L32 36M28 31l4.773-4.773a2 2 0 0 1 2.615-.186L42 31M30 12h12M36 6v12\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1066312\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m10 24 10 10 20-20\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1066334\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M39 14a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM9 44a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM14 4H4v10h10V4ZM44 34H34v10h10V34ZM34 9H14M34 39H14M9 34V14M39 34V14\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1069620\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"2\\\" height=\\\"22\\\" width=\\\"36\\\" y=\\\"22\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M14 22v-8c0-5.523 4.477-10 10-10s10 4.477 10 10v8M24 30v6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1069621\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"2\\\" height=\\\"22\\\" width=\\\"34\\\" y=\\\"22.048\\\" x=\\\"7\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M14 22v-7.995c-.005-5.135 3.923-9.438 9.086-9.954S32.967 6.974 34 12.006M24 30v6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1198669\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 40.833a11.955 11.955 0 0 0 8 3.056c6.627 0 12-5.373 12-12 0-5.301-3.437-9.8-8.204-11.387\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M27.171 27.5c.535 1.359.829 2.84.829 4.39 0 6.627-5.373 12-12 12-6.628 0-12-5.373-12-12 0-5.316 3.455-9.824 8.242-11.4\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 27.89c6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12s-12 5.372-12 12c0 6.627 5.373 12 12 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1199556\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M39.3 6H8.7A2.7 2.7 0 0 0 6 8.7v30.6A2.7 2.7 0 0 0 8.7 42h30.6a2.7 2.7 0 0 0 2.7-2.7V8.7A2.7 2.7 0 0 0 39.3 6Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 6v18H15V6h17Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M26 13v4M10.997 6H36\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200167\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M18 32v4M24 24v12M30 28v8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200168\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 32v4M18 24v12M24 28v8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200169\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 32v4M30 24v12M36 28v8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200170\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M22 30h4M18 24h12M20 18h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200171\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M22 24h4M18 18h12M20 12h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200172\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 36h4M12 30h12M12 24h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200173\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 30h4M12 24h12M12 18h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200174\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M12 24h4M12 18h12M12 12h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200175\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 24h4M24 18h12M28 12h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200176\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 30h4M24 24h12M28 18h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200177\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 36h4M24 30h12M28 24h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1200441\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m8 8 32 32M8 40 40 8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1204049\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 44a19.937 19.937 0 0 0 14.142-5.858A19.937 19.937 0 0 0 44 24a19.938 19.938 0 0 0-5.858-14.142A19.937 19.937 0 0 0 24 4 19.938 19.938 0 0 0 9.858 9.858 19.938 19.938 0 0 0 4 24a19.937 19.937 0 0 0 5.858 14.142A19.938 19.938 0 0 0 24 44Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 28.625v-4a6 6 0 1 0-6-6\\\" data-follow-stroke=\\\"currentColor\\\"/><path fill=\\\"currentColor\\\" d=\\\"M24 37.625a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\\\" clip-rule=\\\"evenodd\\\" fill-rule=\\\"evenodd\\\" data-follow-fill=\\\"currentColor\\\"/></g>\"},\"1205324\":{\"viewBox\":\"0 0 1024 1024\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" d=\\\"M812.98 88.298H209.596c-67.446 0-122.463 54.888-122.463 122.463v603.384c0 67.446 54.888 122.464 122.463 122.464H812.98c67.446 0 122.463-54.889 122.463-122.464V210.761c0-67.575-54.888-122.463-122.463-122.463zm56.7 725.847c0 31.199-25.372 56.701-56.7 56.701H209.596c-31.199 0-56.7-25.373-56.7-56.7V210.76c0-31.198 25.372-56.7 56.7-56.7H812.98c31.199 0 56.7 25.372 56.7 56.7v603.384zm0 0\\\"/><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" d=\\\"M351.736 496.207h131.007v-71.459H351.736v-84.663l134.762-33.14v-66.41l-206.22 51.135v433.8l210.88-75.084v-74.565l-139.422 39.742V496.207zM538.538 768.06h71.459V331.54h94.63v259.296h-58.901l-.13 67.834H772.98V259.953H538.538V768.06zm0 0\\\"/></g>\"},\"1212561\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path data-follow-stroke=\\\"currentColor\\\" d=\\\"M16 6H8a2 2 0 0 0-2 2v8m10 26H8a2 2 0 0 1-2-2v-8m26 10h8a2 2 0 0 0 2-2v-8M32 6h8a2 2 0 0 1 2 2v8\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"4\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\"/><rect data-follow-stroke=\\\"currentColor\\\" x=\\\"14\\\" y=\\\"14\\\" width=\\\"20\\\" height=\\\"20\\\" rx=\\\"2\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"4\\\"/></g>\"},\"1212562\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10 42a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M40.062 8C24 28.433 15.805 38.68 14.242 40.243a6 6 0 0 1-8.485 0\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M38 42a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M42.242 40.243a6 6 0 0 1-8.485 0C32.195 38.68 24 28.446 8 8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1216096\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><g clip-path=\\\"url(#a)\\\"><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.696 17.504c2.639-2.638 5.774-2.565 9.182-.696L32.62 9.745l-.721-4.958L43.213 16.1l-4.947-.71-7.074 12.73c1.783 3.638 1.942 6.544-.697 9.182l-7.778-7.778L6.443 41.556l11.995-16.31-7.742-7.742Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g><defs><clipPath id=\\\"a\\\"><path fill=\\\"currentColor\\\" d=\\\"M0 0h48v48H0z\\\" data-follow-fill=\\\"currentColor\\\"/></clipPath></defs></g>\"},\"1217677\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"2\\\" height=\\\"24\\\" width=\\\"40\\\" y=\\\"18\\\" x=\\\"4\\\" data-follow-stroke=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"24\\\" cx=\\\"14\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"30\\\" cx=\\\"16\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"30\\\" cx=\\\"10\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"24\\\" cx=\\\"20\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"30\\\" cx=\\\"22\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"24\\\" cx=\\\"26\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"30\\\" cx=\\\"28\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"24\\\" cx=\\\"32\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"30\\\" cx=\\\"34\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"2\\\" cy=\\\"24\\\" cx=\\\"38\\\" data-follow-fill=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M17 36h14M33 18v-4.875a1 1 0 0 1 1-1h5a1 1 0 0 0 1-1V6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1217684\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M9.455 14.91h5.454V9.454a5.455 5.455 0 1 0-5.454 5.454ZM9.455 33.09h5.454v5.455a5.455 5.455 0 1 1-5.454-5.454Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M14.909 14.909h18.182v18.182H14.909z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M38.545 14.91h-5.454V9.454a5.455 5.455 0 1 1 5.454 5.454ZM38.545 33.09a5.455 5.455 0 1 1-5.454 5.455v-5.454h5.454Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1218831\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M33.542 27c-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7v6M33.542 15v6c-1.274-4.057-5.064-7-9.542-7-4.477 0-8.268 2.943-9.542 7\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1235404\":{\"viewBox\":\"0 0 1024 1024\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" d=\\\"M747.52 522.24 691.2 460.8c-5.12-5.12-10.24-5.12-10.24 0l-143.36 128c-5.12 5.12-10.24 5.12-10.24 0l-230.4-256c-5.12-5.12-10.24-5.12-10.24 0L153.6 476.16v312.32c0 5.12 5.12 10.24 10.24 10.24h404.48V768C563.2 650.24 640 552.96 747.52 522.24z\\\" fill=\\\"currentColor\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i4.3bdc3a81Dh3Z91\\\" class=\\\"selected\\\"/><path data-follow-fill=\\\"currentColor\\\" d=\\\"M680.96 302.08a66.56 66.56 0 1 0 133.12 0 66.56 66.56 0 1 0-133.12 0Z\\\" fill=\\\"currentColor\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i5.3bdc3a81Dh3Z91\\\" class=\\\"selected\\\"/><path data-follow-fill=\\\"currentColor\\\" d=\\\"M102.4 880.64c-5.12 0-10.24-5.12-10.24-10.24V153.6c0-5.12 5.12-10.24 10.24-10.24h819.2c5.12 0 10.24 5.12 10.24 10.24v384c30.72 15.36 56.32 35.84 76.8 61.44V153.6c0-51.2-40.96-92.16-92.16-92.16H102.4c-51.2 0-92.16 40.96-92.16 92.16v716.8c0 51.2 40.96 92.16 92.16 92.16h547.84c-25.6-20.48-46.08-46.08-61.44-76.8l-486.4-5.12z\\\" fill=\\\"currentColor\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i3.3bdc3a81Dh3Z91\\\" class=\\\"selected\\\"/><path data-follow-fill=\\\"currentColor\\\" d=\\\"M1018.88 752.64 834.56 568.32c-5.12-5.12-10.24-5.12-15.36-5.12-5.12 0-10.24 0-15.36 5.12L619.52 752.64c-5.12 5.12-5.12 15.36-5.12 20.48 5.12 5.12 10.24 10.24 20.48 10.24h102.4v153.6c0 10.24 10.24 20.48 20.48 20.48h122.88c10.24 0 20.48-10.24 20.48-20.48v-153.6h102.4c10.24 0 15.36-5.12 20.48-10.24 0-5.12 0-15.36-5.12-20.48z\\\" fill=\\\"currentColor\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i6.3bdc3a81Dh3Z91\\\" class=\\\"selected\\\"/></g>\"},\"1235417\":{\"viewBox\":\"0 0 1024 1024\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" class=\\\"selected\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i1.3bdc3a81HSx3zD\\\" d=\\\"M128 128a36.571 36.571 0 0 1 36.571-36.571H896A36.571 36.571 0 0 1 932.571 128v182.857a36.571 36.571 0 1 1-73.142 0V164.571H201.143v146.286a36.571 36.571 0 0 1-73.143 0V128z\\\"/><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" class=\\\"selected\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i3.3bdc3a81HSx3zD\\\" d=\\\"M237.714 819.2a113.371 113.371 0 0 1 212.005-55.954c7.022-8.777 14.628-18.176 22.747-28.38l11.118-13.897a49738.386 49738.386 0 0 1-186.88-237.641 36.571 36.571 0 1 1 57.6-45.056c70.29 89.82 128.731 164.206 176.055 223.963 47.433-59.794 106.13-134.217 176.75-224.036a36.571 36.571 0 0 1 57.526 45.202c-75.995 96.658-138.24 175.543-187.574 237.605l11.154 13.97 22.674 28.27A113.371 113.371 0 0 1 822.857 819.2a113.152 113.152 0 0 1-65.243 102.693c-14.629 6.838-30.903 10.678-48.128 10.678a113.079 113.079 0 0 1-80.165-33.206c-11.849-11.813-44.727-51.749-98.267-118.748l-.732-.914-.731.878c-48.97 61.184-80.64 99.84-94.793 115.053a114.14 114.14 0 0 1-38.4 27.539 113.006 113.006 0 0 1-45.312 9.398 113.079 113.079 0 0 1-80.165-33.206l-.292-.293a113.006 113.006 0 0 1-32.915-79.872zm458.825 38.107a40.229 40.229 0 0 0 41.4-9.654l.365-.366a40.229 40.229 0 1 0-41.728 10.02zm-360.301-.731a40.229 40.229 0 1 1 43.995-9.655l-.731.732a40.375 40.375 0 0 1-10.46 7.57c-10.24 5.12-22.235 5.559-32.804 1.39z\\\"/><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" class=\\\"selected\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i2.3bdc3a81HSx3zD\\\" d=\\\"M164.571 420.571a36.571 36.571 0 0 1 36.572 36.572v64a36.571 36.571 0 1 1-73.143 0v-64a36.571 36.571 0 0 1 36.571-36.572zm0 192a36.571 36.571 0 0 1 36.572 36.572v64a36.571 36.571 0 1 1-73.143 0v-64a36.571 36.571 0 0 1 36.571-36.572zm768-155.428a36.571 36.571 0 1 0-73.142 0v64a36.571 36.571 0 1 0 73.142 0v-64zm0 192a36.571 36.571 0 1 0-73.142 0v64a36.571 36.571 0 1 0 73.142 0v-64z\\\"/></g>\"},\"1240026\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path data-follow-stroke=\\\"currentColor\\\" stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m31.337 6.79 11.301.005.004 11.318M19.22 41.674l-11.3-.004-.004-11.318m31.389-20.029L11.02 38.607\\\"/></g>\"},\"1241452\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M20 29H6v14h14V29ZM24 4l10 17H14L24 4ZM36 44a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1243640\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M40.04 22v20h-32V22\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M5.842 13.777C4.312 17.737 7.263 22 11.51 22c3.314 0 6.019-2.686 6.019-6a6 6 0 0 0 6 6h1.018a6 6 0 0 0 6-6c0 3.314 2.706 6 6.02 6 4.248 0 7.201-4.265 5.67-8.228L39.234 6H8.845l-3.003 7.777Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1243700\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M15 8C8.925 8 4 12.925 4 19c0 11 13 21 20 23.326C31 40 44 30 44 19c0-6.075-4.925-11-11-11-3.72 0-7.01 1.847-9 4.674A10.987 10.987 0 0 0 15 8Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1243785\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><mask style=\\\"mask-type:alpha\\\" height=\\\"48\\\" width=\\\"48\\\" y=\\\"0\\\" x=\\\"0\\\" maskUnits=\\\"userSpaceOnUse\\\" id=\\\"a\\\"><path fill=\\\"currentColor\\\" d=\\\"M48 0H0v48h48V0Z\\\" data-follow-fill=\\\"currentColor\\\"/></mask><g mask=\\\"url(#a)\\\" stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" data-follow-stroke=\\\"currentColor\\\"><path d=\\\"M6 24.008V42h36V24M33 15l-9-9-9 9M23.992 32V6\\\"/></g></g>\"},\"1250007\":{\"viewBox\":\"0 0 1024 1024\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" d=\\\"m731.556 550.889-54.278-94.013V162H346.723v294.876l-54.279 94.013-33.679 58.333h224.069V862h58.333V609.222h224.068l-33.679-58.333zm-112.611 0H359.801l45.255-78.381V220.333h213.889v252.175l45.255 78.381h-45.255z\\\" fill=\\\"currentColor\\\" data-spm-anchor-id=\\\"a313x.search_index.0.i0.3bdc3a81JmpAQt\\\" class=\\\"selected\\\"/></g>\"},\"1262673\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M44 14 34 4l-3.75 3.75-3.75 3.75L19 19l-7.5 7.5-3.75 3.75L4 34l10 10 30-30ZM30.25 7.75l-22.5 22.5M9 29l4 4M14 24l6 6M19 19l4 4M24 14l6 6M29 9l4 4\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1276915\":{\"viewBox\":\"0 0 1024 1024\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" transform-origin=\\\"50% 50%\\\" fill=\\\"currentColor\\\" d=\\\"M707.882 485.176 579.461 351.815S136.67 871.822 149.226 885.073l558.656-399.897Z\\\" style=\\\"transform-box:fill-box\\\" transform=\\\"rotate(-.081 -7.54 15.249)\\\"/><path data-follow-fill=\\\"currentColor\\\" transform-origin=\\\"50.0809% 99.4775%\\\" fill=\\\"currentColor\\\" d=\\\"M878.118 353.479 684.056 38.76c-18.162-29.495-60.944-29.767-79.481-.506L405.281 352.973c-19.312 30.464 1.596 70.411 37.636 71.904.654.027 1.309.04 1.964.04h393.356c36.07.036 58.652-38.989 40.649-70.244a49.43 49.43 0 0 0-.712-1.194h-.056Z\\\" style=\\\"transform-box:fill-box\\\" transform=\\\"rotate(44.123)\\\"/></g>\"},\"1279574\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><ellipse stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" ry=\\\"6\\\" rx=\\\"20\\\" cy=\\\"11\\\" cx=\\\"24\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 15.5C6.62 16.6 4 18.208 4 20c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.933-8.158 1.5-13.23 1.5-5.072 0-9.704-.567-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 24.5C6.62 25.6 4 27.208 4 29c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.933-8.158 1.5-13.23 1.5-5.072 0-9.704-.567-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 33.5C6.62 34.6 4 36.208 4 38c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.934-8.158 1.5-13.23 1.5-5.072 0-9.704-.566-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1279614\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M44 16c0 6.627-5.373 12-12 12-2.027 0-3.936-.503-5.61-1.39L9 44l-5-5 17.39-17.39A11.948 11.948 0 0 1 20 16c0-6.627 5.373-12 12-12 2.027 0 3.936.502 5.61 1.39L30 13l5 5 7.61-7.61A11.948 11.948 0 0 1 44 16Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1281515\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path data-follow-stroke=\\\"currentColor\\\" d=\\\"M42 8H6a2 2 0 0 0-2 2v28a2 2 0 0 0 2 2h36a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"4\\\"/><path data-follow-stroke=\\\"currentColor\\\" d=\\\"M35.379 14.398H12.944c-.689 0-1.246.558-1.246 1.247v17.449c0 .689.557 1.247 1.246 1.247h22.435c.69 0 1.247-.558 1.247-1.247V15.645c0-.689-.557-1.247-1.247-1.247Z\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"4\\\"/></g>\"},\"1281553\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M30 6h12v12M31 29H19V17M42 6 19 29\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M22 6H8a2 2 0 0 0-2 2v32a2 2 0 0 0 2 2h32a2 2 0 0 0 2-2V26\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1281634\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M4.592 19.467A2 2 0 0 1 6.537 17h34.926a2 2 0 0 1 1.945 2.467l-5.04 21A2 2 0 0 1 36.423 42H11.577a2 2 0 0 1-1.945-1.533l-5.04-21Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M11 7h8v10h-8zM19 17l6.5-9L38 17\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M15 25h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1281644\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 9h36M6 19h36M6 26l18 14 18-14\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1281645\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 10h36M6 20h36M6 40l18-14 18 14\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1281662\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M40 20c0 6.808-4.252 12.622-10.244 14.934H18.244C12.252 32.622 8 26.808 8 20c0-8.837 7.163-16 16-16s16 7.163 16 16Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m29.756 34.934-.68 8.15a1 1 0 0 1-.996.916h-8.16a1 1 0 0 1-.996-.917l-.68-8.15M18 17v6l6-3 6 3v-6\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1294114\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 44c5.96 0 2.336-8.864 6-13 3.126-3.53 14-1.914 14-7 0-11.046-8.954-20-20-20S4 12.954 4 24s8.954 20 20 20Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M28 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM16 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM17 34a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1300647\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 15V6h9M15 42H6v-9M42 33v9h-9M33 6h9v9M10 24h28\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1300663\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 6h10v10H32zM32 32h10v10H32zM6 32h10v10H6zM6 6h10v10H6zM8 24h22M38 24h2M24 37v2M24 17v14M24 8v2\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1300796\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><circle stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" r=\\\"20\\\" cy=\\\"24\\\" cx=\\\"24\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 16H16M24 34V16\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1300798\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M30 19H20a8 8 0 1 0 0 16h16a8 8 0 0 0 6-13.292\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 24.292A8 8 0 0 1 12 11h16a8 8 0 1 1 0 16H18\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1301875\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M13 12.432v-4.62A2.813 2.813 0 0 1 15.813 5h24.374A2.813 2.813 0 0 1 43 7.813v24.375A2.813 2.813 0 0 1 40.187 35h-4.67\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32.188 13H7.811A2.813 2.813 0 0 0 5 15.813v24.374A2.813 2.813 0 0 0 7.813 43h24.375A2.813 2.813 0 0 0 35 40.187V15.814A2.813 2.813 0 0 0 32.187 13Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1314232\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M11.272 36.728A17.943 17.943 0 0 0 24 42c9.941 0 18-8.059 18-18S33.941 6 24 6c-4.97 0-9.47 2.015-12.728 5.272C9.614 12.93 6 17 6 17\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 9v8h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1314258\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M42 19H6M30 7l12 12M6.799 29h36M6.799 29l12 12\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1334993\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M9.583 27.182C7.867 28.354 7.009 30.293 7.009 33c0 4.06 4.991 11 9.492 11h11.515c4.405 0 7.08-3.85 7.08-6.94V24.6a3.253 3.253 0 0 0-3.243-3.253 3.235 3.235 0 0 0-3.245 3.226v.11\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.981 29.445V7.662a3.217 3.217 0 0 1 6.435 0v15.986\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M17.416 24v-4.192a2.804 2.804 0 0 1 5.608 0v4.62\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M23 24.658v-2.85a2.804 2.804 0 0 1 5.608 0v3.195\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M11 8h30\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m36 12.5 1.667-1.5L41 8l-3.333-3L36 3.5\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1360170\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M7.58 35.42A19.908 19.908 0 0 1 4 24C4 12.954 12.954 4 24 4s20 8.954 20 20-8.954 20-20 20a19.908 19.908 0 0 1-11.42-3.58m-5-5a20.114 20.114 0 0 0 5 5m-5-5L16 27m-3.42 13.42L21 32m-5-5 4-4 5 5-4 4m-5-5 5 5M17 14h4m-2-2v4M28 17h6m-3-3v6M32 29h4m-2-2v4\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1361053\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m24.95 42.36 5.466-11.99 12.689-3.72-9.767-8.88.368-13.163-11.502 6.503-12.46-4.416 2.657 12.9-8.069 10.433 13.145 1.47L24.95 42.36ZM36.178 36.054l8 7.964\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1361058\":{\"viewBox\":\"0 0 24 24\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" d=\\\"M12 21q-3.325 0-5.663-2.3T4 13.1q0-1.625.625-3.037T6.35 7.55L12 2l5.65 5.55q1.1 1.1 1.725 2.513T20 13.1q0 3.3-2.337 5.6T12 21m-5.95-7H17.9q.3-1.8-.337-3.075T16.25 9L12 4.8 7.75 9q-.675.65-1.325 1.925T6.05 14\\\"/></g>\"},\"1363308\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 16c.635 1.22 1.596 2.35 2.823 3.355C12.26 22.173 17.779 24 24 24s11.739-1.827 15.177-4.645C40.404 18.35 41.365 17.22 42 16M28.977 24l2.071 7.727M37.353 21.354l5.657 5.656M5 27.01l5.657-5.657M16.928 31.728 18.998 24\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1363309\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 36c11.046 0 20-12 20-12s-8.954-12-20-12S4 24 4 24s8.954 12 20 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 29a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1364332\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 6v36\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m4 34 12-22v22H4ZM44 34H32V12l12 22Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1364333\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M42 24H6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m14 4 22 12H14V4ZM14 44V32h22L14 44Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1364334\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M14 6.676C8.022 10.134 4 16.597 4 24M14 6.676V14m0-7.324H6.676M6.676 34C10.134 39.978 16.597 44 24 44M6.676 34H14m-7.324 0v7.324M34 41.324C39.978 37.866 44 31.403 44 24M34 41.324V34m0 7.324h7.324M41.324 14C37.866 8.022 31.403 4 24 4m17.324 10H34m7.324 0V6.676\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1364524\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M6 15h36l-2 27H8L6 15Z\\\" clip-rule=\\\"evenodd\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M16 19V6h16v13\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M16 34h16\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1392318\":{\"viewBox\":\"0 0 1792 1792\",\"fill\":\"currentColor\",\"content\":\"<g><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" d=\\\"M1698 94q94 94 94 226.5T1698 546l-225 223 104 104q10 10 10 23t-10 23l-210 210q-10 10-23 10t-23-10l-105-105-603 603q-37 37-90 37H320L64 1792l-64-64 128-256v-203q0-53 37-90l603-603-105-105q-10-10-10-23t10-23l210-210q10-10 23-10t23 10l104 104 223-225q93-94 225.5-94T1698 94M512 1472l576-576-192-192-576 576v192z\\\"/></g>\"},\"1399136\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path fill-opacity=\\\".01\\\" fill=\\\"#fff\\\" d=\\\"M48 0H0v48h48V0Z\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M34 5H8a3 3 0 0 0-3 3v26a3 3 0 0 0 3 3h26a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M44 13.002V42a2 2 0 0 1-2 2H13.003M13 20.486l6 5.525 10-10.292\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"}}");for(var _k in obj){window.__iconpark__[_k] = obj[_k]};var nm={"text":1020074,"rectangle-one":1020075,"round":1020076,"arrow-right-up":1020077,"mosaic":1020078,"pencil":1020081,"config":1020082,"u-line":1020084,"u-serial":1020089,"one-one":1020092,"text-recognition":1020093,"scanning":1020094,"tailoring":1020095,"layout-four":1041697,"add-picture":1065052,"check-small":1066312,"graphic-stitching-four":1066334,"lock":1069620,"unlock":1069621,"application-effect":1198669,"save-one":1199556,"alignment-bottom-center":1200167,"alignment-bottom-left":1200168,"alignment-bottom-right":1200169,"alignment-horizontal-center":1200170,"alignment-horizontal-top":1200171,"alignment-left-bottom":1200172,"alignment-left-center":1200173,"alignment-left-top-dbff13kg":1200174,"alignment-right-top":1200175,"alignment-right-center":1200176,"alignment-right-bottom":1200177,"close":1200441,"help":1204049,"watermark":1205324,"longCapture":1212561,"screenshot":1212562,"pin":1216096,"keyboard-one":1217677,"command":1217684,"update-rotation":1218831,"image-upload":1235404,"long-screenshot":1235417,"double-arrow":1240026,"triangle-round-rectangle":1241452,"application":1243640,"like":1243700,"upload":1243785,"ding":1250007,"ruler":1262673,"u-fat-arrow":1276915,"data-all":1279574,"tool":1279614,"highlight":1281515,"scale":1281553,"receive":1281634,"expand-down-one":1281644,"fold-up-one":1281645,"tips":1281662,"platte":1294114,"scan-code":1300647,"pay-code-one":1300663,"add-text-two":1300796,"link-two":1300798,"copy":1301875,"undo":1314232,"switch":1314258,"hand-drag":1334993,"magic-wand":1360170,"effects":1361053,"opacity":1361058,"preview-close":1363308,"preview-open":1363309,"flip-horizontally":1364332,"flip-vertically":1364333,"rotating-forward":1364334,"buy":1364524,"picker-color":1392318,"full-selection":1399136};for(var _i in nm){window.__iconpark__[_i] = obj[nm[_i]]}})();"object"!=typeof globalThis&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);(()=>{"use strict";var t={816:(t,e,i)=>{var s,r,o,n;i.d(e,{Vm:()=>z,dy:()=>P,Jb:()=>x,Ld:()=>$,sY:()=>T,YP:()=>A});const l=globalThis.trustedTypes,a=l?l.createPolicy("lit-html",{createHTML:t=>t}):void 0,h=`lit$${(Math.random()+"").slice(9)}$`,c="?"+h,d=`<${c}>`,u=document,p=(t="")=>u.createComment(t),v=t=>null===t||"object"!=typeof t&&"function"!=typeof t,f=Array.isArray,y=t=>{var e;return f(t)||"function"==typeof(null===(e=t)||void 0===e?void 0:e[Symbol.iterator])},m=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,g=/-->/g,b=/>/g,S=/>|[ 	\n\r](?:([^\s"'>=/]+)([ 	\n\r]*=[ 	\n\r]*(?:[^ 	\n\r"'`<>=]|("|')|))|$)/g,w=/'/g,k=/"/g,E=/^(?:script|style|textarea)$/i,C=t=>(e,...i)=>({_$litType$:t,strings:e,values:i}),P=C(1),A=C(2),x=Symbol.for("lit-noChange"),$=Symbol.for("lit-nothing"),O=new WeakMap,T=(t,e,i)=>{var s,r;const o=null!==(s=null==i?void 0:i.renderBefore)&&void 0!==s?s:e;let n=o._$litPart$;if(void 0===n){const t=null!==(r=null==i?void 0:i.renderBefore)&&void 0!==r?r:null;o._$litPart$=n=new H(e.insertBefore(p(),t),t,void 0,i)}return n.I(t),n},R=u.createTreeWalker(u,129,null,!1),_=(t,e)=>{const i=t.length-1,s=[];let r,o=2===e?"<svg>":"",n=m;for(let e=0;e<i;e++){const i=t[e];let l,a,c=-1,u=0;for(;u<i.length&&(n.lastIndex=u,a=n.exec(i),null!==a);)u=n.lastIndex,n===m?"!--"===a[1]?n=g:void 0!==a[1]?n=b:void 0!==a[2]?(E.test(a[2])&&(r=RegExp("</"+a[2],"g")),n=S):void 0!==a[3]&&(n=S):n===S?">"===a[0]?(n=null!=r?r:m,c=-1):void 0===a[1]?c=-2:(c=n.lastIndex-a[2].length,l=a[1],n=void 0===a[3]?S:'"'===a[3]?k:w):n===k||n===w?n=S:n===g||n===b?n=m:(n=S,r=void 0);const p=n===S&&t[e+1].startsWith("/>")?" ":"";o+=n===m?i+d:c>=0?(s.push(l),i.slice(0,c)+"$lit$"+i.slice(c)+h+p):i+h+(-2===c?(s.push(void 0),e):p)}const l=o+(t[i]||"<?>")+(2===e?"</svg>":"");return[void 0!==a?a.createHTML(l):l,s]};class N{constructor({strings:t,_$litType$:e},i){let s;this.parts=[];let r=0,o=0;const n=t.length-1,a=this.parts,[d,u]=_(t,e);if(this.el=N.createElement(d,i),R.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(s=R.nextNode())&&a.length<n;){if(1===s.nodeType){if(s.hasAttributes()){const t=[];for(const e of s.getAttributeNames())if(e.endsWith("$lit$")||e.startsWith(h)){const i=u[o++];if(t.push(e),void 0!==i){const t=s.getAttribute(i.toLowerCase()+"$lit$").split(h),e=/([.?@])?(.*)/.exec(i);a.push({type:1,index:r,name:e[2],strings:t,ctor:"."===e[1]?I:"?"===e[1]?j:"@"===e[1]?B:M})}else a.push({type:6,index:r})}for(const e of t)s.removeAttribute(e)}if(E.test(s.tagName)){const t=s.textContent.split(h),e=t.length-1;if(e>0){s.textContent=l?l.emptyScript:"";for(let i=0;i<e;i++)s.append(t[i],p()),R.nextNode(),a.push({type:2,index:++r});s.append(t[e],p())}}}else if(8===s.nodeType)if(s.data===c)a.push({type:2,index:r});else{let t=-1;for(;-1!==(t=s.data.indexOf(h,t+1));)a.push({type:7,index:r}),t+=h.length-1}r++}}static createElement(t,e){const i=u.createElement("template");return i.innerHTML=t,i}}function U(t,e,i=t,s){var r,o,n,l;if(e===x)return e;let a=void 0!==s?null===(r=i.Σi)||void 0===r?void 0:r[s]:i.Σo;const h=v(e)?void 0:e._$litDirective$;return(null==a?void 0:a.constructor)!==h&&(null===(o=null==a?void 0:a.O)||void 0===o||o.call(a,!1),void 0===h?a=void 0:(a=new h(t),a.T(t,i,s)),void 0!==s?(null!==(n=(l=i).Σi)&&void 0!==n?n:l.Σi=[])[s]=a:i.Σo=a),void 0!==a&&(e=U(t,a.S(t,e.values),a,s)),e}class L{constructor(t,e){this.l=[],this.N=void 0,this.D=t,this.M=e}u(t){var e;const{el:{content:i},parts:s}=this.D,r=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:u).importNode(i,!0);R.currentNode=r;let o=R.nextNode(),n=0,l=0,a=s[0];for(;void 0!==a;){if(n===a.index){let e;2===a.type?e=new H(o,o.nextSibling,this,t):1===a.type?e=new a.ctor(o,a.name,a.strings,this,t):6===a.type&&(e=new V(o,this,t)),this.l.push(e),a=s[++l]}n!==(null==a?void 0:a.index)&&(o=R.nextNode(),n++)}return r}v(t){let e=0;for(const i of this.l)void 0!==i&&(void 0!==i.strings?(i.I(t,i,e),e+=i.strings.length-2):i.I(t[e])),e++}}class H{constructor(t,e,i,s){this.type=2,this.N=void 0,this.A=t,this.B=e,this.M=i,this.options=s}setConnected(t){var e;null===(e=this.P)||void 0===e||e.call(this,t)}get parentNode(){return this.A.parentNode}get startNode(){return this.A}get endNode(){return this.B}I(t,e=this){t=U(this,t,e),v(t)?t===$||null==t||""===t?(this.H!==$&&this.R(),this.H=$):t!==this.H&&t!==x&&this.m(t):void 0!==t._$litType$?this._(t):void 0!==t.nodeType?this.$(t):y(t)?this.g(t):this.m(t)}k(t,e=this.B){return this.A.parentNode.insertBefore(t,e)}$(t){this.H!==t&&(this.R(),this.H=this.k(t))}m(t){const e=this.A.nextSibling;null!==e&&3===e.nodeType&&(null===this.B?null===e.nextSibling:e===this.B.previousSibling)?e.data=t:this.$(u.createTextNode(t)),this.H=t}_(t){var e;const{values:i,_$litType$:s}=t,r="number"==typeof s?this.C(t):(void 0===s.el&&(s.el=N.createElement(s.h,this.options)),s);if((null===(e=this.H)||void 0===e?void 0:e.D)===r)this.H.v(i);else{const t=new L(r,this),e=t.u(this.options);t.v(i),this.$(e),this.H=t}}C(t){let e=O.get(t.strings);return void 0===e&&O.set(t.strings,e=new N(t)),e}g(t){f(this.H)||(this.H=[],this.R());const e=this.H;let i,s=0;for(const r of t)s===e.length?e.push(i=new H(this.k(p()),this.k(p()),this,this.options)):i=e[s],i.I(r),s++;s<e.length&&(this.R(i&&i.B.nextSibling,s),e.length=s)}R(t=this.A.nextSibling,e){var i;for(null===(i=this.P)||void 0===i||i.call(this,!1,!0,e);t&&t!==this.B;){const e=t.nextSibling;t.remove(),t=e}}}class M{constructor(t,e,i,s,r){this.type=1,this.H=$,this.N=void 0,this.V=void 0,this.element=t,this.name=e,this.M=s,this.options=r,i.length>2||""!==i[0]||""!==i[1]?(this.H=Array(i.length-1).fill($),this.strings=i):this.H=$}get tagName(){return this.element.tagName}I(t,e=this,i,s){const r=this.strings;let o=!1;if(void 0===r)t=U(this,t,e,0),o=!v(t)||t!==this.H&&t!==x,o&&(this.H=t);else{const s=t;let n,l;for(t=r[0],n=0;n<r.length-1;n++)l=U(this,s[i+n],e,n),l===x&&(l=this.H[n]),o||(o=!v(l)||l!==this.H[n]),l===$?t=$:t!==$&&(t+=(null!=l?l:"")+r[n+1]),this.H[n]=l}o&&!s&&this.W(t)}W(t){t===$?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class I extends M{constructor(){super(...arguments),this.type=3}W(t){this.element[this.name]=t===$?void 0:t}}class j extends M{constructor(){super(...arguments),this.type=4}W(t){t&&t!==$?this.element.setAttribute(this.name,""):this.element.removeAttribute(this.name)}}class B extends M{constructor(){super(...arguments),this.type=5}I(t,e=this){var i;if((t=null!==(i=U(this,t,e,0))&&void 0!==i?i:$)===x)return;const s=this.H,r=t===$&&s!==$||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,o=t!==$&&(s===$||r);r&&this.element.removeEventListener(this.name,this,s),o&&this.element.addEventListener(this.name,this,t),this.H=t}handleEvent(t){var e,i;"function"==typeof this.H?this.H.call(null!==(i=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==i?i:this.element,t):this.H.handleEvent(t)}}class V{constructor(t,e,i){this.element=t,this.type=6,this.N=void 0,this.V=void 0,this.M=e,this.options=i}I(t){U(this,t)}}const z={Z:"$lit$",U:h,Y:c,q:1,X:_,tt:L,it:y,st:U,et:H,ot:M,nt:j,rt:B,lt:I,ht:V};null===(r=(s=globalThis).litHtmlPlatformSupport)||void 0===r||r.call(s,N,H),(null!==(o=(n=globalThis).litHtmlVersions)&&void 0!==o?o:n.litHtmlVersions=[]).push("2.0.0-rc.2")},26:(t,e,i)=>{i.r(e),i.d(e,{customElement:()=>s,eventOptions:()=>a,property:()=>o,query:()=>h,queryAll:()=>c,queryAssignedNodes:()=>v,queryAsync:()=>d,state:()=>n});const s=t=>e=>"function"==typeof e?((t,e)=>(window.customElements.define(t,e),e))(t,e):((t,e)=>{const{kind:i,elements:s}=e;return{kind:i,elements:s,finisher(e){window.customElements.define(t,e)}}})(t,e),r=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?{...e,finisher(i){i.createProperty(e.key,t)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(i){i.createProperty(e.key,t)}};function o(t){return(e,i)=>void 0!==i?((t,e,i)=>{e.constructor.createProperty(i,t)})(t,e,i):r(t,e)}function n(t){return o({...t,state:!0,attribute:!1})}const l=({finisher:t,descriptor:e})=>(i,s)=>{var r;if(void 0===s){const s=null!==(r=i.originalKey)&&void 0!==r?r:i.key,o=null!=e?{kind:"method",placement:"prototype",key:s,descriptor:e(i.key)}:{...i,key:s};return null!=t&&(o.finisher=function(e){t(e,s)}),o}{const r=i.constructor;void 0!==e&&Object.defineProperty(i,s,e(s)),null==t||t(r,s)}};function a(t){return l({finisher:(e,i)=>{Object.assign(e.prototype[i],t)}})}function h(t,e){return l({descriptor:i=>{const s={get(){var e;return null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t)},enumerable:!0,configurable:!0};if(e){const e="symbol"==typeof i?Symbol():"__"+i;s.get=function(){var i;return void 0===this[e]&&(this[e]=null===(i=this.renderRoot)||void 0===i?void 0:i.querySelector(t)),this[e]}}return s}})}function c(t){return l({descriptor:e=>({get(){var e;return null===(e=this.renderRoot)||void 0===e?void 0:e.querySelectorAll(t)},enumerable:!0,configurable:!0})})}function d(t){return l({descriptor:e=>({async get(){var e;return await this.updateComplete,null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t)},enumerable:!0,configurable:!0})})}const u=Element.prototype,p=u.msMatchesSelector||u.webkitMatchesSelector;function v(t="",e=!1,i=""){return l({descriptor:s=>({get(){var s,r;const o="slot"+(t?`[name=${t}]`:":not([name])");let n=null===(r=null===(s=this.renderRoot)||void 0===s?void 0:s.querySelector(o))||void 0===r?void 0:r.assignedNodes({flatten:e});return n&&i&&(n=n.filter((t=>t.nodeType===Node.ELEMENT_NODE&&(t.matches?t.matches(i):p.call(t,i))))),n},enumerable:!0,configurable:!0})})}},23:(t,e,i)=>{i.r(e),i.d(e,{unsafeSVG:()=>l});const s=t=>(...e)=>({_$litDirective$:t,values:e});var r=i(816);class o extends class{constructor(t){}T(t,e,i){this.Σdt=t,this.M=e,this.Σct=i}S(t,e){return this.update(t,e)}update(t,e){return this.render(...e)}}{constructor(t){if(super(t),this.vt=r.Ld,2!==t.type)throw Error(this.constructor.directiveName+"() can only be used in child bindings")}render(t){if(t===r.Ld)return this.Vt=void 0,this.vt=t;if(t===r.Jb)return t;if("string"!=typeof t)throw Error(this.constructor.directiveName+"() called with a non-string value");if(t===this.vt)return this.Vt;this.vt=t;const e=[t];return e.raw=e,this.Vt={_$litType$:this.constructor.resultType,strings:e,values:[]}}}o.directiveName="unsafeHTML",o.resultType=1,s(o);class n extends o{}n.directiveName="unsafeSVG",n.resultType=2;const l=s(n)},249:(t,e,i)=>{i.r(e),i.d(e,{CSSResult:()=>n,LitElement:()=>x,ReactiveElement:()=>b,UpdatingElement:()=>A,_Σ:()=>s.Vm,_Φ:()=>$,adoptStyles:()=>c,css:()=>h,defaultConverter:()=>y,getCompatibleStyle:()=>d,html:()=>s.dy,noChange:()=>s.Jb,notEqual:()=>m,nothing:()=>s.Ld,render:()=>s.sY,supportsAdoptingStyleSheets:()=>r,svg:()=>s.YP,unsafeCSS:()=>l});var s=i(816);const r=window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,o=Symbol();class n{constructor(t,e){if(e!==o)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t}get styleSheet(){return r&&void 0===this.t&&(this.t=new CSSStyleSheet,this.t.replaceSync(this.cssText)),this.t}toString(){return this.cssText}}const l=t=>new n(t+"",o),a=new Map,h=(t,...e)=>{const i=e.reduce(((e,i,s)=>e+(t=>{if(t instanceof n)return t.cssText;if("number"==typeof t)return t;throw Error(`Value passed to 'css' function must be a 'css' function result: ${t}. Use 'unsafeCSS' to pass non-literal values, but\n            take care to ensure page security.`)})(i)+t[s+1]),t[0]);let s=a.get(i);return void 0===s&&a.set(i,s=new n(i,o)),s},c=(t,e)=>{r?t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):e.forEach((e=>{const i=document.createElement("style");i.textContent=e.cssText,t.appendChild(i)}))},d=r?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const i of t.cssRules)e+=i.cssText;return l(e)})(t):t;var u,p,v,f;const y={toAttribute(t,e){switch(e){case Boolean:t=t?"":null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let i=t;switch(e){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},m=(t,e)=>e!==t&&(e==e||t==t),g={attribute:!0,type:String,converter:y,reflect:!1,hasChanged:m};class b extends HTMLElement{constructor(){super(),this.Πi=new Map,this.Πo=void 0,this.Πl=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this.Πh=null,this.u()}static addInitializer(t){var e;null!==(e=this.v)&&void 0!==e||(this.v=[]),this.v.push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach(((e,i)=>{const s=this.Πp(i,e);void 0!==s&&(this.Πm.set(s,i),t.push(s))})),t}static createProperty(t,e=g){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const i="symbol"==typeof t?Symbol():"__"+t,s=this.getPropertyDescriptor(t,i,e);void 0!==s&&Object.defineProperty(this.prototype,t,s)}}static getPropertyDescriptor(t,e,i){return{get(){return this[e]},set(s){const r=this[t];this[e]=s,this.requestUpdate(t,r,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||g}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),this.elementProperties=new Map(t.elementProperties),this.Πm=new Map,this.hasOwnProperty("properties")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const i of e)this.createProperty(i,t[i])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const i=new Set(t.flat(1/0).reverse());for(const t of i)e.unshift(d(t))}else void 0!==t&&e.push(d(t));return e}static Πp(t,e){const i=e.attribute;return!1===i?void 0:"string"==typeof i?i:"string"==typeof t?t.toLowerCase():void 0}u(){var t;this.Πg=new Promise((t=>this.enableUpdating=t)),this.L=new Map,this.Π_(),this.requestUpdate(),null===(t=this.constructor.v)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,i;(null!==(e=this.ΠU)&&void 0!==e?e:this.ΠU=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(i=t.hostConnected)||void 0===i||i.call(t))}removeController(t){var e;null===(e=this.ΠU)||void 0===e||e.splice(this.ΠU.indexOf(t)>>>0,1)}Π_(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this.Πi.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return c(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)})),this.Πl&&(this.Πl(),this.Πo=this.Πl=void 0)}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)})),this.Πo=new Promise((t=>this.Πl=t))}attributeChangedCallback(t,e,i){this.K(t,i)}Πj(t,e,i=g){var s,r;const o=this.constructor.Πp(t,i);if(void 0!==o&&!0===i.reflect){const n=(null!==(r=null===(s=i.converter)||void 0===s?void 0:s.toAttribute)&&void 0!==r?r:y.toAttribute)(e,i.type);this.Πh=t,null==n?this.removeAttribute(o):this.setAttribute(o,n),this.Πh=null}}K(t,e){var i,s,r;const o=this.constructor,n=o.Πm.get(t);if(void 0!==n&&this.Πh!==n){const t=o.getPropertyOptions(n),l=t.converter,a=null!==(r=null!==(s=null===(i=l)||void 0===i?void 0:i.fromAttribute)&&void 0!==s?s:"function"==typeof l?l:null)&&void 0!==r?r:y.fromAttribute;this.Πh=n,this[n]=a(e,t.type),this.Πh=null}}requestUpdate(t,e,i){let s=!0;void 0!==t&&(((i=i||this.constructor.getPropertyOptions(t)).hasChanged||m)(this[t],e)?(this.L.has(t)||this.L.set(t,e),!0===i.reflect&&this.Πh!==t&&(void 0===this.Πk&&(this.Πk=new Map),this.Πk.set(t,i))):s=!1),!this.isUpdatePending&&s&&(this.Πg=this.Πq())}async Πq(){this.isUpdatePending=!0;try{for(await this.Πg;this.Πo;)await this.Πo}catch(t){Promise.reject(t)}const t=this.performUpdate();return null!=t&&await t,!this.isUpdatePending}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this.Πi&&(this.Πi.forEach(((t,e)=>this[e]=t)),this.Πi=void 0);let e=!1;const i=this.L;try{e=this.shouldUpdate(i),e?(this.willUpdate(i),null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(i)):this.Π$()}catch(t){throw e=!1,this.Π$(),t}e&&this.E(i)}willUpdate(t){}E(t){var e;null===(e=this.ΠU)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}Π$(){this.L=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this.Πg}shouldUpdate(t){return!0}update(t){void 0!==this.Πk&&(this.Πk.forEach(((t,e)=>this.Πj(e,this[e],t))),this.Πk=void 0),this.Π$()}updated(t){}firstUpdated(t){}}var S,w,k,E,C,P;b.finalized=!0,b.shadowRootOptions={mode:"open"},null===(p=(u=globalThis).reactiveElementPlatformSupport)||void 0===p||p.call(u,{ReactiveElement:b}),(null!==(v=(f=globalThis).reactiveElementVersions)&&void 0!==v?v:f.reactiveElementVersions=[]).push("1.0.0-rc.1");const A=b;(null!==(S=(P=globalThis).litElementVersions)&&void 0!==S?S:P.litElementVersions=[]).push("3.0.0-rc.1");class x extends b{constructor(){super(...arguments),this.renderOptions={host:this},this.Φt=void 0}createRenderRoot(){var t,e;const i=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=i.firstChild),i}update(t){const e=this.render();super.update(t),this.Φt=(0,s.sY)(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this.Φt)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this.Φt)||void 0===t||t.setConnected(!1)}render(){return s.Jb}}x.finalized=!0,x._$litElement$=!0,null===(k=(w=globalThis).litElementHydrateSupport)||void 0===k||k.call(w,{LitElement:x}),null===(C=(E=globalThis).litElementPlatformSupport)||void 0===C||C.call(E,{LitElement:x});const $={K:(t,e,i)=>{t.K(e,i)},L:t=>t.L}},409:function(t,e,i){var s=this&&this.__decorate||function(t,e,i,s){var r,o=arguments.length,n=o<3?e:null===s?s=Object.getOwnPropertyDescriptor(e,i):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(t,e,i,s);else for(var l=t.length-1;l>=0;l--)(r=t[l])&&(n=(o<3?r(n):o>3?r(e,i,n):r(e,i))||n);return o>3&&n&&Object.defineProperty(e,i,n),n};Object.defineProperty(e,"__esModule",{value:!0}),e.IconparkIconElement=void 0;const r=i(249),o=i(26),n=i(23),l={color:1,fill:1,stroke:1},a={STROKE:{trackAttr:"data-follow-stroke",rawAttr:"stroke"},FILL:{trackAttr:"data-follow-fill",rawAttr:"fill"}};class h extends r.LitElement{constructor(){super(...arguments),this.name="",this.identifyer="",this.size="1em"}get _width(){return this.width||this.size}get _height(){return this.height||this.size}get _stroke(){return this.stroke||this.color}get _fill(){return this.fill||this.color}get SVGConfig(){return(window.__iconpark__||{})[this.identifyer]||(window.__iconpark__||{})[this.name]||{viewBox:"0 0 0 0",content:""}}connectedCallback(){super.connectedCallback(),setTimeout((()=>{this.monkeyPatch("STROKE",!0),this.monkeyPatch("FILL",!0)}))}monkeyPatch(t,e){switch(t){case"STROKE":this.updateDOMByHand(this.strokeAppliedNodes,"STROKE",this._stroke,!!e);break;case"FILL":this.updateDOMByHand(this.fillAppliedNodes,"FILL",this._fill,!!e)}}updateDOMByHand(t,e,i,s){!i&&s||t&&t.forEach((t=>{i&&i===t.getAttribute(a[e].rawAttr)||t.setAttribute(a[e].rawAttr,i||t.getAttribute(a[e].trackAttr))}))}attributeChangedCallback(t,e,i){super.attributeChangedCallback(t,e,i),"name"===t||"identifyer"===t?setTimeout((()=>{this.monkeyPatch("STROKE"),this.monkeyPatch("FILL")})):l[t]&&(this.monkeyPatch("STROKE"),this.monkeyPatch("FILL"))}render(){return r.svg`<svg width=${this._width} height=${this._height} preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg" fill=${this.SVGConfig.fill} viewBox=${this.SVGConfig.viewBox}>${n.unsafeSVG(this.SVGConfig.content)}</svg>`}}h.styles=r.css`:host {display: inline-flex; align-items: center; justify-content: center;} :host([spin]) svg {animation: iconpark-spin 1s infinite linear;} :host([spin][rtl]) svg {animation: iconpark-spin-rtl 1s infinite linear;} :host([rtl]) svg {transform: scaleX(-1);} @keyframes iconpark-spin {0% { -webkit-transform: rotate(0); transform: rotate(0);} 100% {-webkit-transform: rotate(360deg); transform: rotate(360deg);}} @keyframes iconpark-spin-rtl {0% {-webkit-transform: scaleX(-1) rotate(0); transform: scaleX(-1) rotate(0);} 100% {-webkit-transform: scaleX(-1) rotate(360deg); transform: scaleX(-1) rotate(360deg);}}`,s([o.property({reflect:!0})],h.prototype,"name",void 0),s([o.property({reflect:!0,attribute:"icon-id"})],h.prototype,"identifyer",void 0),s([o.property({reflect:!0})],h.prototype,"color",void 0),s([o.property({reflect:!0})],h.prototype,"stroke",void 0),s([o.property({reflect:!0})],h.prototype,"fill",void 0),s([o.property({reflect:!0})],h.prototype,"size",void 0),s([o.property({reflect:!0})],h.prototype,"width",void 0),s([o.property({reflect:!0})],h.prototype,"height",void 0),s([o.queryAll(`[${a.STROKE.trackAttr}]`)],h.prototype,"strokeAppliedNodes",void 0),s([o.queryAll(`[${a.FILL.trackAttr}]`)],h.prototype,"fillAppliedNodes",void 0),e.IconparkIconElement=h,customElements.get("iconpark-icon")||customElements.define("iconpark-icon",h)}},e={};function i(s){var r=e[s];if(void 0!==r)return r.exports;var o=e[s]={exports:{}};return t[s].call(o.exports,o,o.exports,i),o.exports}i.d=(t,e)=>{for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i(409)})();
