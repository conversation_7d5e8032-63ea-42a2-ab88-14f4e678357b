/*滚动条整体部分*/
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
  background-color: var(--color-neutral-1);
  border-radius: 4px;
  border: 1px solid var(--color-neutral-1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
}

::-webkit-scrollbar-thumb:hover {
  background-color: #676e77;
  border: 1px solid #676e77;
}

::-webkit-scrollbar-thumb:active {
  background-color: #4E5969;
  border: 1px solid #4E5969;
}

/*边角，即两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
  background-color: transparent;
}

.u-web-popup {
  .t-popup__content {
    padding: 0 !important;
  }
}

@import "theme-dark";
@import "theme-light";
