<template>
  <div class="u-fx u-fac u-gap10">
    <canvas
      ref="canvas"
      :style="{width: `${ canvasSize.width }px`, height: `${ canvasSize.height }px`}"
      @mousedown="startDrag"
      @mousemove="drag"
      @mouseup="endDrag"
      @mouseleave="endDrag"
    />
    <div>
      <t-input-number size="small"
                      theme="column"
                      style="width: 80px"
                      v-model:model-value="angle"
                      @change="updateAngle"
                      :min="0"
                      :max="359">
      </t-input-number>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'

withDefaults(defineProps<{
  size: 'mini' | 'large' | 'small' | 'medium'
}>(), {
  size: 'medium'
});

const angle = defineModel<number>('angle', {
  default: 0
});

const canvas = ref<HTMLCanvasElement>(null);
const dragging = ref<boolean>(false);
const sizeConfig = {
  mini: {
    radius: 12,
    gap: 8,
    indicatorSize: 2
  },
  medium: {
    radius: 30,
    gap: 8,
    indicatorSize: 6
  },
  small: {
    radius: 20,
    gap: 8,
    indicatorSize: 4,
  }
}
const currentConfig = sizeConfig['mini'];
const radius = currentConfig.radius;
const gap = currentConfig.gap;
const canvasSize = {width: radius * 2 + gap * 2,  height: radius * 2 + gap * 2};
const centerX = radius + gap;
const centerY = radius + gap;

const draw = () => {
  if (!canvas.value) {
    return;
  }
  const ctx = canvas.value.getContext('2d');
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);

  // Draw the circle
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
  ctx.strokeStyle = 'blue';
  ctx.stroke();

  // Draw the line based on the angle
  const rad = (angle.value * (Math.PI / 180)); // 直接使用角度
  const lineX = centerX + Math.cos(rad) * radius; // Extend line length
  const lineY = centerY + Math.sin(rad) * radius;

  ctx.beginPath();
  ctx.moveTo(centerX, centerY);
  ctx.lineTo(lineX, lineY);
  ctx.strokeStyle = 'grey';
  ctx.stroke();

  // Draw the indicator
  const indicatorX = centerX + radius * Math.cos(rad);
  const indicatorY = centerY + radius * Math.sin(rad);
  ctx.beginPath();
  ctx.arc(indicatorX, indicatorY, currentConfig.indicatorSize, 0, Math.PI * 2);
  ctx.fillStyle = 'white';
  ctx.fill();
  ctx.strokeStyle = 'black';
  ctx.stroke();
};

const startDrag = (event) => {
  dragging.value = true;
  updateAngleFromMouse(event);
};

const drag = (event) => {
  if (dragging.value) {
    updateAngleFromMouse(event);
  }
};

const endDrag = () => {
  dragging.value = false;
};

const updateAngleFromMouse = (event) => {
  const rect = canvas.value.getBoundingClientRect();
  const mouseX = event.clientX - rect.left - centerX;
  const mouseY = event.clientY - rect.top - centerY;
  angle.value = Math.round((Math.atan2(mouseY, mouseX) * (180 / Math.PI) + 360) % 360); // 右边为0度
  draw();
};

const updateAngle = () => {
  angle.value = parseFloat(angle.value) || 0;
  draw();
};

const setCanvasSize = () => {
  if (!canvas.value) {
    return;
  }
  const ctx = canvas.value.getContext('2d');
  const dpr = window.devicePixelRatio || 1;
  const rect = canvas.value.getBoundingClientRect();
  canvas.value.width = rect.width * dpr;
  canvas.value.height = rect.height * dpr;
  ctx.scale(dpr, dpr);
  draw();
};

onMounted(() => {
  setCanvasSize();
  window.addEventListener('resize', setCanvasSize);
});
</script>

<style lang="less" scoped>
//canvas {
//  border: 1px solid #000;
//}
</style>
