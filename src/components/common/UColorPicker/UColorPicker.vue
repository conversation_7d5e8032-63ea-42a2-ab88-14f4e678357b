<script lang="ts" setup>
import {ref} from "vue";
import {getCurrentTheme} from "@/hooks/useTheme";
import {ColorPicker} from "vue3-colorpicker";

const dark = ref(getCurrentTheme() === 'dark');
// 颜色
const pureColor = ref('');

function handleColorPickerValue(val: string) {
  return val;
}

const color = defineModel<string>('color', {
  set: (value: string) => handleColorPickerValue(value),
});

// 初始化当前颜色
pureColor.value = color.value;

function handlePureColorChange(val: string) {
  color.value = val;
}

</script>
<template>
  <color-picker :theme="dark ? 'black' : 'white'"
                :pureColor="pureColor"
                @pureColorChange="handlePureColorChange"
                disable-alpha>
  </color-picker>
</template>
<style lang="less">

</style>
