<script lang="ts" setup>
import { ref } from 'vue'

const nickname = ref(utools.getUser()?.nickname || '')
const show = ref(false)
function close() {
  show.value = false
}

defineExpose({
  show: () => (show.value = true)
})
</script>

<template>
  <transition>
    <div v-if="show"
         class="envelope-container-wrapper"
         @click.self="close">
      <div class="envelope-container">
        <div class="envelope-paper">
          <p class="header">
            <slot name="header">亲爱的 {{ nickname }} 用户</slot>
          </p>
          <p class="indent">谢谢你打开这封信!</p>
          <slot>
            <p class="indent">
              不知不觉间，截图工具 Plus
              已陪伴大家度过了大半年的时光。这是我第一次以这种形式与大家沟通。
            </p>
            <p class="indent">
              今天，我想和大家分享一个重要的决定：我计划对这个插件的部分功能进行收费不影响基本的正常使用。做出这个决定让我感到忐忑，因为我从未在网络上尝试过出售自己的产品，而收费意味着我将承担更大的责任。这也是我之前所有插件不收费的主要原因，但现在我已经做好了准备。
            </p>
            <p class="indent">
              关于收费金额的分配，我经过深思熟虑，决定将收入的 15% 捐赠给 Leafer，或用于购买 Leafer
              插件，以支持国产开源项目，因为我们的截图插件底层正是基于这个框架。正所谓“喝水不忘挖井人”。同时，20%
              将用于用户社区建设，期待与大家实现共赢。
            </p>
            <p class="indent">
              在过去不收费的情况下，我始终积极回应用户的反馈，而我相信在收费后，我能够提供更好的服务和支持。最后，我将始终以用户为中心，努力提供更优质的使用体验，期待你的建议与反馈。
            </p>
          </slot>
          <p style="text-align: end">一位青涩的开发者: xiaou</p>
          <p style="text-align: end">2025年01月16日</p>
        </div>
        <div class="u-fx" style="justify-content: center">
          <a-button size="mini" type="outline" @click="close">收起</a-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<style lang="less" scoped>
.envelope-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
}
.envelope-container {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px;
  background: #f5f5dc;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  width: 500px;
  height: 450px;
  color: #0E0E0E;
  .envelope-paper {
    background-color: #f5f5dc;
    position: relative;
    // 修改纹理效果
    background-image: linear-gradient(rgba(222, 184, 135, 0.15) 1px, transparent 1px);
    background-size: 100% 24px;
    p {
      line-height: 24.2px;
      word-break: break-word;
    }
    .indent {
      text-indent: 2em;
    }
  }
}

// 添加 transition 动画
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

.v-enter-to,
.v-leave-from {
  opacity: 1;
}
</style>
