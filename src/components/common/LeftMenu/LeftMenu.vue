<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { onMounted, ref, watch } from 'vue'

const router = useRouter();
const routes = router.options.routes.find(item => item.name === 'home')?.children || router.options.routes;

const menuData = ref<{
  current: string[];
  collapsed: boolean;
}>({
  current: [],
  collapsed: false,
});


function handleMenuClick(name: string): void {
  router.replace({ name })
}

const name = import.meta.env.VITE_NAME;

const route = useRoute()

function handleCurrentName(route) {
  menuData.value.current.length = 0
  menuData.value.current.push(route.name as string)
}
onMounted(() => {
  handleCurrentName(route);
});
// 监听路由处理
watch(route, (newRoute) => {
  handleCurrentName(newRoute)
});
const purchasedUser = ref(utools.isPurchasedUser());

const avatar = utools.getUser().avatar || '/logo.png';
</script>

<template>
  <div class="min">
    <a-menu
      v-model:selected-keys="menuData.current"
      :style="{ width: menuData.collapsed ? '52px' : '150px', height: '100%' }"
      v-model:collapsed="menuData.collapsed"
      :default-open-keys="['0']"
      show-collapse-button
      breakpoint="xl"
      @menu-item-click="handleMenuClick"
      style="position:relative;"
    >
      <div class="u-fx u-gap10 u-fac logo">
        <div class="u-fx u-pos-rel u-fac"
             :style="{width: `${menuData.collapsed ? 28 : 36}px`}">
          <img style="border-radius: 50%; overflow: hidden"
               :src="avatar">
          <img v-if="purchasedUser"
               alt=""
               class="u-pos-abs"
               style="width: 18px; height: auto; top: -10px; right: -14px; transform: translateX(-50%) rotate(30deg)"
               src="/vip.png">
        </div>
        <div>{{name}}</div>
      </div>
      <a-menu-item v-for="(item) in routes" :key="item.name">
        <template #icon>
          <iconpark-icon :name="item.meta!.icon" />
        </template>
        {{item.meta!.title}}
      </a-menu-item>
    </a-menu>
  </div>
</template>

<style scoped lang="less">

:deep(.arco-menu-light) {
  background: var(--utools-background);
}
:deep(.arco-menu-light .arco-menu-item) {
  color: var(--text-color);
  background: var(--utools-background) !important;
}
:deep(.arco-menu-light .arco-menu-item:hover) {
  background: var(--select-hover) !important;
}
:deep(.arco-menu-light .arco-menu-selected) {
  background: var(--select-selected-color) !important;
}
:deep(.arco-menu-inner){
  padding: 35px 4px;
}


:deep(.arco-menu-light .arco-menu-collapse-button) {
  top: 4px;
  z-index: 66;
  right: -32px;
  border-radius: 50%;
  box-sizing: content-box;
  padding: 4px;
}
:deep(.arco-menu-light .arco-menu-collapse-button) {
  background: var(--main-background);
}

:deep(.arco-menu-collapse-button) {
  top: 4px;
}

:deep(.arco-menu-inner){
  padding: 20px 4px;
  .logo {
    width: 100%;
    margin-bottom: 10px;
    div:last-child {
      background-image: linear-gradient(135deg,#feb692,#ea5455);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
}
// 折叠
.arco-menu-collapsed {
  .logo {
    justify-content: center;
    div:last-child {
      display: none;
    }
  }
  .arco-menu-item {
    justify-content: center !important;
    .arco-menu-title {
      display: none !important;
    }
  }
  .arco-menu-icon {
    iconpark-icon {
      font-size: 24px;
    }
  }
}

.min {
  .arco-menu-collapsed {
    :deep(.arco-menu-item) {
      padding: 0 6px;
      line-height: 32px;
      width: 32px;
      border-radius: 4px;
    }
  }
  .arco-menu-icon {
    iconpark-icon {
      font-size: 20px;
    }
  }
  .arco-menu-collapsed {
    width: 42px;
  }
}

.arco-menu-icon {
  iconpark-icon {
    font-size: 16px;
  }
}
.left-menu {
  > div {
    min-height: 100vh;
  }
}
</style>
