<script setup lang="ts">

import { ColorPicker } from 'vue3-colorpicker'
import { ref, toRaw } from 'vue'
import { getCurrentTheme } from '@/hooks/useTheme'
import type { IGradientPaint, IPaint } from '@leafer-ui/interface'
import type { FillColor } from '@/stores/ToolConfigStore'
import LeaferHelper from '@/leaferApp/LeaferHelper'


const props = defineProps<{
  id: string;
}>();

// 颜色
const pureColor = ref('');
function handlePureColorChange(val: string) {
  leaferColor.value = {
    id: props.id,
    type: 'solid',
    color: val,
    edit: true
  };
}

// 渐变色
const gradientColor = ref('');
function handleGradientColorChange(val: string) {

}

function handleColorPickerValue(value: IPaint) {
  if (LeaferHelper.isSolidPaint(value)) {
    const solid = value as any;
    pureColor.value = solid.color;
  } else if (LeaferHelper.isGradientPaint(value)) {
    handleGradientPaint(value);
  }
  return value;
}

const leaferColor = defineModel<FillColor>('leaferColor', {
  set: (value: IPaint) => handleColorPickerValue(value),
});

// 初始化当前颜色
handleColorPickerValue(toRaw(leaferColor.value));




function handleGradientPaint(value: IGradientPaint) {
  // let colorStr = ''
  // if (value.type === 'linear') {
  //   colorStr += 'linear-gradient('
  // }
}


const dark = ref(getCurrentTheme() === 'dark');
</script>

<template>
  <color-picker shape="circle"
                :theme="dark ? 'black' : 'white'"
                :pureColor="pureColor"
                :gradientColor="gradientColor"
                @pureColorChange="handlePureColorChange"
                @gradientColorChange="handleGradientColorChange">
  </color-picker>
</template>

<style  lang="less">
.vc-input-toggle {
  display: none;
}

.vc-color-wrap.round {
  width: 14px;
  height: 14px;
  border: none !important;
  position: relative;
  margin-right: 0;
}

.vc-color-wrap {
  box-shadow: none;
  width: 100%;
  height: 100%;
}

// region fix: 暗色主题
body[arco-theme="dark"] {
  .vc-color-input input {
    color: #bdc3c7 !important;
  }
  .vc-alpha-input__inner {
    color: #bdc3c7 !important;
  }
  .vc-alpha-input {
    color: #576666 !important;
  }
  .vc-input-toggle:hover {
    background: #000;
  }
  .vc-colorpicker--tabs__inner {
    background: #000 !important;
  }
  .vc-colorpicker--tabs {
    background: #000;
  }
}
// endregion
</style>
