<!-- 右键菜单 -->
<script setup lang="ts">
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { onMounted, ref } from 'vue'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import KeyboardsConstants from '@/core/constants/KeyboardsConstants'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import type LayoutTool from '@/components/toolbox/LayoutTool/LayoutTool'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { cloneDeep } from 'es-toolkit'

const currentToolBoxStore = useCurrentToolBoxStore()
const zoomScaleList = ref([50, 75, 100, 125, 150, 175])
function handleZoom(scale: number) {
  const app = currentToolBoxStore.getLeaferApp()
  const { interaction } = app
  const currentScaleRatio = (app.zoomLayer.scale as number) * 100
  const scaleRatio = scale / currentScaleRatio
  interaction.zoom({ x: 0, y: 0, scale: scaleRatio })
}
function handleClose() {
  ScreenshotCapacity.screenshotClose()
}
const transparency = ref(100)
function handleTransparencyChange(transparencyValue: number) {
  if (transparencyValue <= 20) {
    transparencyValue = 20
  } else if (transparencyValue >= 100) {
    transparencyValue = 100
  }
  transparency.value = transparencyValue
  const app = currentToolBoxStore.getLeaferApp()
  const mainImage = app.tree.findId(LeaferConstant.ElementScreenShot)
  const background = app.ground.findId(LeaferConstant.ElementBackground)
  mainImage.set({
    opacity: transparencyValue / 100
  })
  background.set({
    opacity: transparencyValue / 100
  })
}
const toolConfigStore = useToolConfigStore()
function degreesToRadians(degrees: number) {
  return degrees * (Math.PI / 180)
}
function rotate(angle: number) {
  const app = currentToolBoxStore.getLeaferApp()
  const mainImage = app.tree.findId(LeaferConstant.ElementScreenShot)
  const tool = currentToolBoxStore.getToolByCode<LayoutTool>('layout')!
  const config = cloneDeep(toolConfigStore.layoutConfig)
  // 先重置padding
  tool.layoutPadding({
    ...config,
    padding: {
      width: 0,
      height: 0
    }
  })

  // 执行旋转
  mainImage.rotateOf('center', angle)

  const { width: imageWidth, height: imageHeight } = mainImage
  const rotation = mainImage.rotation

  // 计算旋转后的新尺寸
  const radians = degreesToRadians(rotation)
  const newWidth =
    Math.abs(imageWidth * Math.cos(radians)) + Math.abs(imageHeight * Math.sin(radians))
  const newHeight =
    Math.abs(imageHeight * Math.cos(radians)) + Math.abs(imageWidth * Math.sin(radians))
  console.log('imageHeight', imageHeight, imageWidth)
  // 根据不同角度设置图片位置
  console.log('rotation', rotation, mainImage)
  if (rotation === 45) {
    const offsetX = (imageHeight * Math.abs(Math.sin(radians))) / window.devicePixelRatio
    mainImage.set({
      x: offsetX,
      y: 0
    })
  } else if (rotation === -45) {
    const offsetY = ((imageWidth ) * Math.abs(Math.cos(radians))) / window.devicePixelRatio
    mainImage.set({
      x: 0,
      y: offsetY
    })
  } else if (rotation === 135) {
    // 计算135度旋转时的偏移量
    const offsetX =
      ((imageWidth + imageHeight) * Math.abs(Math.sin(radians))) / window.devicePixelRatio
    const offsetY = (imageHeight * Math.abs(Math.cos(radians))) / window.devicePixelRatio
    mainImage.set({
      x: offsetX,
      y: offsetY
    })
  } else if (rotation === -135) {
    const offsetX = ((imageWidth) * Math.abs(Math.sin(radians))) / window.devicePixelRatio
    const offsetY = ((imageWidth + imageHeight) * Math.abs(Math.cos(radians))) / window.devicePixelRatio
    mainImage.set({
      x: offsetX,
      y: offsetY
    })
  } else if (rotation === 180 || rotation === -180) {
    mainImage.set({
      x: imageWidth / window.devicePixelRatio,
      y: imageHeight / window.devicePixelRatio
    })
  } else if (rotation === 90) {
    console.log({
      x: imageHeight / window.devicePixelRatio,
      y: 0
    })
    mainImage.set({
      x: imageHeight / window.devicePixelRatio,
      y: 0
    })
  } else if (rotation === -90) {
    mainImage.set({
      x: 0,
      y: imageWidth / window.devicePixelRatio
    })
  } else if (rotation === 0) {
    mainImage.set({
      x: 0,
      y: 0
    })
  }

  // 设置画布大小
  LeaferHelper.setCanvasSize(app, {
    width: newWidth / window.devicePixelRatio,
    height: newHeight / window.devicePixelRatio
  })

  // 恢复原始padding
  tool.layoutPadding(toolConfigStore.layoutConfig)
}
function handleClick() {
  rotate(90)
}
function handleImageFlip(axis: 'x' | 'y') {
  const app = currentToolBoxStore.getLeaferApp()
  const mainImage = app.tree.findId(LeaferConstant.ElementScreenShot)
  mainImage.flip(axis)
}
const keyboardConfigStore = useKeyboardConfigStore()
onMounted(() => {
  const leftConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.ROTATE_LEFT_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.ROTATE_LEFT_PICTURE, leftConfig, {
    downHandle: () => {
      rotate(-90)
    }
  });

  const left45Config = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.ROTATE_LEFT_45_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.ROTATE_LEFT_45_PICTURE, left45Config, {
    downHandle: () => {
      rotate(-45)
    }
  })
  const rightConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.ROTATE_RIGHT_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.ROTATE_RIGHT_PICTURE, rightConfig, {
    downHandle: () => {
      rotate(90);
    }
  });
  const right45Config = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.ROTATE_RIGHT_45_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.ROTATE_RIGHT_45_PICTURE, right45Config, {
    downHandle: () => {
      rotate(45);
    }
  })

  const increaseConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.TRANSPARENCY_INCREASE_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(
    KeyboardsConstants.TRANSPARENCY_INCREASE_PICTURE,
    increaseConfig,
    {
      downHandle: () => {
        handleTransparencyChange((transparency.value += 10))
      }
    }, 'default');
  const lowerConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(
    KeyboardsConstants.TRANSPARENCY_LOWER_PICTURE
  )
  window.keyboardManager.addKeyboardConfig(
    KeyboardsConstants.TRANSPARENCY_LOWER_PICTURE,
    lowerConfig,
    {
      downHandle: () => {
        handleTransparencyChange((transparency.value -= 10))
      }
    },
    'default');

});
</script>
<template>
  <a-dropdown
    id="screenShotContextMenu"
    trigger="contextMenu"
    alignPoint
    :style="{ display: 'block' }"
  >
    <slot name="default"></slot>
    <template #content>
      <div class="contextMenu">
        <div class="btn">
          <iconpark-icon name="scale" />
          <div>缩放</div>
        </div>
        <div class="layout">
          <div
            v-for="(scale, index) in zoomScaleList"
            :key="index"
            class="layout-item"
            @click="() => handleZoom(scale)"
          >
            {{ scale }}%
          </div>
        </div>
        <div class="btn">
          <iconpark-icon name="opacity" />
          <div>透明度</div>
        </div>
        <div style="padding: 0 4px">
          <a-slider
            v-model:model-value="transparency"
            @change="(val) => handleTransparencyChange(val as number)"
            :min="20"
            :max="100"
          />
        </div>
        <div class="divider"></div>
        <div class="btn edit" @click="() => handleClick()">
          <iconpark-icon name="rotating-forward" />
          <div>旋转</div>
        </div>
        <div class="btn edit" @click="() => handleImageFlip('x')">
          <iconpark-icon name="flip-horizontally" />
          <div>水平翻转</div>
        </div>
        <div class="btn edit" @click="() => handleImageFlip('y')">
          <iconpark-icon name="flip-vertically" />
          <div>垂直翻转</div>
        </div>
        <div class="divider"></div>
        <div class="btn edit" @click="handleClose">
          <iconpark-icon name="close" />
          <div>关闭</div>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<style lang="less">
#screenShotContextMenu > .arco-trigger-popup-wrapper > .arco-trigger-content > .arco-dropdown {
  padding: 0;
  background-color: transparent;
  border: none;
  color: #ffffff;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
#screenShotContextMenu .arco-dropdown-list-wrapper {
  max-height: 100% !important;
}
</style>
<style lang="less" scoped>
.contextMenu {
  -webkit-app-region: none;
  padding: 6px;
  background-color: rgba(11, 11, 11, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(11, 11, 11, 0.8);
  border-radius: 10px;
  color: #ffffff;
  overflow-y: auto;
  .btn {
    cursor: pointer;
    width: 160px;
    padding: 4px 8px;
    margin-top: 10px;
    font-size: 12px;
    display: flex;
    gap: 4px;
    align-items: center;
    &:first-child {
      margin-top: 0px;
    }
    &.edit {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0%;
        transform: translate(-0%, -50%);
        width: 0%;
        height: 0%;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        transition: height 200ms ease-in-out;
        transition: width 500ms ease-in-out;
        max-width: 100%;
        max-height: 100%;
      }
      &:hover::before {
        width: 200%; /* 扩散到父元素的两倍宽度 */
        height: 200%; /* 扩散到父元素的两倍高度 */
      }
    }
  }
  .divider {
    margin: 6px 0;
    height: 1px;
    background: #7c7776;
    width: 100%;
  }
}

.layout {
  display: grid;
  gap: 4px;
  grid-template-columns: repeat(3, 50px);
}
.layout-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  gap: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 200ms linear;
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  &.active {
    background: rgba(255, 255, 255, 0.3);
  }
}
</style>
