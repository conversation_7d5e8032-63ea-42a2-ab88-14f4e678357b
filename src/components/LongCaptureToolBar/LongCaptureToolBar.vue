<script setup lang="ts">
import HtmlDomConstants from "@/core/constants/HtmlDomConstants";
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import type LongCaptureTool from '@/components/toolbox/LongCaptureTool/LongCaptureTool';
// 关闭窗口
function handleWindowClose() {
  window.winHelper.getCurrentWindow().close();
}

const currentToolBoxStore = useCurrentToolBoxStore()

// 使用
function handleCopyImage() {
  const tool = currentToolBoxStore.getToolByCode('longCapture') as LongCaptureTool;
  const base64 = tool.stopLong();
  utools.copyImage(base64);
  handleWindowClose();
}
</script>

<template>
  <div>
    <div :id="HtmlDomConstants.LongCaptureToolboxContainer"
         class="u-fx u-fac toolbox-bar">
      <div class="toolbox-wrapper">
        <a-tooltip content="关闭">
          <div class="toolbox" @click="handleWindowClose">
            <iconpark-icon name="close" />
          </div>
        </a-tooltip>
      </div>
      <div class="toolbox-wrapper">
        <a-tooltip content="复制&关闭">
          <div class="toolbox" @click="handleCopyImage">
            <iconpark-icon name="check-small" />
          </div>
        </a-tooltip>
      </div>
    </div>
    <div :id="HtmlDomConstants.LongCaptureToolPreview">
      <canvas :id="HtmlDomConstants.LongCapturePreviewCanvas" style="width: 100%;max-height: 80%;">
      </canvas>
    </div>
  </div>
</template>

<style scoped lang="less">
#longCaptureToolboxContainer {
  display: none;
}
#longCapturePreview {
  display: none;
  position: fixed;
  width: 300px;
  top: 0;
  right: 0;
}
</style>
