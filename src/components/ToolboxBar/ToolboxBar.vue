<script setup lang="ts">
import { computed, defineAsyncComponent, nextTick, onMounted, provide, ref, type StyleValue, toRefs } from 'vue'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'
import { getShowToolList } from '@/components/toolbox/ToolBoxHelper'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import type { App } from 'leafer-ui'
import type { ToolBoxBarInstance, ToolBoxBarProvide } from '@/components/ToolboxBar/types'
import { ToolBoxBarProvideCode } from '@/components/ToolboxBar/types'
import { useSettingUserStore } from '@/stores/SettingUserStore'

const loadToolBar = ref(true);
let toolListObj: any = null;

const sceneStore = useSceneStore();
const currentToolBoxStore = useCurrentToolBoxStore()
const { toolBarLocation } = toRefs(useCurrentToolBoxStore())
const { generalSetting } = toRefs(useSettingUserStore());
provide<ToolBoxBarProvide>(ToolBoxBarProvideCode, {
  location: toolBarLocation
});

function loadToolList(app?: App) {
  app = app || currentToolBoxStore.getLeaferApp();
  loadToolBar.value = true;
  setTimeout(() => {
    console.log('加载场景配置: ' + window.sceneCode);
    toolListObj = getShowToolList();
    currentToolBoxStore.registerAllToolBox(app, toolListObj.toolboxSortList);
    // 加载配置文件
    const sceneConfig = sceneStore.getSceneConfig(window.sceneCode)
    new ConfigReloadEvent(sceneConfig.disableAutoSaveTools, sceneConfig.saveMode !== 'auto')
      .dispatchEvent();
    loadToolBar.value = false;
  });
}

nextTick(() => {
  window.loadToolList = loadToolList;
});

const toolboxStyle = computed<StyleValue>(() => {
  const { toolBorder, toolBorderColor } = generalSetting.value;
  if (toolBorder === 'none') {
    return {};
  }
  return {
    border: `${toolBorderColor} solid 1px`,
  };
});
defineExpose<ToolBoxBarInstance>({
  loadToolList
});
onMounted(() => {
  console.log('工具条')
})
</script>
<template>
  <div v-if="!loadToolBar"
       id="toolboxContainerWrapper"
       class="u-fx">
    <div id="toolboxContainer"
         class="u-fx u-fac"
         :style="toolboxStyle">
      <template v-for="(key, index) in toolListObj.groupSort"
                :key="index">
        <component v-for="(item, i) in toolListObj.toolBoxObj[key]" :key="i"
                   :is="defineAsyncComponent(item.component)" />
        <a-divider v-if="index !== Object.keys(toolListObj.toolBoxObj).length - 1"
                   :direction="toolBarLocation === 'right' ? 'horizontal' : 'vertical'"
                   :margin="6" />
      </template>
    </div>
    <slot name="default"></slot>
  </div>
</template>
<style lang="less" scoped>
#toolboxContainer {
  position: absolute;
  margin-top: 10px;
  background: var(--main-background);
  padding: 6px;
  border-radius: 10px;
  gap: 4px;
  -webkit-app-region: no-drag;
}
</style>
