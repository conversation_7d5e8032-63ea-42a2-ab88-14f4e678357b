<script setup lang="ts">
const modelValue = defineModel<number>();
</script>

<template>
  <div class="stroke-width">
    <div class="stroke-width-select">
      <div :class="modelValue === 2 ? ['active'] : []"
           style="width: 8px; height: 8px;"
           @click="modelValue = 2"/>
      <div :class="modelValue === 4 ? ['active'] : []"
           style="width: 12px; height: 12px;"
           @click="modelValue = 4"/>
      <div :class="modelValue === 8 ? ['active'] : []"
           style="width: 16px; height: 16px;"
           @click=" modelValue = 8"/>
    </div>
    <t-input-number v-model:value="modelValue"
                    size="small"
                    theme="column"
                    style="text-align: center; width: 90px;"
                    :max="99"
                    :min="1"
                    @keyup.stop />
  </div>
</template>

<style scoped lang="less">
.stroke-width {
  display: flex;
  gap: 10px;
  .stroke-width-select {
    display: flex;
    align-items: center;
    gap: 10px;
    >div {
      cursor: pointer;
      border-radius: 50%;
      background: #747d8c;
      &.active {
        background: var(--text-color);
      }
    }
  }
}
</style>
