<script setup lang="ts">
import { computed } from 'vue'
import type { FillColor } from '@/stores/ToolConfigStore'
import LeaferColorPicker from '@/components/common/LeaferColorPicker/LeaferColorPicker.vue'

defineProps<{
  showTransparency?: boolean;
  hideLabel?: boolean;
}>();

const modalValue = defineModel<string>('modalValue');
const calcBackgroundColor = computed(() => {
  return (strokeSelectOption: any) => {
    if (strokeSelectOption.type !== 'solid') {
      const colors = strokeSelectOption.stops
        .map((i: {color: string}) => i.color)
        .join(',');
      return {background: `linear-gradient(70deg, ${colors})`};
    } else {
      return {backgroundColor: strokeSelectOption.color};
    }
  }
})

const emits = defineEmits<{
  (e: 'change', colorItem: FillColor | string): void;
}>()

function setColor(item: FillColor | null) {
  if (item) {
    modalValue.value = item.id;
    emits('change', item);
  } else {
    modalValue.value = '';
    emits('change', '');
  }
}

const strokeSelectOptions = defineModel<FillColor[]>('strokeSelectOptions');
function handleUpdateLeaferColor(val: FillColor, index: number) {
  strokeSelectOptions.value[index] = val;
}
</script>

<template>
  <div class="stroke">
    <div v-if="!hideLabel" style="font-size: 12px;">
      <slot name="title">颜色</slot>
    </div>
    <div class="stroke-color-select">
      <div v-if="showTransparency"
           class="stroke-color-select"
           :class="modalValue === '' ? ['active'] : ''"
           style="background:#ffffff; border: 2px solid transparent; position:relative;"
           @click="setColor(null)">
        <div style="position:absolute; left: -2px;">
          <div style="color: #ff0000"
               class="i-u-u-line" />
        </div>
      </div>
      <template  v-for="(item, index) in strokeSelectOptions"  :key="index">
        <div v-if="!item.edit"
             :class="item.id === modalValue ? ['active'] : ''"
             :style="{ ...calcBackgroundColor(item) }"
             @click="setColor(item)">
        </div>
        <div v-else
             class="color-picker-wrapper"
             :class="item.id === modalValue ? ['active'] : ''"
             @click="setColor(item)">
          <LeaferColorPicker :leafer-color="item"
                             :id="item.id"
                             @update:leaferColor="(val) => handleUpdateLeaferColor(val, index)">
          </LeaferColorPicker>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
.stroke {
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.vc-color-wrap.round) {
    width: 14px;
    height: 14px;
    border: none;
    position: relative;
    margin-right: 0;
    .edit-icon {
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .stroke-color-select {
    display: flex;
    gap: 6px;
    justify-content: flex-end;
    >div {
      display: flex;
      align-items: center;
      border-radius: 50%;
      width: 14px;
      height: 14px;
      cursor: pointer;
      &.active {
        width: 14px;
        height: 14px;
        border: 2px solid var(--tool-attr-select-color) !important;
        box-shadow: var(--tool-box-shadow);
      }
    }
    .color-picker-wrapper {
      position: relative;
      :deep(.vc-color-wrap.round) {
        width: 100%;
        height: 100%;
      }
      .edit {
        position: absolute;
        bottom: -4px;
        right: -4px;
        color: #ed4264;
        font-size: 10px;
      }
    }
  }
}
</style>
