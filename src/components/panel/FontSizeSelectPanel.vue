<script setup lang="ts">
const modelValue = defineModel<number>();
</script>

<template>
  <div class="stroke-width">
    <div class="stroke-width-select">
      <div :class="modelValue === 12 ? ['active'] : []"
           style="font-size: 12px"
           @click="modelValue = 12">
        小
      </div>
      <div :class="modelValue === 14 ? ['active'] : []"
           style="font-size: 14px"
           @click="modelValue = 14">
        中
      </div>
      <div :class="modelValue === 16 ? ['active'] : []"
           style="font-size: 16px"
           @click=" modelValue = 16">
        大
      </div>
    </div>
    <t-input-number size="small"
                    v-model:model-value="modelValue"
                    style="text-align: center; width: 90px;"
                    :max="99"
                    :min="1"
                    theme="column"
                    @keyup.stop />
  </div>
</template>

<style scoped lang="less">
.stroke-width {
  display: flex;
  gap: 10px;
  .stroke-width-select {
    display: flex;
    align-items: center;
    gap: 10px;
    >div {
      position: relative;
      cursor: pointer;
      border-radius: 50%;
      &:after {
        content: '';
        display: block;
        opacity: 0;
        position: absolute;
        bottom: -4px;
        left: -1px;
        border: 1px solid #c3c3c3;
        width: 100%;
        transition: all 230ms linear;
      }
      &:hover {
        &:after {
          opacity: 1;
        }
      }
      &.active {
        &:after {
          opacity: 1;
          border: 1px solid var(--text-color);
        }
      }
    }
  }
}
</style>
