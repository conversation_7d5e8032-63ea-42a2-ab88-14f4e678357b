<script setup lang="ts">
import {
  type FileTagLibrary,
  getRandomTagColor,
  tagColorSelectList,
  useFileManagerStore
} from '@/stores/FileManager/FileManagerStore'
import { h, onMounted, ref, toRefs, type VNode } from 'vue'
import type { SelectProps } from 'tdesign-vue-next';

type SelectOptionData =  SelectProps["options"];

const props = withDefaults(defineProps<{
  selectProps?: Partial<SelectProps>,
  autoCreate?: boolean,
}>(), {
  autoCreate: false,
  selectProps: {} as any
});
const { fileTagLibraryList, tagIdToFileTagLibrary } = toRefs(useFileManagerStore());
const { addOrUpdateFileTag } = useFileManagerStore();

const tagEditList = defineModel('modalValue', {
  default: []
});
const tagSelectData = ref<FileTagLibrary[]>([]);

const selectedCreateData = defineModel('createTags', {
  default: []
});


function handleImageTagChange(value: number[]) {
  console.log('handleImageTagChange', value);
  const existCreateTegIds = selectedCreateData.value
    .map(item => item.id);
  const createTegIds = value
    .filter(tagId => tagId < 0 && selectedCreateData.value.indexOf(tagId) === -1)
    .filter(tagId => !existCreateTegIds.includes(tagId));
  const createTagItems = tagSelectData.value.filter(item => createTegIds.includes(item.id));
  if (props.autoCreate) {
    createTagItems.map(item => {
      const result = item.label.match(/\$\{input\}/g);
      console.log(result)
      item.label = item.label.replace(/.*「(.*)」.*/, '$1');
      if (!item.color) {
        item.color = getRandomTagColor();
      }
      return item;
    }).map(fileTag => {
      const fileTagLibrary = addOrUpdateFileTag({
        ...fileTag,
        id: Date.now(),
      });
      value.splice(value.indexOf(fileTag.id), 1, fileTagLibrary.id);
    });
    tagEditList.value = value;
  } else {
    tagEditList.value = value;
    selectedCreateData.value.push(...createTagItems);
    selectedCreateData.value = selectedCreateData.value.filter(item => value.includes(item.id))
      .map(item => {
        const result = item.label.match(/\$\{input\}/g);
        console.log(result)
        item.label = item.label.replace(/.*「(.*)」.*/, '$1');
        if (!item.color) {
          item.color = getRandomTagColor();
        }
        return item;
      });
  }
  handleImageTagSearch('');
}
function handleImageTagSearch(input: string) {
  console.log('handleImageTagSearch', input)
  input = input.trim();
  let res: FileTagLibrary[] = [];
  const fileTagLibraries = JSON.parse(JSON.stringify(fileTagLibraryList.value)) as FileTagLibrary[];
  fileTagLibraries.push(...selectedCreateData.value);
  console.log('selectedCreateData', selectedCreateData.value)
  if (input) {
    res = fileTagLibraries.filter(item => item.label.toString().includes(input));
  } else {
    res = fileTagLibraries;
  }
  if (!res.length && input) {
    res.push({
      id: -Date.now(),
      new: true,
      label: `创建「${input}」标签`,
    });
  }

  tagSelectData.value = [];
  tagSelectData.value = res;
}
function handleTagColorSelect(fileTag: FileTagLibrary, color: string) {
  fileTag.color = color;
  console.log(fileTag, color)
  if (fileTag.id > 0) {
    addOrUpdateFileTag({
      ...fileTag,
      color,
    });
  }
}

function handleSelectDelete(tagId: number) {
  console.log('handleSelectDelete', tagId);
  const index = tagEditList.value.indexOf(tagId);
  if (index > -1) {
    tagEditList.value.splice(index, 1);
    if (tagId < 0) {
      const index = selectedCreateData.value.findIndex(item => item.id === tagId);
      if (index > -1) {
        selectedCreateData.value.splice(index, 1);
      }
    }
  }
  handleImageTagChange(tagEditList.value);
}

onMounted(() => {
  tagEditList.value = tagEditList.value.filter(tagId => tagIdToFileTagLibrary.value[tagId]);
  console.log(tagEditList.value)
  tagSelectData.value = JSON.parse(JSON.stringify(fileTagLibraryList.value));
  selectedCreateData.value.length = 0;
});
</script>

<template>
  <t-select v-model:value="tagEditList"
            style="width: 100%;"
            size="small"
            placeholder="添加标签"
            :onChange="(val) => handleImageTagChange(val as any[])"
            :onRemove="(val) => handleSelectDelete(val as any)"
            :onSearch="(val) => handleImageTagSearch(val as any)"
            creatable
            multiple
            filterable
            clearable>
    <template #value-display="{ value }">
      <view v-if="value">
        <t-tag v-for="(item, index) in value"
               :key="index"
               :color="item?.color || ''"
               size="small">
          {{ item?.label || '' }}
        </t-tag>
      </view>
    </template>
    <t-option v-for="(fileTag) in tagSelectData"
              :key="fileTag.id"
              :value="fileTag.id"
              :label="fileTag.label"
              :color="fileTag.color"
              style="width: 100%">
      <div class="u-fx u-f-between u-fac">
        <div :class="{
            'u-font-size-smail': selectProps.size && selectProps.size != 'small'
          }"
          :style="{color: fileTag.color}">
          {{ fileTag.label }}
        </div>
        <div @click.stop>
          <t-popup trigger="hover"
                   placement="top"
                   overlayClassName="u-web-popup">
            <template #content>
              <div class="u-fx u-fac tag-select-color">
                <div class="u-fx u-fc">
                  <t-tag :color="fileTag.color" class="tag-display" size="small">
                    {{ fileTag.label }}
                  </t-tag>
                </div>
                <div class="tag-select">
                  <t-tag v-for="(color) in tagColorSelectList"
                         size="small"
                         :key="color"
                         :color="color"
                         @click="handleTagColorSelect(fileTag, color)">
                  </t-tag>
                </div>
              </div>
            </template>
            <div v-if="!/.*「(.*)」.*/.test(fileTag.label)"
                 class="i-u-platte"
                 style="color: #6078ea" />
          </t-popup>
        </div>
      </div>
    </t-option>
  </t-select>
</template>
<style scoped lang="less">
// 标签颜色选择
.tag-select-color {
  background: var(--main-background-transparent);
  padding: 6px 5px;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  flex-direction: column;
  .tag-display {
    border-radius: 10px;
    text-align: center;
    min-width: 66px;
    display: flex;
    justify-content: center;
  }
  .tag-select {
    display: grid;
    justify-content: center;
    flex-wrap: wrap;
    grid-template-columns: repeat(4, 38px);
    gap: 5px;
    width: 200px;
    margin-top: 10px;
    >span {
      border-radius: 10px;
      cursor: pointer;
      text-align: center;
      transition: all 200ms linear;
      &:hover {
        border: 1px solid #0396ff;
      }
    }
  }
}
</style>
