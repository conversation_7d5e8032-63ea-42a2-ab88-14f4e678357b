import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { defineKey, Group, Line, PointerEvent, PropertyEvent, Text } from 'leafer-ui'
import type { IPointData } from '@leafer-ui/interface'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import { EditorEvent } from '@leafer-in/editor'


/**
 * 尺子工具
 */
export class RulerTool extends BaseDrawTool<Line> {
  constructor() {
    super(Line)
  }

  doInstall() {
    this.getEditor().on(EditorEvent.SELECT, (e: EditorEvent) => {
      if (e.value instanceof Array || !e.value || e.value.className !== LeaferConstant.CLASS_RULER_LINE) {
        this.getEditor().config.point = undefined;
        return;
      }
      this.getEditor().config.point = { width: 0, height: 0, cornerRadius: 0 };
    })
  }

  protected doUse() {
    this.modifyCursor('crosshair');
  }

  doMouseDown(e: PointerEvent) {
    const group = new Group({
      className: LeaferConstant.CODE_RULER,
      data: {
        code: LeaferConstant.CODE_RULER,
      },
    });
    console.log('group', group);
    const pointData = this._downPoint!.get()!;
    const line = new Line({
      ...pointData,
      startArrow: 'mark',
      endArrow: 'mark',
      toPoint: { x: 0, y: 0 },
      strokeWidth: 2,
      stroke: '#f03e3e',
      editable: true,
      className: LeaferConstant.CLASS_RULER_LINE,
      data: {
        code: LeaferConstant.CODE_RULER
      }
    });
    line.on_(PropertyEvent.CHANGE, (e: PropertyEvent) => {
      // this.calcTextPosition(line, text);
      setTimeout(() => {
        this.calcTextPosition(line, text);
      }, 20);
    });
    defineKey(line, 'editConfig', {
      get() { return {
        disableMultiple: ['skewable', 'rotateable', 'resizeable'],
        skewable: false,
      } }
    });
    const text = new Text({
      text: '0px',
      fill: '#fff',
      strokeCap: 'square',
      strokeWidth: 1,
      stroke: {
        type: 'linear',
        stops: ['#0083fe', '#0083fe']
      },
      fontFamily: 'Arial',
      fontSize: 12,
      padding: [0, 0],
      className: LeaferConstant.CLASS_RULER_TEXT,
      data: {
        code: LeaferConstant.CODE_RULER
      }
    });
    group.addMany(line, text);
    setTimeout(() => {
      this.calcTextPosition(line, text);
    });
    this.getDrawGroup().add(group);
    this.ui = line;
  }
  doMouseMove(e: PointerEvent) {
    const innerPoint = this.getAppTree().getInnerPoint(e);
    const downPoint = this._downPoint;
    this.ui!.toPoint = {x: innerPoint.x - downPoint.x , y: innerPoint.y - downPoint.y};
  }

  protected doDestroy() {
    this.modifyCursor('default');
  }

  private calcTextPosition(line: Line, text: Text) {
    // 计算终点坐标
    const lineEndX = line.x! + line.toPoint.x;
    const lineEndY = line.y! + line.toPoint.y;
    const startPoint: IPointData = {
      x: line.x!,
      y: line.y!
    }
    const endPoint: IPointData = {
      x: lineEndX,
      y: lineEndY
    }

    if (endPoint.x - startPoint.x < 0) {
      const temp: IPointData = {...endPoint};
      endPoint.x = startPoint.x;
      endPoint.y = startPoint.y;
      startPoint.x = temp.x;
      startPoint.y = temp.y;
    }

    // 计算 delta
    const deltaX = endPoint.x - startPoint.x;
    const deltaY = endPoint.y - startPoint.y!;

    // 计算角度（弧度）
    const angleInRadians = Math.atan2(deltaY, deltaX);
    const angleInDegrees = angleInRadians * (180 / Math.PI); // 转换为度数
    const angle = (Math.atan2(deltaY, deltaX) * (180 / Math.PI) + 360) % 360; // 转换为度数

    // 计算直线的中点
    const midX = (startPoint.x + endPoint.x) / 2;
    const midY = (startPoint.y + endPoint.y) / 2;

    // 获取文本的实际尺寸
    const textBounds = text.getBounds();
    const textWidth = textBounds.width;
    const textHeight = textBounds.height; // 使用实际高度

    // 计算偏移量
    const offsetX = (textWidth / 2) * Math.cos(angleInRadians);
    const offsetY = (textHeight / 2) * Math.sin(angleInRadians);

    const reverse = angle <= 275 && angle > 180;

    const distance = LeaferHelper.calculateDistance(startPoint, endPoint);
    // const textX = reverse ? midX + offsetX : midX - offsetX;
    // const textY = reverse ? midY + offsetY : midY - offsetY;
    // const textDistance = LeaferHelper.calculateDistance({ x: midX, y: midY }, { x: textX, y: textY });
    // console.log('textDistance',{ x: midX, y: midY }, { x: textX, y: textY }, textDistance)
    // 设置文本的位置，使其在斜线的中间
    text.set({
      x: reverse ? midX + offsetX : midX - offsetX, // 水平居中
      y: reverse ? midY + offsetY : midY - offsetY, // 垂直居中
      rotation: angleInDegrees, // 旋转角度
      scale: reverse ? -1 : 1,
      padding: [10, 0],
      text: `${Math.round(distance * 100) / 100} px`,
    });
  }
}
