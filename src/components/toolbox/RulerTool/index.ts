import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import RulerToolBox from './RulerToolBox.vue'
import { RulerTool } from './RulerTool'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'ruler',
  title: '尺子',
  sort: 5,
  group: 'draw',
  keyboard: {
    key: ''
  },
  toolIcon: 'i-u-ruler',
  config: false
}
export default {
  info: CONFIG,
  tool: new RulerTool(),
  component: async () => RulerToolBox
} as ToolboxItem;
