import DrawArrowToolBox from './DrawArrowToolBox.vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { DrawArrowTool } from './DrawArrowTool'
import DrawArrowSceneSetting from './DrawArrowSceneSetting.vue'

//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'arrow',
  title: '箭头',
  sort: 4,
  group: 'draw',
  keyboard: {
    key: '3'
  },
  toolIcon: 'i-u-arrow-right-up',
  config: true,
  sceneSetting: async () => DrawArrowSceneSetting,
}
export default {
  info: CONFIG,
  tool: new DrawArrowTool(),
  component: async () => DrawArrowToolBox,
} as ToolboxItem;
