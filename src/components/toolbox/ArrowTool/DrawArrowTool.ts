import { Line, PointerEvent } from 'leafer-ui'
import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { watch, type WatchStopHandle } from 'vue'
import type { IArrowConfig } from '@/stores/ToolConfigStore/store/ArrowToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import '@leafer-in/arrow'
import { SupperArrowLine } from '@/leaferApp/custom/SupperArrow/SupperArrow'
import type { ILineInputData } from '@leafer-ui/interface'

export class DrawArrowTool extends BaseDrawTool<Line> {

  private __configWatch?: WatchStopHandle;
  constructor() {
    super(Line);
  }

  doInstall() {
    this.installHoverStyle();
  }

  protected doUse() {
    this.modifyCursor('crosshair');
    const toolConfigStore = useToolConfigStore()
    this.__configWatch = watch(toolConfigStore.arrowConfig, (config) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        this.setUserConfig(tool, config);
      }
    })
  }

  doMouseDown(e: PointerEvent) {
    const toolConfigStore = useToolConfigStore();
    const arrowConfig = toolConfigStore.getArrowConfig;
    const pointData = this._downPoint!.get()!;
    const config: ILineInputData = {
      className: this.toolInfo.code,
      x: pointData.x,
      y: pointData.y,
      strokeCap: 'round',
      strokeJoin: 'round',
      editable: true,
      toPoint: {
        x: 0,
        y: 0
      },
    };
    let line: Line = null;
    console.log('arrowConfig', arrowConfig, arrowConfig.shape === 'arrow' && arrowConfig.subShape === 'fat')
    if (arrowConfig.shape === 'arrow' && arrowConfig.subShape === 'fat') {
      line = new SupperArrowLine(config);
    } else  {
      line = new Line(config)
    }
    this.setUserConfig(line, arrowConfig);
    return line;
  }

  private setUserConfig(line: Line, arrowConfig: IArrowConfig) {
    const stroke =  arrowConfig.strokeSelectOptions
      .find(item => arrowConfig.currentStroke === item.id)!;
    line.set({
      [line instanceof SupperArrowLine ? 'fill': 'stroke']: stroke,
      strokeWidth: arrowConfig.strokeWidth,
    });
    if (arrowConfig.shape === 'arrow') {
      console.log('箭头', line);
      line.set({
        startArrow: null,
        endArrow: 'angle',
      });
    } else if (arrowConfig.shape === 'line') {
      line.set({
        startArrow: null,
        endArrow: null,
      });
    } else if (arrowConfig.shape === 'doubleArrow') {
      line.set({
        startArrow: 'angle',
        endArrow: 'angle',
      });
    }
  }

  doMouseMove(e: PointerEvent) {
    const innerPoint = this.getAppTree().getInnerPoint(e);
    const downPoint = this._downPoint;
    this.ui!.toPoint = {x: innerPoint.x - downPoint.x , y: innerPoint.y - downPoint.y};
  }


  protected doDestroy() {
    this.modifyCursor('default');
    this.__configWatch && this.__configWatch();
  }

}
