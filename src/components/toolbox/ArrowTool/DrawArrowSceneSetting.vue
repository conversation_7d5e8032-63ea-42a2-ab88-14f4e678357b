<script setup lang="ts">
import { ref } from 'vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const sceneArrowConfig = toolConfigStore.getSceneArrowConfig(props.sceneCode);
</script>

<template>
  <div class="scene-setting-form" v-if="sceneArrowConfig">
    <t-form :model="modal" :label-width="80">
      <t-form-item label="大小">
        <t-input-number v-model:value="sceneArrowConfig.strokeWidth"
                        size="small"
                        theme="column" />
      </t-form-item>
      <t-form-item label="颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneArrowConfig.strokeSelectOptions"
                            v-model:model-value="sceneArrowConfig.currentStroke"
                            hide-label />
      </t-form-item>
      <t-form-item label="箭头样式">
        <t-radio-group v-model:value="sceneArrowConfig.shape"
                       variant="default-filled"
                       size="small"
                       type="button">
          <t-radio-button value="arrow">
            箭头
          </t-radio-button>
          <t-radio-button value="doubleArrow">双箭头</t-radio-button>
          <t-radio-button value="line">直线</t-radio-button>
        </t-radio-group>
        <template #help>
          <t-radio-group v-if="sceneArrowConfig.shape === 'arrow'"
                         v-model:value="sceneArrowConfig.subShape"
                         variant="default-filled"
                         size="small">
            <t-radio-button value="fat">
              <div class="u-fx u-fc u-fac" style="padding: 2px 0">
                <div class="i-u-fat-arrow w-4 h-4"></div>
              </div>
            </t-radio-button>
            <t-radio-button value="general">
              <div class="u-fx u-fc u-fac" style="padding: 2px 0">
                <div class="i-u-arrow-right-up w-4 h-4"></div>
              </div>
            </t-radio-button>
          </t-radio-group>
        </template>
      </t-form-item>

    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
