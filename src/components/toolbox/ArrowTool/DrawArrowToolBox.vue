<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import StrokeWidthSelectPanel from '@/components/panel/StrokeWidthSelectPanel.vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { toRefs } from 'vue'
import { CONFIG } from '@/components/toolbox/ArrowTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG;
const { arrowConfig } = toRefs(useToolConfigStore())
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #icon>
      <div v-if="arrowConfig.shape === 'arrow'"
           class="i-u-arrow-right-up" />
      <div v-else-if="arrowConfig.shape === 'line'"
           class="i-u-u-line" />
      <div v-else-if="arrowConfig.shape === 'doubleArrow'"
           class="i-u-double-arrow" />
    </template>
    <template #config>
      <div class="toolbox-config">
        <StrokeWidthSelectPanel v-model:model-value="arrowConfig.strokeWidth" />
        <StrokeSelectPanel v-model:stroke-select-options="arrowConfig.strokeSelectOptions"
                           v-model:model-value="arrowConfig.currentStroke" />
        <div class="u-fx corner-radius" style="justify-content: space-between">
          <div></div>
          <t-radio-group
            v-model:value="arrowConfig.shape"
            variant="default-filled"
            size="small">
            <t-radio-button value="arrow">箭头</t-radio-button>
            <t-radio-button value="doubleArrow">双箭头</t-radio-button>
            <t-radio-button value="line">直线</t-radio-button>
          </t-radio-group>
        </div>
        <div v-if="arrowConfig.shape === 'arrow'"
             class="u-fx corner-radius"
             style="justify-content: flex-start; padding-left: 20px;">
          <t-radio-group v-model:value="arrowConfig.subShape"
                         variant="default-filled"
                         size="small">
            <t-radio-button value="fat">
              <div class="u-fx u-fc u-fac" style="padding: 2px 0">
                <div class="i-u-fat-arrow"></div>
              </div>
            </t-radio-button>
            <t-radio-button value="general">
              <div class="u-fx u-fc u-fac" style="padding: 2px 0">
                <div class="i-u-arrow-right-up"></div>
              </div>
            </t-radio-button>
          </t-radio-group>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
</style>
