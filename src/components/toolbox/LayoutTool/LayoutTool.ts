import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import type { WatchStopHandle } from 'vue'
import { toRefs, watch } from 'vue'
import { AlignHelper, Point } from 'leafer-ui'
import type { IAlign } from '@leafer/interface'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import type { FillColor } from '@/stores/ToolConfigStore'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import type { IBeautifyConfig, ILayoutConfig } from '@/stores/ToolConfigStore/store/LayoutToolConfigStore'

export default class LayoutTool extends  BaseUseTool {
  private _paddingWatch?: WatchStopHandle;
  private _canvasWatch?: WatchStopHandle;
  private _beautifyWatch?: WatchStopHandle;
  constructor() {
    super();
  }

  doInstall() {
    /*window.addEventListener(MainImageLoadedEvent.MAIN_IMAGE_LOADED_EVENT, () => {
      // 应用一下全部配置
      this.applyAllConfig();
    });*/
  }

  protected doUse(): void {
    const { layoutConfig, beautifyConfig } = toRefs(useToolConfigStore())
    const {getLayoutConfig} =  useToolConfigStore();
    this._paddingWatch =  watch(getLayoutConfig.padding, () => {
      this.layoutPaddingWithBackground(layoutConfig.value);
    });

    /*this._canvasWatch = watch(layoutConfig.value.canvas, (canvas) => {
      console.log('canvas', canvas)
      LeaferHelper.setCanvasSize(this._app!, canvas);
    });*/

    this._beautifyWatch = watch(() => beautifyConfig.value, (newVal) => {
      this.beautifyShadow(newVal);
    }, {
      deep: true,
    })
  }

  private beautifyShadow(beautifyConfig: IBeautifyConfig) {
    if (beautifyConfig.shadowMode === 'NONE') {
      this.getMainImage().set({
        shadow: undefined
      });
    } else {
      this.getMainImage().set({
        shadow: beautifyConfig.shadowList
      })
    }
  }


  private layoutPaddingWithBackground(layout: ILayoutConfig) {
    if (layout.mode === 'auto') {
      this.layoutPadding(layout);
      if (this.isNoPadding(layout)) {
        this.setTransparentBackground();
      }
    }
  }

  public layoutPadding(layout: ILayoutConfig) {
    if (layout.mode === 'auto') {
      const mainImage = this.getMainImage();
      const { width, height } = mainImage.getBounds();
      const w = width + layout.padding.width;
      const h = height + layout.padding.height;
      LeaferHelper.setCanvasSize(this.getApp(), { width: w, height: h });
      this.layoutAlign(layout.align as any);
    }
  }


  private setTransparentBackground() {
    const {  beautifyConfig } = toRefs(useToolConfigStore());
    if (beautifyConfig.value.fillId) {
      beautifyConfig.value.fillId = '';
      this.setBackground(undefined);
    }
  }


  public layoutAlign(align: IAlign) {
    const screenshotGroup = this.getScreenshotGroup();
    const contentBounds = {...screenshotGroup.getBounds('box'), x: 0, y: 0};
    const containerBounds = {width: this.getApp().width, height: this.getApp().height, x: 0, y: 0};
    const point = new Point();
    // @ts-ignore
    AlignHelper.toPoint(align, contentBounds, containerBounds, point, false);
    const newPoint = this.getAppTree().getPagePoint(point);
    screenshotGroup.set({
      x: Math.round(newPoint.x),
      y: Math.round(newPoint.y),
    });
  }

  public layoutModeSwatch(mode: 'manual' | 'auto') {
    const { layoutConfig } = toRefs(useToolConfigStore());
    layoutConfig.value.mode = mode;
    const screenshotGroup = this.getScreenshotGroup();
    this.getAppTree().addAt(screenshotGroup, 0);
    const mainImage = this.getMainImage();
    mainImage.x = 0;
    mainImage.y = 0;
    layoutConfig.value.canvas.width = Math.max(mainImage.width!, this._app?.width!);
    layoutConfig.value.canvas.height = Math.max(mainImage.height!, this._app?.height!);
    if (mode === 'manual') {
      mainImage.hittable = true;
      mainImage.editable = true;
      this._app?.editor.select(mainImage)
    } else if (mode === 'auto') {
      LeaferHelper.exitEditor(this.getApp())
      mainImage.editable = false;
      mainImage.hittable = false;
      if (layoutConfig.value.align) {
        this.layoutAlign(layoutConfig.value.align as any);
      } else {
        layoutConfig.value.align = 'center';
        this.layoutAlign(layoutConfig.value.align as any);
      }
    }
  }

  public applyBackgroundCorner() {
    const toolConfigStore = useToolConfigStore();
    const beautifyConfig = toolConfigStore.beautifyConfig;
    if (this.isNoPadding(toolConfigStore.layoutConfig)) {
      // 如果没有填充那么就不需要填充背景色
      this.setTransparentBackground();
    }


    if (!toolConfigStore.beautifyConfig.fillId) {
      // 因为没有背景直接可以调整主图
      const mainImage = this.getMainImage();
      mainImage.set({
        cornerRadius: beautifyConfig.backgroundCorner
      });
    } else {
      const background = this.getApp().findId(LeaferConstant.ElementBackground);
      background.set({
        cornerRadius: beautifyConfig.backgroundCorner
      });
    }

    const contentWrapper = document.getElementById('contentWrapper');
    contentWrapper.style.borderRadius = `${beautifyConfig.backgroundCorner}px`
  }

  public applyImageCorner() {
    const toolConfigStore = useToolConfigStore();
    const layoutConfig = toolConfigStore.layoutConfig;
    const beautifyConfig = toolConfigStore.beautifyConfig;
    if (this.isNoPadding(layoutConfig)) {
      return;
    }

    const mainImage = this.getMainImage();

    mainImage.set({
      cornerRadius: beautifyConfig.imageCorner
    });
  }


  public applyBackground() {
    const toolConfigStore = useToolConfigStore();
    const fillId = toolConfigStore.beautifyConfig.fillId;
    if (fillId) {
      const fillColor = toolConfigStore.getLayoutBackgroundColorList()
        .find(item => item.id === fillId);
      this.setBackground(fillColor);
    } else {
      this.setBackground(undefined);
    }
  }
  public setBackground(colorItem?: FillColor | string) {
    const toolConfigStore = useToolConfigStore();
    if (colorItem && this.isNoPadding(toolConfigStore.layoutConfig)) {
      toolConfigStore.layoutConfig.padding.width = 20;
      toolConfigStore.layoutConfig.padding.height = 20;
    }

    const backgroundGroup = this.getApp().findId(LeaferConstant.ElementBackground)!;
    backgroundGroup.set({
      fill: colorItem
    });
  }

  private isNoPadding({ padding }: ILayoutConfig) {
    return padding.width === 0 && padding.height === 0
  }

  /**
   * 将所有配置都应用一下
   * @private
   */
  private applyAllConfig() {
    const { layoutConfig, beautifyConfig } = useToolConfigStore()
    this.applyBackground();
    this.applyImageCorner();
    this.applyBackgroundCorner();
    this.beautifyShadow(beautifyConfig);
    // 需要延迟一下保证其窗口位置已经计算完成
    setTimeout(() => {
      this.layoutPaddingWithBackground(layoutConfig);
    }, 100);
  }

  protected doReloadConfig() {
    console.log('美化::重新加载配置');
    const mainImage = this.getMainImage()
    if (!mainImage) {
      // 配置加载时候还没有将截图渲染完成, 这里就不进行应用配置了
      console.log('美化::重新加载配置::截图未渲染完成');
      return;
    }
    this.applyAllConfig();
  }


  protected doDestroy() {
    if (this._paddingWatch) {
      this._paddingWatch();
    }
    if (this._canvasWatch) {
      this._canvasWatch();
    }

    if (this._beautifyWatch) {
      this._beautifyWatch();
    }
  }
}
