<script setup lang="ts">
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import { computed, ref, toRefs } from 'vue'
import LayoutTool from '@/components/toolbox/LayoutTool/LayoutTool'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { CONFIG } from '@/components/toolbox/LayoutTool/index'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import type { FillColor } from '@/stores/ToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'


const toolInfo: ToolboxInfoItemConfig = CONFIG;

const { layoutConfig, beautifyConfig, getLayoutBackgroundColorList } = toRefs(useToolConfigStore());

const currentToolBoxStore = useCurrentToolBoxStore()

// function handleAutoLayoutAlign(val: boolean) {
//   const tool = currentToolBoxStore.getToolByCode<LayoutTool>(toolInfo.code)!;
//   if (val) {
//     tool.layoutModeSwatch('auto');
//   } else {
//     tool.layoutModeSwatch('manual');
//   }
// }
function handleAlignChange(val: string) {
  const tool = currentToolBoxStore.getToolByCode<LayoutTool>(toolInfo.code)!;
  tool.layoutAlign(val as any);
}

function handleBackground(colorItem: FillColor | string) {
  const tool = currentToolBoxStore.getToolByCode<LayoutTool>(toolInfo.code)!;
  tool.setBackground(colorItem);
}

function handleLayoutBackgroundCorner() {
  const tool = currentToolBoxStore.getToolByCode<LayoutTool>(toolInfo.code);
  tool.applyBackgroundCorner();
}
function handleLayoutImageCorner() {
  const tool = currentToolBoxStore.getToolByCode<LayoutTool>(toolInfo.code);
  tool.applyImageCorner();
}
const isPadding = computed(() => {
  return layoutConfig.value.padding.width !== 0 || layoutConfig.value.padding.height != 0;
});
const layoutType = ref<'layout' | 'beautify'>('beautify');
function handleModifyPadding(width: number, height: number) {
  layoutConfig.value.padding.width = width;
  layoutConfig.value.padding.height = height;
}
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="toolbox-config">
        <div class="u-fx" style="justify-content: space-between; margin-bottom: 4px;">
          <div style="margin-bottom: 10px;">
            <t-radio-group v-model:value="layoutType"
                           size="small"
                           variant="default-filled">
              <t-radio-button value="layout">布局</t-radio-button>
              <t-radio-button value="beautify">美化</t-radio-button>
            </t-radio-group>
          </div>
          <div>
<!--            <a-tooltip v-if="layoutConfig.mode === 'auto'"
                       content="保存为默认后下次截图将自动生效">
              <a-button size="mini">
                <template #icon>
                  <iconpark-icon name="save-one" />
                </template>
              </a-button>
            </a-tooltip>-->
          </div>
        </div>
        <div v-if="layoutType === 'layout'">
          <!--        <div class="action">
          <div></div>
          <div class="align-way">
            <a-tag size="small" color="green">
              自动
            </a-tag>
          </div>
        </div>-->
          <div v-if="layoutConfig.mode === 'auto'"
               class="toolbox-form-item"
               @keyup.stop>
            <div>填充</div>
            <div class="u-fx" style="flex-direction: column">
              <div class="item-width group2">
                <t-tooltip content="填充宽">
                  <t-input-number :min="0"
                                  style="width: 100%"
                                  v-model:value="layoutConfig.padding.width"
                                  size="small"
                                  theme="normal" />
                </t-tooltip>
                <t-tooltip content="填充高">
                  <t-input-number :min="0"
                                  style="width: 100%"
                                  v-model:value="layoutConfig.padding.height"
                                  size="small"
                                  theme="normal" />
                </t-tooltip>
              </div>
              <div style="margin-top: 4px;">
                <t-link v-if="isPadding"
                        theme="primary"
                        style="font-size: 12px"
                        @click="handleModifyPadding(0, 0)">
                  无填充
                </t-link>
              </div>
            </div>
          </div>
          <div v-if="layoutConfig.mode === 'auto'" class="toolbox-form-item">
            <div>对齐</div>
            <div class="layout-position">
              <div>
                <t-radio-group v-model:value="layoutConfig.align"
                               size="small"
                               class="u-web-radio-group"
                               style="width: 95px"
                               variant="default-filled"
                               @change="(val: any) => handleAlignChange(val)">
                  <div class="flex item-center justify-center">
                    <t-radio-button value="top-left">
                      <div class="i-u-alignment-left-top w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="top">
                      <div class="i-u-alignment-horizontal-top w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="top-right">
                      <div class="i-u-alignment-right-top w-4 h-4" />
                    </t-radio-button>
                  </div>
                  <div class="flex item-center justify-center">
                    <t-radio-button value="left">
                      <div class="i-u-alignment-left-center w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="center">
                      <div class="i-u-alignment-horizontal-center w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="right">
                      <div class="i-u-alignment-right-center w-4 h-4" />
                    </t-radio-button>
                  </div>
                  <div class="flex item-center justify-center">
                    <t-radio-button value="bottom-left">
                      <div class="i-u-alignment-left-bottom w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="bottom">
                      <div class="i-u-alignment-bottom-center w-4 h-4" />
                    </t-radio-button>
                    <t-radio-button value="bottom-right">
                      <div class="i-u-alignment-right-bottom w-4 h-4" />
                    </t-radio-button>
                  </div>
                </t-radio-group>
              </div>
            </div>
          </div>
          <div v-if="layoutConfig.mode === 'manual'"
               class="toolbox-form-item"
               @keyup.stop>
            <div>画布大小</div>
            <div class="item-width group2">
              <a-tooltip content="宽">
                <a-input-number :min="0"
                                v-model:value="layoutConfig.canvas.width" size="mini" />
              </a-tooltip>
              <a-tooltip content="高">
                <a-input-number :min="0" v-model:value="layoutConfig.canvas.height"
                                size="mini" />
              </a-tooltip>
            </div>
          </div>
        </div>
        <div v-if="layoutType === 'beautify'">
          <div style="margin-bottom: 10px;">
            <StrokeSelectPanel v-model:modal-value="beautifyConfig.fillId"
                               :stroke-select-options="getLayoutBackgroundColorList()"
                               @change="handleBackground"
                               show-transparency>
              <template #title>背景色</template>
            </StrokeSelectPanel>
          </div>
          <div class="toolbox-form-item">
            <div>{{ isPadding ? '背景圆角' : '圆角' }}</div>
            <div class="item-width">
              <t-slider v-model:value="beautifyConfig.backgroundCorner"
                        @change="handleLayoutBackgroundCorner"  />
            </div>
          </div>
          <div v-if="isPadding" class="toolbox-form-item">
            <div>截图圆角</div>
            <div class="item-width">
              <t-slider v-model:value="beautifyConfig.imageCorner"
                        @change="handleLayoutImageCorner"  />
            </div>
          </div>
          <div class="toolbox-form-item">
            <div>阴影</div>
            <div class="item-width">
              <t-radio-group v-model:value="beautifyConfig.shadowMode"
                             type="button"
                             variant="default-filled"
                             size="small">
                <t-radio-button value="NONE">无</t-radio-button>
                <t-popup :trigger="beautifyConfig.shadowMode === 'SHADOW' ? 'hover' : 'click'"
                         overlayClassName="u-web-popup"
                         placement="right" >
                  <t-radio-button value="SHADOW">阴影</t-radio-button>
                  <template #content>
                    <div class="toolbox-config shadow-trigger">
                      <div class="toolbox-form-item">
                        <div>阴影位置</div>
                        <div class="item-sub-width group2">
                          <a-tooltip content="X">
                            <a-input-number :min="0"
                                            v-model:value="beautifyConfig.shadowList[0].x"
                                            size="small"
                                            theme="normal"
                                            @keyup.stop />
                          </a-tooltip>
                          <a-tooltip content="Y">
                            <a-input-number :min="0"
                                            theme="normal"
                                            v-model:value="beautifyConfig.shadowList[0].y"
                                            size="mini"
                                            @keyup.stop />
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="toolbox-form-item">
                        <div>模糊大小</div>
                        <div class="item-sub-width">
                          <t-slider v-model:value="beautifyConfig.shadowList[0].blur"
                                    :min="0"
                                    :max="66"
                                    size="mini"
                                    @keyup.stop>
                          </t-slider>
                        </div>
                      </div>
                      <div class="toolbox-form-item">
                        <div>扩展大小</div>
                        <div class="item-sub-width">
                          <t-slider v-model:value="beautifyConfig.shadowList[0].spread"
                                    :min="0"
                                    :max="66"
                                    size="mini"
                                    @keyup.stop>
                          </t-slider>
                        </div>
                      </div>
<!--                      <div class="toolbox-form-item">
                        <div>阴影颜色</div>
                      </div>-->
                    </div>
                  </template>
                </t-popup>
              </t-radio-group>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
.toolbox-config {
  gap: 0;
}
.action {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.toolbox-form-item {
  >div:first-child {
    width: 60px;
  }
  .item-width {
    width: 150px;
  }
  .item-sub-width {
    width: 120px;
  }
  .group2 {
    display: grid;
    grid-template-columns: 49% 49%;
    gap: 4px ;
  }
}
.layout-position {
  :deep(.arco-radio-group-button) {
    width: 158px;
    flex-wrap: wrap;
    gap: 4px 6px;
    justify-content: center;
  }
}
:deep(.arco-btn-icon) {
  display: flex;
}
.align-way {
  margin-bottom: 5px;
  display: flex;
  justify-content: flex-end;
}
.shadow-trigger {
  background: var(--main-background);
  padding: 10px;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
}
</style>
