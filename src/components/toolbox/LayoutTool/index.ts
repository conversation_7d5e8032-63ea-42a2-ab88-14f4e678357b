import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import LayoutToolBox from './LayoutToolBox.vue'
import LayoutTool from './LayoutTool'
import LayoutSceneSetting from './LayoutSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'layout',
  title: '美化',
  sort: 2,
  group: 'default',
  environment: 'screenshot',
  keyboard: {
    ctrl: true,
    key: 'F1'
  },
  instant: false,
  toolIcon: 'i-u-magic-wand',
  config: true,
  triggerOptions: {
    popupTranslate: [70, 0]
  },
  sceneSetting: async () => LayoutSceneSetting
}


export default {
  info: CONFIG,
  tool: new LayoutTool(),
  component: async () => LayoutToolBox
} as ToolboxItem;
