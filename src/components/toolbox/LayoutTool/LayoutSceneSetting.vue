<script setup lang="ts">
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { computed, ref } from 'vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'

const props = defineProps<{
  sceneCode: string
}>();

const toolConfigStore = useToolConfigStore();

const modal = ref({});
const sceneLayoutConfig = toolConfigStore.getSceneLayoutConfig(props.sceneCode);
const sceneBeautifyConfig = toolConfigStore.getSceneBeautifyConfig(props.sceneCode);
const layoutType = ref<'layout' | 'beautify'>('beautify');
const isPadding = computed(() => {
  return sceneLayoutConfig.value.padding.width !== 0 || sceneLayoutConfig.value.padding.height != 0;
});
</script>

<template>
  <div class="toolbox-config">
    <div class="scene-setting-form" v-if="sceneLayoutConfig">
      <div style="margin-bottom: 10px; width: 250px;">
        <t-radio-group v-model:value="layoutType"
                       class="u-web-radio-group"
                       style="width: 100%;"
                       variant="default-filled"
                       type="button">
          <t-radio-button value="layout" class="w-1/2 flex justify-center">布局</t-radio-button>
          <t-radio-button value="beautify" class="w-1/2 flex justify-center">美化</t-radio-button>
        </t-radio-group>
      </div>
      <t-form v-if="layoutType === 'layout'"
              :model="modal"
              :label-width="50">
        <t-form-item  v-if="sceneLayoutConfig.mode === 'auto'"
                      label="填充">
          <div class="u-fx u-gap10">
            <t-tooltip content="填充宽">
              <t-input-number  v-model:value="sceneLayoutConfig.padding.width"
                              :min="0"
                              style="width: 100px;"
                              size="small"
                              theme="column"
                              @keyup.stop />
            </t-tooltip>
            <t-tooltip content="填充高">
              <t-input-number  v-model:value="sceneLayoutConfig.padding.height"
                              :min="0"
                              style="width: 100px;"
                              size="small"
                              theme="column"
                              @keyup.stop />
            </t-tooltip>
          </div>
        </t-form-item>
        <t-form-item v-if="sceneLayoutConfig.mode === 'auto'"
                     label="对齐">
          <div class="layout-position">
            <t-radio-group v-model:value="sceneLayoutConfig.align"
                           size="small"
                           class="u-web-radio-group"
                           style="width: 95px"
                           variant="default-filled">
              <div class="flex item-center justify-center">
                <t-radio-button value="top-left">
                  <div class="i-u-alignment-left-top w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="top">
                  <div class="i-u-alignment-horizontal-top w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="top-right">
                  <div class="i-u-alignment-right-top w-4 h-4" />
                </t-radio-button>
              </div>
              <div class="flex item-center justify-center">
                <t-radio-button value="left">
                  <div class="i-u-alignment-left-center w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="center">
                  <div class="i-u-alignment-horizontal-center w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="right">
                  <div class="i-u-alignment-right-center w-4 h-4" />
                </t-radio-button>
              </div>
              <div class="flex item-center justify-center">
                <t-radio-button value="bottom-left">
                  <div class="i-u-alignment-left-bottom w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="bottom">
                  <div class="i-u-alignment-bottom-center w-4 h-4" />
                </t-radio-button>
                <t-radio-button value="bottom-right">
                  <div class="i-u-alignment-right-bottom w-4 h-4" />
                </t-radio-button>
              </div>
            </t-radio-group>
          </div>
        </t-form-item>
      </t-form>
      <t-form v-else-if="layoutType === 'beautify'"
              :model="modal"
              :label-width="70"
              label-align="left">
        <t-form-item label="背景颜色">
          <StrokeSelectPanel :stroke-select-options="toolConfigStore.getLayoutBackgroundColorList()"
                              v-model:model-value="sceneBeautifyConfig.fillId"
                              hide-label
                             show-transparency />
        </t-form-item>
        <t-form-item :label="isPadding ? '背景圆角' : '圆角'">
          <t-slider v-model:value="sceneBeautifyConfig.backgroundCorner" />
        </t-form-item>
        <t-form-item v-if="isPadding"
                     label="截图圆角">
          <t-slider v-model:value="sceneBeautifyConfig.imageCorner" />
        </t-form-item>
      </t-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.toolbox-config {
  width: 300px;
}
.scene-setting-form {
  width: 70%;
}
.layout-position {
  :deep(.arco-radio-group-button) {
    width: 158px;
    flex-wrap: wrap;
    gap: 4px 6px;
    justify-content: center;
  }
}
</style>
