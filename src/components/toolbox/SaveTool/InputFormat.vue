<script setup lang="ts">
import { ref, watch } from 'vue'
import { NotifyPlugin } from 'tdesign-vue-next'
import { filePathFormat, stringVariable } from '@/utils/StrTemplateUtils'


withDefaults(defineProps<{
  fileSuffix?: 'png' | 'jpg'
}>(), {
  fileSuffix: 'png'
})

const modelValue = defineModel<string>('model-value');

const demoFormatResult = ref('');
watch(() => modelValue.value, (val) => {
  formatResult();
});
function formatResult() {
  demoFormatResult.value = filePathFormat(modelValue.value || '');
  console.log('demoFormatResult', demoFormatResult.value)
}

function copyText(text: string) {
  utools.copyText(text);
  NotifyPlugin.success({
    title: '提示',
    content: '复制成功',
    duration: 1000
  });
}
</script>

<template>
  <a-trigger trigger="hover"
             position="top"
             unmount-on-close>
    <template #content>
      <div class="input-format-trigger">
        <div class="u-font-size-smail">
          <div style="padding-top: 5px;">
            <div style="padding-top: 3px;">
              <span>结果: {{demoFormatResult}} </span>
            </div>
          </div>
          <div class="helper">
            <div style="padding: 5px 0;">常用格式</div>
            <div class="u-fx u-gap5" style="flex-direction: column">
              <div>
                <t-link size="small"
                        theme="primary"
                        @click="() => modelValue = '{timestamp}.{suffix}'">{timestamp}</t-link>: 1738507241769
              </div>
              <div>
                <t-link  @click="() => modelValue = '{Y}{M}{D}{h}{m}{s}{ss}'">
                  {Y}{M}{D}{h}{m}{s}{ss}
                </t-link>: 250101120059
              </div>
            </div>
            <div style="padding: 5px 0;">变量</div>
            <div class="u-fx u-gap5 u-fac" style="flex-wrap: wrap">
              <div v-for="variable in stringVariable" :key="variable.value"
                   :style="{width: variable.width ? `${variable.width}px` : '100px'}">
                <t-link
                  size="small"
                  theme="primary"
                  @click="() => copyText(variable.value)">{{variable.value}}</t-link> : {{variable.label}} : {{variable.demo()}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <t-input size="small" v-model:value="modelValue">
      <template #suffix>
        .{{ fileSuffix }}
      </template>
    </t-input>
  </a-trigger>
</template>

<style scoped lang="less">
.input-format-trigger {
  padding: 10px;
  width: 440px;
  background: var(--main-background);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  .helper {
    max-height: 190px;
    overflow-y: auto;
  }
}
</style>
