import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import SaveToolBox from './SaveToolBox.vue'
import SaveTool from '@/components/toolbox/SaveTool/SaveTool'
import CaptureChooseSaveTool from '@/components/toolbox/SaveTool/CaptureChooseSaveTool'
import SaveToolSceneSetting from './SaveToolSceneSetting.vue'

//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'save',
  title: '保存',
  sort: 100,
  group: 'action',
  instant: true,
  keyboard: {
    ctrl: true,
    key: 'S'
  },
  toolIcon: 'i-u-save-one',
  config: false,
  sceneSetting: async () => SaveToolSceneSetting,
}

export default {
  info: CONFIG,
  tool: window.environment === 'capture' ? new CaptureChooseSaveTool() :  new SaveTool(),
  component: async () => SaveToolBox,
} as ToolboxItem;
