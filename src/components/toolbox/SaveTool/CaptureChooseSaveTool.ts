import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import leaferConstant from '@/leaferApp/LeaferConstant'
import dayjs from 'dayjs'

const fileSuffix =  'png';
export default class CaptureChooseSaveTool extends BaseUseTool {

  constructor() {
    super()
  }

  protected doUse() {
    const editor = this.getApp().editor;
    const beforeSelected = editor.target;
    editor.cancel();



    const filename = dayjs().format("YYYY_MM_DD_HH_mm_ss." + fileSuffix);

    const savePath = utools.showSaveDialog({
      title: '请选择存储位置',
      defaultPath: window.path.join(utools.getPath('downloads'), filename)
    });

    if (!savePath) {
      return;
    }

    const chooseAreaRect = this.getAppTree().findId(leaferConstant.ElementChooseAreaRect)!;
    const chooseAreaRectBounds = chooseAreaRect.getBounds();
    const dx = chooseAreaRectBounds.x + 4;
    const dy = chooseAreaRectBounds.y + 4;
    const dw = chooseAreaRectBounds.width - 6;
    const dh = chooseAreaRectBounds.height - 6;
    const ratio = window.devicePixelRatio;
    this.getApp().export(fileSuffix, { blob: true,
      quality: 1,
      screenshot: {
        x: dx * ratio, y: dy * ratio,
        width: dw, height: dh,
      } }).then(async (res) => {
      editor.select(beforeSelected!);
      const arrayBuffer = await res.data.arrayBuffer();
      window.fs.writeFileSync(savePath, window.nodeBuffer.Buffer.from(arrayBuffer, 'utf-8'));
    });
  }
}
