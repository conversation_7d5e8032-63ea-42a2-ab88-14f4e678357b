<script setup lang="ts">
import InputFormat from './InputFormat.vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { SwitchPlusEnable } from '@xiaou66/u-web-ui'


const props = defineProps<{
  sceneCode: string
}>();
const { getSceneSaveConfig } = useToolConfigStore();
const saveConfig = getSceneSaveConfig(props.sceneCode);
</script>

<template>
  <t-form>
    <t-form-item label="文件后缀">
      <t-radio-group v-model:value="saveConfig.fileSuffix"
                     size="small"
                     type="button">
        <t-tooltip content="圆角保存为 PNG, 非圆角保存为 JPG" mini>
          <t-radio-button value="auto">自动</t-radio-button>
        </t-tooltip>
        <t-radio-button value="png">PNG</t-radio-button>
        <t-radio-button value="jpg">JPG</t-radio-button>
      </t-radio-group>
    </t-form-item>
    <t-form-item label="文件名称">
      <input-format v-model:model-value="saveConfig.filenameFormat"
                    :file-suffix="saveConfig.fileSuffix === 'auto' ? 'png' : saveConfig.fileSuffix"
      />
    </t-form-item>
    <t-form-item label="保存后关闭">
      <SwitchPlusEnable v-model:value="saveConfig.saveAfterClose" style="width: 42px" />
    </t-form-item>
  </t-form>
</template>

<style scoped lang="less">

</style>
