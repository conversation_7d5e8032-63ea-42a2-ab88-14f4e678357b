import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { filePathFormat } from '@/utils/StrTemplateUtils'

const fileSuffix =  'png';
export default class SaveTool extends BaseUseTool {
  constructor() {
    super();
  }
  protected doUse(): void {
    const editor = this.getApp().editor;
    LeaferHelper.autoAdjustWindowSize(this.getApp(), false);
    const { saveConfig } = useToolConfigStore();

    const { beautifyConfig: { backgroundCorner }} = useToolConfigStore();
    let fileSuffix = saveConfig.fileSuffix;
    if (fileSuffix === 'auto') {
      if (backgroundCorner > 0) {
        fileSuffix = 'png'
      } else {
        fileSuffix = 'jpg'
      }
    }

    const filename = filePathFormat(saveConfig.filenameFormat) + "." + fileSuffix;
    const savePath = utools.showSaveDialog({
      title: '请选择存储位置',
      defaultPath: window.path.join(saveConfig.filePath, filename)
    });

    saveConfig.filePath = window.path.dirname(savePath);

    LeaferHelper.autoAdjustWindowSize(this.getApp());



    if (!savePath) {
      return;
    }
    const beforeSelected = editor.target;
    ScreenshotCapacity.exportScreenshotBlob(this.getApp(), {}, fileSuffix)
      .then(async (res) => {
        const arrayBuffer = await res.arrayBuffer();
        window.fs.writeFileSync(savePath, window.nodeBuffer.Buffer.from(arrayBuffer as any, 'utf-8'));
        if (saveConfig.saveAfterClose) {
          ScreenshotCapacity.screenshotClose().then(() => {})
        } else {
          editor.select(beforeSelected!);
        }
      });
  }
}
