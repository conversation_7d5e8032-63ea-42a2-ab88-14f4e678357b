import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { RecallTool } from '@/components/toolbox/RecallTool/RecallTool'
import RecallToolBox from '@/components/toolbox/RecallTool/RecallToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'recall',
  title: '撤回',
  sort: 10,
  group: 'draw',
  keyboard: {
    key: 'Z',
    ctrl: true,
  },
  toolIcon: 'i-u-undo',
  config: false,
  instant: true,
}

export default {
  info: CONFIG,
  tool: new RecallTool(),
  component: async () => RecallToolBox,
} as ToolboxItem;
