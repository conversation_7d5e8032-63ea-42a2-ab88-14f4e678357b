<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import FontSizeSelectPanel from '@/components/panel/FontSizeSelectPanel.vue'
import { toRefs } from 'vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { CONFIG } from '@/components/toolbox/TextTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG
const {textConfig} = toRefs(useToolConfigStore());
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
     <div class="toolbox-config">
       <FontSizeSelectPanel v-model:model-value="textConfig.fontSize" />
       <StrokeSelectPanel v-model:model-value="textConfig.fillId"
                          v-model:stroke-select-options="textConfig.fillSelectOptions">
         <template #title>
           文字
         </template>
       </StrokeSelectPanel>
       <StrokeSelectPanel v-model:model-value="textConfig.fillBackgroundId"
                          v-model:stroke-select-options="textConfig.fillBackgroundSelectOptions"
                          show-transparency>
         <template #title>
           背景色
         </template>
       </StrokeSelectPanel>
     </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
</style>
