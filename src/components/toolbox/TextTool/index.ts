import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import TextTool from '@/components/toolbox/TextTool/TextTool'
import TextToolBox from '@/components/toolbox/TextTool/TextToolBox.vue'
import TextSceneSetting from './TextSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'text',
  title: '文本',
  sort: 8,
  keyboard: {
    ctrl: true,
    key: 'D'
  },
  group: 'draw',
  toolIcon: 'i-u-text',
  config: true,
  sceneSetting: async () => TextSceneSetting,
}
export default {
  info: CONFIG,
  tool: new TextTool(),
  component: async () => TextToolBox,
} as ToolboxItem;
