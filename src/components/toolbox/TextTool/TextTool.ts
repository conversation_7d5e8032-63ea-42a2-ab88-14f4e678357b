import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { Box, PointerEvent, Text } from 'leafer-ui'
import { watch, type WatchStopHandle } from 'vue'
import type { IUI } from '@leafer-ui/interface'
import type { ITextConfig } from '@/stores/ToolConfigStore/store/TextToolConfigStore'
import LeaferElement from '@/leaferApp/LeaferElement'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { EditorEvent } from '@leafer-in/editor'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { type Fn, useEventListener } from '@vueuse/core'
import { ElementLoadEvent } from '@/events/ElementLoadEvent'

export default class TextTool extends BaseUseTool {

  private __configWatch?: WatchStopHandle;

  private __cancelTime: number = 0;
  private __elementLoadEvent: Fn | null=  null;

  constructor() {
    super();
    super.register(PointerEvent.UP, this.mouseDown.bind(this));
  }

  elementLoad() {
    const textList: Text[] = this.getAppTree().find('.' + this.toolInfo.code)
      .filter(item => item.tag === 'Text') as Text[];
    textList.map(text => {
      LeaferElement.reloadTextBox(this.getApp(), text)
    });
  }
  doInstall() {
    this.__elementLoadEvent && this.__elementLoadEvent();
    this.__elementLoadEvent = useEventListener(window, ElementLoadEvent.ELEMENT_LOAD,
      this.elementLoad.bind(this));
    this.getEditor().on(EditorEvent.SELECT, (e: EditorEvent) => {
      if (LeaferHelper.formatIUIArray(e.value).length) {
        return;
      }

      if (LeaferHelper.formatIUIArray(e.oldValue).length) {
        this.__cancelTime = Date.now();
      }
    });
  }

  protected doUse() {
    this.modifyCursor('text');
    const toolConfigStore = useToolConfigStore()
    this.__configWatch = watch(toolConfigStore.getTextConfig, (userConfig) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      console.log('selectedCurrentTools', selectedCurrentTools)
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        this.setUserConfig(tool, userConfig);
      }
    });
  }

  private mouseDown(e: PointerEvent) {
    if (!this.isLeftClick(e)) {
      return;
    }
    if (this._app!.editor.target ||  Date.now() - this.__cancelTime < 1000) {
      this.__cancelTime = 0;
      return;
    }

    this.recordHistory();

    const localPoint = this.getMiddleGroup().getLocalPoint(e);
    const toolConfigStore = useToolConfigStore();
    const textConfig = toolConfigStore.getTextConfig;
    const box = LeaferElement.createTextBox(this.getApp(), this.getMiddleGroup(), {
      className: this.toolInfo.code,
      data: {
        code: this.toolInfo.code
      },
      x: localPoint.x - 10,
      y: localPoint.y - textConfig.fontSize - Math.floor(textConfig.fontSize * 0.5),
    }, {
      className: this.toolInfo.code,
      data: {
        code: this.toolInfo.code
      },
    });
    // const textIuI = box.findTag('Text')[0];
    // if (textIuI) {
    //   if (textIuI.get('text') === '请输入文字' || !textIuI.get('text')) {
    //     box.remove();
    //     return;
    //   }
    // }
    this.applyHistory();
    this.setUserConfig(box, textConfig);
  }

  private setUserConfig(tool: IUI, userConfig: ITextConfig) {
    let box: Box | null = null;
    let text: Text | null = null;
    if (tool instanceof Box) {
      box = tool;
      text = box.children[0] as Text;
    } else if (tool instanceof Text) {
      text = tool;
      box = text.parent as Box;
    }
    if (!box || !text) {
      return;
    }

    const fillBackground = userConfig.fillBackgroundSelectOptions
      .find(item => userConfig.fillBackgroundId === item.id);
    const fill = userConfig.fillSelectOptions.find(item =>  userConfig.fillId === item.id);
    box.set({
      fill: fillBackground,
    });
    text.set({
      fill: fill,
      fontSize: userConfig.fontSize
    });
  }

  protected doDestroy() {
    this.modifyCursor('default');
    this.__configWatch && this.__configWatch();
  }
}
