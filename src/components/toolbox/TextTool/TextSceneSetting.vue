<script setup lang="ts">
import { ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const sceneTextConfig = toolConfigStore.getSceneTextConfig(props.sceneCode);
</script>

<template>
  <div v-if="sceneTextConfig"
       class="scene-setting-form">
    <t-form :model="modal" :label-width="80">
      <t-form-item label="字体大小">
        <t-input-number v-model:value="sceneTextConfig.fontSize"
                        size="small"
                        theme="column" />
      </t-form-item>
      <t-form-item label="字体颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneTextConfig.fillSelectOptions"
                            v-model:model-value="sceneTextConfig.fillId"
                            hide-label />
      </t-form-item>
      <t-form-item label="背景色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneTextConfig.fillBackgroundSelectOptions"
                            v-model:model-value="sceneTextConfig.fillBackgroundId"
                            hide-label />
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
