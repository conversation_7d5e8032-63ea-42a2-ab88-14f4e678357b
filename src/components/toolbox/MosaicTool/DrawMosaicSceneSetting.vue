<script setup lang="ts">
import { ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const scenePencilConfig = toolConfigStore.getSceneMosaicConfig(props.sceneCode);
</script>

<template>
  <div v-if="scenePencilConfig"
       class="scene-setting-form">
    <t-form :model="modal" :label-width="80">
      <t-form-item label="像素块">
        <div class="mosaic-size-select">
          <div :class="scenePencilConfig.mosaicSize === 10 ? ['active'] : []"
               style="width: 8px; height: 8px;"
               @click="() => scenePencilConfig.mosaicSize = 10"/>
          <div :class="scenePencilConfig.mosaicSize === 18 ? ['active'] : []"
               style="width: 12px; height: 12px;"
               @click="() => scenePencilConfig.mosaicSize = 18"/>
          <div :class="scenePencilConfig.mosaicSize === 25 ? ['active'] : []"
               style="width: 16px; height: 16px;"
               @click="() => scenePencilConfig.mosaicSize = 25"/>
        </div>
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
.mosaic-size-select {
  display: flex;
  align-items: center;
  gap: 10px;
  >div {
    cursor: pointer;
    border-radius: 50%;
    background: #c3c3c3;
    &.active {
      background: #000000;
    }
  }
}
</style>
