import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { Bounds, define<PERSON>ey, Line, PointerEvent } from 'leafer-ui'
import Mosaic from '@/leaferApp/custom/mosaic/Mosaic'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { Editor } from '@leafer-in/editor'
import { watch, type WatchStopHandle } from 'vue'

export class DrawMosaicTool extends BaseDrawTool<Mosaic> {
  private bounds?: Bounds;
  private mosaicEditor?: Editor;
  private __configWatch?: WatchStopHandle;

  constructor() {
    super(Line);
  }

  protected doUse() {
    this.destroyEditor();
    const toolConfigStore = useToolConfigStore();
    this.__configWatch = watch(toolConfigStore.getMosaicConfig, (userConfig) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        tool.set({
          mosaicSize: userConfig.mosaicSize,
        });
      }
    })
  }


  doMouseDown(e: PointerEvent) {
    const pointData = this._downPoint?.get()!;
    const toolConfigStore = useToolConfigStore();
    const mosaicConfig = toolConfigStore.getMosaicConfig;
    const mosaic = new Mosaic({
      className: this.toolInfo.code,
      ...pointData,
      width: 4,
      height: 4,
      editable: true,
      fill: '#2ecc71',
      mosaicSize: mosaicConfig.mosaicSize,
    });
    defineKey(mosaic, 'editConfig', {
      get() { return { rotateable: false } }
    });

    this.bounds = new Bounds(pointData.x, pointData.y);
    const group = this.getAppTree()!.findId('mosaic')!;
    this.ui = mosaic;
    group.add(mosaic);

    this.drawMosaicSelect()

  }

  /**
   * 绘画时选择马赛克
   * @private
   */
  private drawMosaicSelect() {
    if (!this.mosaicEditor) {
      this.mosaicEditor = new Editor();
      this.getApp().sky.add(this.mosaicEditor);
      this.mosaicEditor.hittable = false;
    }
    this.mosaicEditor.target = this.ui;
  }

  doMouseMove(e: PointerEvent) {
    const innerPoint = this.getAppTree().getInnerPoint(e);
    this.bounds?.addPoint(innerPoint);
    const ui = this.ui!;
    this.getAppTree().forceUpdate();
    ui.set(this.bounds!.unsign().get())
  }

  doMouseUp(e: PointerEvent) {
    this.destroyEditor();
  }

  destroyEditor() {
    if (this.mosaicEditor) {
      this.mosaicEditor.cancel();
    }
  }

  protected doDestroy() {
    this.destroyEditor();
    if (this.__configWatch) {
      this.__configWatch();
      this.__configWatch = undefined;
    }
  }
}
