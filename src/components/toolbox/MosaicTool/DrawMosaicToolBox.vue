<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import { toRefs } from 'vue'
import { CONFIG } from '@/components/toolbox/MosaicTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG;

const {mosaicConfig} = toRefs(useToolConfigStore());

</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="toolbox-config">
        <div class="toolbox-form-item">
          <div>像素块</div>
          <div class="mosaic-size-select">
            <div :class="mosaicConfig.mosaicSize === 10 ? ['active'] : []"
                 style="width: 8px; height: 8px;"
                 @click="() => mosaicConfig.mosaicSize = 10"/>
            <div :class="mosaicConfig.mosaicSize === 18 ? ['active'] : []"
                 style="width: 12px; height: 12px;"
                 @click="() => mosaicConfig.mosaicSize = 18"/>
            <div :class="mosaicConfig.mosaicSize === 25 ? ['active'] : []"
                 style="width: 16px; height: 16px;"
                 @click="() => mosaicConfig.mosaicSize = 25"/>
          </div>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
.mosaic-size-select {
  display: flex;
  align-items: center;
  gap: 10px;
  >div {
    cursor: pointer;
    border-radius: 50%;
    background: #c3c3c3;
    &.active {
      background: #000000;
    }
  }
}
</style>
