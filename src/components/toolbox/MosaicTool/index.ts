import DrawMosaicToolBox from './DrawMosaicToolBox.vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { DrawMosaicTool } from '@/components/toolbox/MosaicTool/DrawMosaicTool'
import DrawMosaicSceneSetting from './DrawMosaicSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'mosaic',
  title: '马赛克',
  sort: 7,
  group: 'draw',
  keyboard: {
    ctrl: true,
    key: 'E'
  },
  toolIcon: 'i-u-mosaic',
  config: true,
  sceneSetting: async () => DrawMosaicSceneSetting,
}


export default {
  info: CONFIG,
  tool: new DrawMosaicTool(),
  component: async () => DrawMosaicToolBox,
} as ToolboxItem;
