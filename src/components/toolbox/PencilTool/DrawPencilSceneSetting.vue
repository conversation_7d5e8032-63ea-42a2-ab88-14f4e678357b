<script setup lang="ts">
import { ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const scenePencilConfig = toolConfigStore.getScenePencilConfig(props.sceneCode);

</script>

<template>
  <div v-if="scenePencilConfig" class="scene-setting-form">
    <t-form :model="modal" :label-width="80">
      <t-form-item label="大小">
        <t-input-number v-model:value="scenePencilConfig.strokeWidth"
                        size="small"
                        theme="column" />
      </t-form-item>
      <t-form-item label="颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="scenePencilConfig.strokeSelectOptions"
                            v-model:model-value="scenePencilConfig.currentStroke"
                            hide-label />
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
