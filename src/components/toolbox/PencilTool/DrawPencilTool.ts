import { Line, PointerEvent } from 'leafer-ui'
import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { watch, type WatchStopHandle } from 'vue'
import type { IPencilConfig } from '@/stores/ToolConfigStore/store/PencilToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

export default class DrawPencilTool extends BaseDrawTool<Line> {

  private __configWatch?: WatchStopHandle;

  constructor() {
    super(Line);
  }

  doInstall() {
    this.installHoverStyle();
  }

  protected doUse() {
    this.modifyCursor('crosshair');
    const toolConfigStore = useToolConfigStore();
    this.__configWatch = watch(toolConfigStore.pencilConfig, (userConfig) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        this.setUserConfig(tool, userConfig);
      }
    })
  }

  doMouseDown(e: PointerEvent) {
    const pointData = this._downPoint?.get()!;
    const toolConfigStore = useToolConfigStore();
    const line = new Line({
      className: this.toolInfo.code,
      points: [pointData.x, pointData.y],
      curve: true,
      editable: true,
    });
    this.setUserConfig(line, toolConfigStore.getPencilConfig);
    return line;
  }

  private setUserConfig(tool: Line, userConfig: IPencilConfig) {
    const stroke =  userConfig.strokeSelectOptions
      .find(item => userConfig.currentStroke === item.id)!;
    tool.set({
      stroke,
      strokeWidth: userConfig.strokeWidth,
    })
  }

  doMouseMove(e: PointerEvent) {
    const innerPoint = this.getAppTree().getInnerPoint(e)
    const ui = this.ui!;
    const points = JSON.parse(JSON.stringify(ui.points))
    points.push(innerPoint.x, innerPoint.y)
    ui.points = points;
  }

  protected selectDefaultUI() {
  }

  protected doDestroy() {
    this.modifyCursor('default');
    this.__configWatch && this.__configWatch();
  }
}
