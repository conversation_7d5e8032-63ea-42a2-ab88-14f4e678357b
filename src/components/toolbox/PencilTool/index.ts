import DrawPencilToolBox from './DrawPencilToolBox.vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import DrawPencilTool from '@/components/toolbox/PencilTool/DrawPencilTool'
import DrawPencilSceneSetting from './DrawPencilSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'pencil',
  title: '画笔',
  sort: 6,
  group: 'draw',
  keyboard: {
    key: '5'
  },
  toolIcon: 'i-u-pencil',
  config: true,
  sceneSetting: async () => DrawPencilSceneSetting,
}


export default {
  info: CONFIG,
  tool: new DrawPencilTool(),
  component: async () => DrawPencilToolBox,
} as ToolboxItem;
