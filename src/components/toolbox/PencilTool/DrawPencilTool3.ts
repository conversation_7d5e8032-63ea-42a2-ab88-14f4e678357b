import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { Line, Pen, PointerEvent } from 'leafer-ui'

export class DrawPencilTool3 extends BaseUseTool {

  private _down: boolean = false;
  private ui?: Pen;

  constructor() {
    super();
    super.register(PointerEvent.DOWN, this.mouseDown.bind(this));
    super.register(PointerEvent.MOVE, this.mouseMove.bind(this));
    super.register(PointerEvent.UP, this.mouseUp.bind(this));
  }

  protected doUse() {
    this._app!.editor.visible = false;
  }


  mouseDown(e: PointerEvent) {
    this._down = true;
    const word = {x: e.x, y: e.y};
    this._app!.tree.worldToInner(word);
    const pen = new Pen({
      ...word,
      strokeWidth: 5,
      strokeCap: 'round',
      strokeJoin: 'round',
      stroke: 'rgb(50,205,121)',
      editable: true,
    });
    pen.setStyle({ fill: '#FF4B4B', width: 2, stroke: '#fffccc', strokeWidth: 2 });
    pen.drawPoints([word.x, word.y]).paint();
    console.log(pen)
    this.ui = pen;
    this._app?.tree.add(this.ui);
  }
  mouseMove(e: PointerEvent) {
    if (!this._down) {
      return;
    }
    const word = {x: e.x, y: e.y};
    this._app!.tree.worldToInner(word);
    this.ui!.drawPoints([word.x, word.y]).paint();
  }
  mouseUp(e: PointerEvent) {
    this._down = false;
    const word = {x: e.x, y: e.y};
    this._app!.tree.worldToInner(word);
  }


  protected doDestroy() {
    this._app!.editor.visible = true;
  }
}
