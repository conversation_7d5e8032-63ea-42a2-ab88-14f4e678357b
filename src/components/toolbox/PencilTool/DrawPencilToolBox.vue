<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import StrokeWidthSelectPanel from '@/components/panel/StrokeWidthSelectPanel.vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { toRefs } from 'vue'
import { CONFIG } from '@/components/toolbox/PencilTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG
const { pencilConfig } = toRefs(useToolConfigStore())
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="toolbox-config">
        <StrokeWidthSelectPanel v-model:model-value="pencilConfig.strokeWidth" />
        <StrokeSelectPanel v-model:stroke-select-options="pencilConfig.strokeSelectOptions"
                           v-model:model-value="pencilConfig.currentStroke" />
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
</style>
