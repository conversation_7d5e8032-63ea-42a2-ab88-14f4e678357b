import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { createScreenShot } from '@/utils/screenshot'
import screenshotApi from '@/core/sdk/screenshotApi'
import { Editor } from '@leafer-in/editor'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'


export default class DingTool extends BaseUseTool {
  constructor() {
    super()
  }

  protected doUse() {
    for (const child of this.getApp().sky.children) {
      if (child instanceof Editor) {
        // 刷新状态
        child.cancel();
      }
    }
    setTimeout(() => {
      ScreenshotCapacity.exportScreenshotDataUrl(this.getApp())
        .then(async (base64) => {
          const winObj = await createScreenShot();
          screenshotApi.sendBase64Screenshot(winObj.webContentsId, base64);
          window.winHelper.getCurrentWindow().close();
        });
    });
  }
}
