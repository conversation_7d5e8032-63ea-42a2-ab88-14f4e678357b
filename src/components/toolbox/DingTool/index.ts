import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import DingTool from './DingTool'
import DingToolBox from './DingToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'pin',
  title: '钉住',
  sort: 2,
  group: 'tool',
  keyboard: {
    key: ''
  },
  environment: 'capture',
  instant: false,
  toolIcon: 'i-u-pin',
  config: false,
  hideScene: true,
}

export default {
  info: CONFIG,
  tool: new DingTool(),
  component: async () => DingToolBox
} as ToolboxItem;
