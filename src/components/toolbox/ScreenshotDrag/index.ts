import ScreenshotDragTool from "./ScreenshotDragTool";
import ScreenshotDragToolBox from "./ScreenshotDragToolBox.vue";
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import CaptureChooseAreaTool from '@/components/toolbox/ScreenshotDrag/CaptureChooseAreaTool'

//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'screenshotDrag',
  title: '拖拽',
  sort: 1,
  group: 'default',
  keyboard: {
    key: '空格',
    mode: 'switchover',
  },
  toolIcon: 'i-u-one-one',
  config: false
}

export default {
  info: CONFIG,
  tool: window.environment === 'capture' ? new CaptureChooseAreaTool() : new ScreenshotDragTool(),
  component: async () => ScreenshotDragToolBox
} as ToolboxItem;
