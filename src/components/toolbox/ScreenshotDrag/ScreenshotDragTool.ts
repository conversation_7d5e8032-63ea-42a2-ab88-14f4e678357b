import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import Leafer<PERSON>elper from '@/leaferApp/LeaferHelper'
import { useWindowDrag, type WindowDrag } from '@/hooks/useDraggable'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import JsUtil from '@/utils/jsUtil'

export default class ScreenshotDragTool extends BaseUseTool {
  private __windowDrag: WindowDrag;
  private __time: number = 0;

  constructor() {
    super();
    this.__windowDrag = useWindowDrag({
      allowDrag: (e: MouseEvent) => {
        if (JsUtil.findFatherElementId(e.target as any, 'screenShotContextMenu')) {
          return false;
        }
        const contentWrapper = document.getElementById('contentWrapper')
        const mode = contentWrapper.getAttribute('data-mode');
        if (mode === 'mini') {
          return false;
        }
        return true;
      }
    });
  }

  protected doUse(): void {
    const { dragMode } = useSettingUserStore()
    if (dragMode !== 'full') {
        if (dragMode === 'compatibility') {
          const screenshotDrag = document.getElementById('screenshotDrag');
          screenshotDrag.style.display = 'block';
        } else {
          const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
          // @ts-ignore
          screenshotContainer.style['-webkit-app-region'] = 'drag';
        }
    } else {
      this.__windowDrag.start();
    }
    this.getEditor().visible = false;
  }

  protected doDestroy() {
    const screenshotDrag = document.getElementById('screenshotDrag');
    screenshotDrag.style.display = 'none';
    const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
    // @ts-ignore
    if (screenshotContainer.style['-webkit-app-region'] === 'drag') {
      // @ts-ignore
      screenshotContainer.style['-webkit-app-region'] = 'no-drag';
    } else {
      this.__windowDrag.stop();
      LeaferHelper.autoAdjustWindowSize(this.getApp());
    }
    this.getEditor().visible = true;
  }
}
