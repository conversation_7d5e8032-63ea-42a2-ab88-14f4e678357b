import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import { PointerEvent } from 'leafer-ui'
import { adjustToolBarPosition } from '@/leaferApp/custom/CustomEditTool/CustomEditTool'
import type { IPointData } from '@leafer-ui/interface'

export default class CaptureChooseAreaTool extends BaseUseTool {
  private downPoint: IPointData;

  private __crossTimer: NodeJS.Timer | null;

  private __fist: boolean = false;

  public constructor() {
    super()
  }
  bodyMouseMove(event: MouseEvent){
    console.log('1111', event)
    const x_div = document.getElementById("x_div");
    const y_div = document.getElementById("y_div");
    x_div.style.top = event.clientY + "px";
    y_div.style.left = event.clientX + "px";
  }
  protected doUse(): void {
    this.initSelect();
    this.getEditor().hittable = true;
    this.getEditor().cancel();
    const chooseAreaRect = this.getChooseAreaRect();
    chooseAreaRect.set({
      hittable: true
    });
    this.getEditor().select(this.getChooseAreaRect());

    if (!this.alreadyChooseArea()) {
      window.addEventListener('mousemove', this.bodyMouseMove);
      const coordinateElement = document.getElementById('coordinate');
      this.__crossTimer = setInterval(() => {
        const displayNearestPoint = utools.getDisplayNearestPoint(utools.getCursorScreenPoint());
        const currentWindowDisplay = utools.getDisplayNearestPoint(window.winHelper.getCurrentWindow().getBounds())
        coordinateElement.style.display  = currentWindowDisplay.id === displayNearestPoint.id ? 'block' : 'none'
      }, 200);
    }
  }



  private getChooseAreaRect() {
    return this.getApp().findId(LeaferConstant.ElementChooseAreaRect)!;
  }

  private mouseDown(e: PointerEvent) {
    this.__fist = !this.alreadyChooseArea();
    if (this.alreadyChooseArea()) {
      return;
    }
    this.getEditor().hittable = false;
    const innerPoint = this.getAppTree().getInnerPoint(e);
    const chooseAreaRect = this.getChooseAreaRect();
    chooseAreaRect.x = innerPoint.x;
    chooseAreaRect.y = innerPoint.y;
    this.downPoint = innerPoint;
  }

  private mouseMove(e: PointerEvent) {
    if (this.downPoint == null) {
      return;
    }
    const chooseAreaRect = this.getChooseAreaRect()
    const width = e.x - this.downPoint.x;
    const height = e.y - this.downPoint.y;
    chooseAreaRect.set({
      scaleX: width > 0 ? 1 : -1,
      scaleY: height > 0 ? 1 : -1,
      width: Math.abs(width),
      height: Math.abs(height),
    });
  }

  private mouseUp(e: PointerEvent) {
    if (this.downPoint) {
      const toolboxContainerWrapper = document.getElementById('toolboxContainerWrapper');
      if (toolboxContainerWrapper) {
        toolboxContainerWrapper.style.opacity = '0';
        toolboxContainerWrapper.style.display = 'block';
        setTimeout(() => {
          adjustToolBarPosition(this.getChooseAreaRect().getBounds(), this.getApp());
          toolboxContainerWrapper.style.opacity = '1';
          if (this.__crossTimer) {
            window.removeEventListener('mousemove', this.bodyMouseMove);
            document.getElementById('coordinate')!.remove();
            clearInterval(this.__crossTimer as any);
            this.__crossTimer = null;
          }
        })
      }
    }


    if (this.events.length) {
      this.getApp().off_(this.events);
    }

    this.downPoint = null;
    this.getEditor().hittable = true;
    const otherWindowsIds = window.winHelper.getData<number[]>('otherWindowsIds')
    if (otherWindowsIds && otherWindowsIds.length) {
      for (const otherWindowsId of otherWindowsIds) {
        window.winHelper.getBrowserWindow(otherWindowsId)?.close();
      }
      window.winHelper.setData('otherWindowsIds', []);
    }

    // if (this.__fist) {
    //   this.recordHistory();
    //   this.applyHistory();
    // }
  }

  private alreadyChooseArea() {
    return this.getChooseAreaRect().x !== 0;
  }

  initSelect() {
    if (this.alreadyChooseArea()) {
      return;
    }
    this.batchAddEvent([
      this.getApp().on_(PointerEvent.DOWN, this.mouseDown, this),
      this.getApp().on_(PointerEvent.MOVE, this.mouseMove, this),
      this.getApp().on_(PointerEvent.UP, this.mouseUp, this)
    ])
  }

  protected doDestroy() {
    const chooseAreaRect = this.getChooseAreaRect();
    chooseAreaRect.set({
      hittable: false
    })
    this.getEditor().hittable = true;
    this.getEditor().cancel();
  }
}
