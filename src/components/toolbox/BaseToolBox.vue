<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox/index'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { computed, inject, watch } from 'vue'
import { KEYBOARD } from '@/core/constants/StrConstants'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import { type ToolBoxBarProvide, ToolBoxBarProvideCode } from '@/components/ToolboxBar/types'
import { useSettingUserStore } from '@/stores/SettingUserStore'

const props = defineProps<ToolboxInfoItemConfig>();

const currentToolBoxStore = useCurrentToolBoxStore()
const { generalSetting } = useSettingUserStore()

function handleSwitchToolBox() {
  currentToolBoxStore.setCurrentToolBoxByCode(props.code);
}

const emits = defineEmits<{
  (e: 'openConfig'): void;
  (e: 'closeConfig'): void;
}>();

const popupVisible = computed(() => currentToolBoxStore.toolBarVisible
  && currentToolBoxStore.getCurrentToolBoxCode === props.code);
watch(() => popupVisible.value, (newValue) => {
  if (newValue) {
    emits('openConfig');
  } else {
    emits('closeConfig');
  }
});

const keyboardConfigStore = useKeyboardConfigStore();
const keyboardConfig = computed(() => {
  const keyboardConfig = keyboardConfigStore.toolBoxKeyboardConfig[props.code];
  if (keyboardConfig) {
    return keyboardConfig;
  }
  return props.keyboard;
});
function handleMateChange(val: boolean) {
  const toolBoxKeyboardConfig = keyboardConfigStore.addToolBoxKeyboardConfig({
    code: props.code,
    ctrl: val,
  });
  window.keyboardManager.addKeyboardConfig(props.code, toolBoxKeyboardConfig, {
    downHandle: () => {
      currentToolBoxStore.setCurrentToolBoxByCode(props.code, true);
    },
    upHandle: () => {
      currentToolBoxStore.rollbackToolBox();
    }
  });
}
function handleModeChange(val: 'hold' | 'double' | 'switchover' | '') {
  const toolBoxKeyboardConfig = keyboardConfigStore.addToolBoxKeyboardConfig({
    code: props.code,
    mode: val,
    key: keyboardConfig.value.key,
  });
  window.keyboardManager.addKeyboardConfig(props.code, toolBoxKeyboardConfig, {
    downHandle: () => {
      currentToolBoxStore.setCurrentToolBoxByCode(props.code, true);
    },
    upHandle: () => {
      currentToolBoxStore.rollbackToolBox();
    }
  });
}
function handleKeyboardChange(val: string) {
  const toolBoxKeyboardConfig = keyboardConfigStore.addToolBoxKeyboardConfig({
    code: props.code,
    key: val,
  });
  window.keyboardManager.addKeyboardConfig(props.code, toolBoxKeyboardConfig, {
    downHandle: () => {
      currentToolBoxStore.setCurrentToolBoxByCode(props.code, true);
    },
    upHandle: () => {
      currentToolBoxStore.rollbackToolBox();
    }
  });
}
const isMacOs = computed(() => {
  if (!window.utools) {
    return true;
  }
  return utools.isMacOS();
});
const toolBoxBarInject = inject<ToolBoxBarProvide>(ToolBoxBarProvideCode);

const configPosition = computed(() => {
  if (toolBoxBarInject.location.value === 'cover') {
    return 'top';
  }
  if (toolBoxBarInject.location.value === 'right') {
    return "right";
  }
  if (toolBoxBarInject.location.value === 'default') {
    return 'bottom';
  }
  return 'bottom';
});

const tipsPosition = computed(() => {
  if (toolBoxBarInject.location.value === 'cover') {
    return 'bottom';
  }
  if (toolBoxBarInject.location.value === 'default') {
    return 'top';
  }
  return 'top';
})
function handleRouterToolConfig() {
  utools.redirect(['截图工具 Plus', '打开插件页面'],
    `#ui.router?router=sceneManager&sceneCode=${window.sceneCode}&toolCode=${props.code}`);
}
</script>

<template>
  <div class="toolbox-wrapper">
    <t-popup overlayClassName="u-web-popup"
             :placement="configPosition"
             v-bind="props.triggerOptions ? props.triggerOptions : {}"
             trigger="click"
             :visible="popupVisible">
          <div ref="toolboxTooltipRef">
            <t-popup overlayClassName="u-web-popup"
                     :placement="tipsPosition"
                     :delay="[generalSetting.toolTooltipDelayTime, 0]"
                     :popup-offset="6">
              <template #content>
                <div class="tooltip-content">
                  <slot name="tooltip">
                    <div class="u-fx u-fac u-gap10 u-f-between u-pointer u-hover"
                         @click="handleRouterToolConfig"
                         style="margin-bottom: 4px;">
                      <div class="tooltip-title">{{props.title}}</div>
                      <div>
                        <div class="i-u-config w-3 h-3"></div>
                      </div>
                    </div>
                    <div v-if="props.keyboard"
                         class="u-fx u-fac u-gap10 tooltip-keyboard" >
                      <div class="label">
                       <t-tooltip content="按键">
                         <div class="i-u-keyboard-one w-3 h-3"></div>
                       </t-tooltip>
                      </div>
                      <div>
                        <t-select size="small"
                                  style="width: 60px"
                                  :model-value="keyboardConfig!.key"
                                  @change="(value: any) => handleKeyboardChange(value)"
                                  allow-search
                                  allow-clear>
                          <a-option v-for="(key, idx) in KEYBOARD"
                                    :key="idx"
                                    :value="value"
                                    :label="key">
                            {{key}}
                          </a-option>
                        </t-select>
                      </div>
                    </div>
                    <div class="u-fx u-fac u-gap10">
                      <div>
                        <t-checkbox :checked="keyboardConfig?.ctrl"
                                    @change="(val: any) => handleMateChange(val as any)">
                          <div class="flex items-center gap-1">
                            <span v-if="isMacOs" style="font-size: 10px; font-weight: 700;">
                              <div class="i-u-command w-3 h-3"></div>
                            </span>
                            <span v-else style="font-size: 12px">CTRL</span>
                          </div>
                        </t-checkbox>
                      </div>
                      <div>
                        <t-tooltip content="按住可直接切换到当前工具,松开回到之前工具">
                          <t-checkbox v-if="props.code === 'screenshotDrag'"
                                      size="small"
                                      :checked="keyboardConfig.mode === 'switchover'"
                                      @change="(value: any) => handleModeChange(value ? 'switchover' : '')">
                          <span v-if="isMacOs" style="font-size: 10px; font-weight: 700;">
                            <div class="i-u-command w-3 h-3"></div>
                          </span>
                            <span v-else style="font-size: 12px">按住</span>
                          </t-checkbox>
                        </t-tooltip>
                      </div>
                    </div>
                  </slot>
                </div>
              </template>
              <slot name="toolbox">
                <div class="toolbox"
                     :class="{
                         active: currentToolBoxStore.getCurrentToolBoxCode === code,
                         medium: generalSetting.toolbarSize === 'medium'
                     }"
                     @click="handleSwitchToolBox">
                  <slot name="icon">
                    <div v-if="toolIcon" :class="toolIcon"></div>
                    <span v-else>无</span>
                  </slot>
                </div>
              </slot>
            </t-popup>
          </div>
      <template v-if="config" #content>
        <div class="toolbox-trigger">
          <slot name="config"></slot>
        </div>
      </template>
    </t-popup>
  </div>
</template>
<style lang="less" scoped>
:deep(.arco-checkbox) {
  padding-left: 0;
}
:deep(.arco-checkbox-icon) {
  width: 12px;
  height: 12px;
}
</style>
<style lang="less">
.min-select {
  .arco-select-dropdown {
    padding: 2px 0;
  }
  .arco-select-dropdown .arco-select-option {
    line-height: 24px;
    font-size: 12px;
    padding: 0 6px;
  }
  &.arco-select-view-single {
    font-size: 12px;
    padding-right: 1px;
    padding-left: 4px;
    .arco-select-view-suffix {
      padding-left: 0;
    }
  }
  .arco-select-view-clear-btn svg, .arco-select-view-icon svg {
    font-size: 10px;
  }
  .arco-select-dropdown-list {
    max-height: 100px;
  }
  .arco-scrollbar-track-direction-vertical {
    display: none;
  }
}
.tooltip-keyboard {
  margin-bottom: 4px;
}
</style>
