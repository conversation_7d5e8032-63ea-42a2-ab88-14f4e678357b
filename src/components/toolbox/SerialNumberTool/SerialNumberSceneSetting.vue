<script setup lang="ts">
import { ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const sceneSerialNumberConfig = toolConfigStore.getSceneSerialNumberConfig(props.sceneCode);
</script>

<template>
  <div  v-if="sceneSerialNumberConfig"
        class="scene-setting-form">
    <t-form :model="modal" :label-width="100">
      <t-form-item label="文本字体大小">
        <t-input-number v-model:value="sceneSerialNumberConfig.textConfig.fontSize"
                        size="small"
                        theme="column" />
      </t-form-item>
      <t-form-item label="文本字体颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneSerialNumberConfig.textConfig.fillSelectOptions"
                            v-model:model-value="sceneSerialNumberConfig.textConfig.fillId"
                            hide-label />
      </t-form-item>
      <t-form-item label="文本背景颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneSerialNumberConfig.textConfig.fillBackgroundSelectOptions"
                            v-model:model-value="sceneSerialNumberConfig.textConfig.fillBackgroundId"
                            hide-label />
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
