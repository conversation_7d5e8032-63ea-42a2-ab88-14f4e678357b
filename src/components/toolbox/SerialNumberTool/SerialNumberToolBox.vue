<script setup lang="ts">
import { CONFIG } from './index'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import FontSizeSelectPanel from '@/components/panel/FontSizeSelectPanel.vue'
import { computed, ref, toRefs, watch } from 'vue'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import type { SerialNumberTool } from '@/components/toolbox/SerialNumberTool/SerialNumberTool'
import { EditorEvent } from '@leafer-in/editor'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import type { IEventListenerId } from '@leafer/interface'
import { type Group, PropertyEvent, type Text } from 'leafer-ui'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import type { IUI } from '@leafer-ui/interface'
import Leafer<PERSON>elper from '@/leaferApp/LeaferHelper'

const toolInfo: ToolboxInfoItemConfig = CONFIG;

const {serialNumberConfig} = toRefs(useToolConfigStore());

const currentToolBoxStore = useCurrentToolBoxStore();
const tool = currentToolBoxStore.getToolByCode<SerialNumberTool>(toolInfo.code)!;
const editor = currentToolBoxStore.getLeaferApp()?.editor!;
const enableTextAttr = ref<boolean>(false);
const singleSelectedIUI = ref<IUI>();
const eventId = ref<IEventListenerId>();


function handleOpenConfig() {
   eventId.value = editor.on_(EditorEvent.SELECT, (e: EditorEvent) => {
     const selectedCurrentTools = tool.getSelectedCurrentTools()
     const serialInnerTextRegion = selectedCurrentTools.find(item => {
      const iuis = item.find('.' + LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION);
      return iuis.length && iuis.length > 0;
    });
    enableTextAttr.value = !!serialInnerTextRegion;
    singleSelectedIUI.value = selectedCurrentTools.length === 1 ? selectedCurrentTools[0] : null;
  })
}

const serialNumberIUI = ref<Text>();
const serialNumberEvent = ref<IEventListenerId>();
const serialNumberNumber = ref();
watch(() => singleSelectedIUI.value, (newValue) => {
  console.log('singleSelected', newValue);
  if (serialNumberEvent.value && serialNumberIUI.value) {
    serialNumberIUI.value.off_(serialNumberEvent.value);
    serialNumberIUI.value = null;
    serialNumberEvent.value = null;
  }
  if (newValue) {
    serialNumberIUI.value = LeaferHelper.findFather(tool.getSelectedCurrentTools()[0], (iui) => {
      return iui.className === LeaferConstant.ClASS_SERIAL_NUMBER;
    }).find('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER)[0].children[0] as Text;
    serialNumberNumber.value = Number(serialNumberIUI.value.text);
    serialNumberEvent.value = serialNumberIUI.value.on_(PropertyEvent.CHANGE, (e: PropertyEvent) => {
      serialNumberNumber.value = Number((e.current as Text).text);
    });
  }
});

function handleSerialNumber(number: number) {
  serialNumberIUI.value.set({
    text: number.toString(),
  });
}
function handleCloseConfig() {
  eventId.value && editor.off_(eventId.value);
  if (serialNumberEvent.value && serialNumberIUI.value) {
    serialNumberIUI.value.off_(serialNumberEvent.value);
    serialNumberIUI.value = null;
    serialNumberEvent.value = null;
  }
}

const showConfig = computed(() => enableTextAttr.value || singleSelectedIUI.value);

// 添加文本框
function handleAddTextBox() {
  tool.createSerialNumberTextBox(tool.getSelectedCurrentTools()[0] as Group);
}
</script>

<template>
  <BaseToolBox v-bind="toolInfo" @open-config="handleOpenConfig" @close-config="handleCloseConfig">
    <template #config>
      <div class="toolbox-config" v-show="showConfig">
        <div class="form-item">
          <div>序号</div>
          <div style="width: 120px">
            <t-input-number :min="0"
                            :max="99"
                            size="small"
                            theme="column"
                            :model-value="serialNumberNumber"
                            @change="(val: any) => handleSerialNumber(val as any)" />
          </div>
        </div>
        <t-button v-show="!enableTextAttr && singleSelectedIUI"
                  size="small"
                  theme="default"
                  @click="handleAddTextBox">
          添加文本框
        </t-button>
        <FontSizeSelectPanel v-show="enableTextAttr"
                             v-model:model-value="serialNumberConfig.textConfig.fontSize" />
        <StrokeSelectPanel v-show="enableTextAttr"
                           v-model:model-value="serialNumberConfig.textConfig.fillId"
                           v-model:stroke-select-options="serialNumberConfig.textConfig.fillSelectOptions">
          <template #title>
            文字
          </template>
        </StrokeSelectPanel>
        <StrokeSelectPanel v-show="enableTextAttr"
                           v-model:model-value="serialNumberConfig.textConfig.fillBackgroundId"
                           v-model:stroke-select-options="serialNumberConfig.textConfig.fillBackgroundSelectOptions"
                           show-transparency>
          <template #title>
            背景色
          </template>
        </StrokeSelectPanel>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">

</style>
