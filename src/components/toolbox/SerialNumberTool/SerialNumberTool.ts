import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { Box, defineKey, Group, PointerEvent, PropertyEvent, Text, UI } from 'leafer-ui'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import { isArray } from '@/utils/jsUtil'
// @ts-ignore
import { LeaferXQnConnector } from 'leafer-x-connector'
import LeaferElement from '@/leaferApp/LeaferElement'
import { EditorEvent, InnerEditorEvent } from '@leafer-in/editor'
import type { ILeaferDataBase } from '@/leaferApp/LeaferData'
import type { IPointData, IUI } from '@leafer-ui/interface'
import type { ITextConfig } from '@/stores/ToolConfigStore/store/TextToolConfigStore'
import { watch } from 'vue'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { type Fn, useEventListener } from '@vueuse/core'
import { ElementLoadEvent } from '@/events/ElementLoadEvent'


export interface ISerialNumberData extends ILeaferDataBase {
  textBoxInnerId: number;
}

export class SerialNumberTool extends BaseUseTool {

  private __cancelTime: number = 0;

  private __elementLoadEvent: Fn | null=  null;

  constructor() {
    super();
    super.register(PointerEvent.UP, this.mouseDown.bind(this));
  }


  increaseSerialNumber(boxList: IUI[], addNumber: number) {
    if (boxList.length && boxList[0].children.length) {
      const serialInnerNumberText = boxList[0].children[0] as Text
      const serialNumber = Number(serialInnerNumberText.text) + addNumber
      if (serialNumber > 99 || serialNumber <= 0) {
        return
      }
      serialInnerNumberText.set({
        text: serialNumber.toString()
      })
    }
    setTimeout(() => {
      delete window.tempData.DOUBLE_TAP;
    }, 500);
  }

  protected doDestroy() {
    super.doDestroy()
    // this.__elementLoadEvent && this.__elementLoadEvent();
  }

  elementLoad() {
    console.log('SerialNumberTool--elementLoad')
    const serialNumberList = (this.getAppTree().find('.' + LeaferConstant.ClASS_SERIAL_NUMBER) || []) as Group[];
    serialNumberList.map((serialNumberGroup: Group) => {
      const textUI = serialNumberGroup.findTag('Text')
        .find(item => item.className  === LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION) as Text;
      LeaferElement.reloadTextBox(this.getApp(), textUI)
      let connector: LeaferXQnConnector = serialNumberGroup.findTag("Arrow")[0];
      serialNumberGroup.remove(connector);
      const serialNumberBox = serialNumberGroup.findOne('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER) as Box;
      connector = new LeaferXQnConnector(serialNumberBox, textUI.parent, {
        padding: 0,
      });

      defineKey(connector, 'editConfig', {
        get() { return {
          rotateable: false,
          resizeable: false,
          moveable: false,
          skewable: false
        } }
      });
      connector.className = LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION;
      connector.set({
        // editable: true,
        stroke: 'red',
        data: {
          code: LeaferConstant.CODE_SERIAL_NUMBER_LINE
        },
      });
      serialNumberGroup.add(connector);
      textUI.on(InnerEditorEvent.CLOSE, (e) => {
        console.log(InnerEditorEvent.CLOSE, e);
        // connector.opacity = 1;
        connector._draw();
      });
      textUI.on(PropertyEvent.CHANGE, (e: PointerEvent) => {
        console.log('textBox.text.CHANGE', e, e.target.getLayoutBounds());
        connector._draw();
      })
      textUI.parent.on(PropertyEvent.CHANGE, (e) => {
        console.log('textBox.CHANGE', e);
        connector._draw();
      });
      textUI.on(PropertyEvent.CHANGE, (e) => {
        console.log('serialNumberBox.CHANGE', e);
        connector._draw();
      });
      textUI.set({
        editable: true
      });
    })
  }

  doInstall() {
    // 元素重新加载或通过 json 加载
    this.__elementLoadEvent && this.__elementLoadEvent();
    this.__elementLoadEvent = useEventListener(window, ElementLoadEvent.ELEMENT_LOAD, this.elementLoad.bind(this));
    window.keyboardManager.addKeyboard('serialNumber', '=,-', (e) => {
      const currentTools = this.getSelectedCurrentTools();
      if (!currentTools.length || currentTools.length > 1) {
        return;
      }
      const boxList = currentTools[0].find('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER);
      this.increaseSerialNumber(boxList, e.code === 'Minus' ? -1 : 1);
    }, 'serialNumber');
    this.getEditor().on(EditorEvent.SELECT, (e: EditorEvent) => {
      if (LeaferHelper.formatIUIArray(e.value).length) {
        return;
      }
      console.log('__cancelTime', Date.now())
      this.__cancelTime = Date.now();
    });
    this.getEditor().on(EditorEvent.SELECT, (e) => {
      window.keyboardManager.switchoverScope('default');
      if (e.value instanceof Array || !e.value || e.value.className !== LeaferConstant.ClASS_SERIAL_INNER_NUMBER) {
        const serialNumberEditor = this.getEditor().buttons
          .findId(LeaferConstant.ID_SERIAL_NUMBER_EDITOR);
        this.getEditor().buttons.remove(serialNumberEditor);
        return;
      }
      window.keyboardManager.switchoverScope('serialNumber');
      const box = new Box({
        id: LeaferConstant.ID_SERIAL_NUMBER_EDITOR,
        y: -40
      });
      const minusButton = Box.one({
        x: -15,
        y: 0,
        around: 'center',
        fill: '#2b88d8',
        cornerRadius: 100,
        cursor: 'pointer',
        children: [{
          tag: 'Text',
          fill: 'white',
          text: '-',
          fontSize: 12,
          lineHeight: 12,
          padding: [2, 6, 2, 7],
        }]
      });

      const plusButton = Box.one({
        x: 15,
        y: 0,
        around: 'center',
        fill: '#2b88d8',
        cornerRadius: 100,
        cursor: 'pointer',
        children: [{
          tag: 'Text',
          fill: 'white',
          text: '+',
          fontSize: 12,
          lineHeight: 12,
          padding: [2, 7, 2, 7],
        }]
      });
      minusButton.on(PointerEvent.CLICK, (e) => {
        window.tempData.DOUBLE_TAP = true;
        const target = this.getEditor().target;
        if (target instanceof Array) {
          return;
        }
        const boxList = target.find('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER);
        this.increaseSerialNumber(boxList, -1);
      })
      plusButton.on(PointerEvent.CLICK, (e) => {
        window.tempData.DOUBLE_TAP = true;
        const target = this.getEditor().target;
        if (target instanceof Array) {
          return;
        }
        const boxList = target.find('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER);
        this.increaseSerialNumber(boxList, 1);
      });

      box.addMany(minusButton, plusButton);
      this.getEditor().config.buttonsMargin = 2;
      this.getEditor().buttons.add(box);
    });
    this.getEditor().on(EditorEvent.SELECT, (e: EditorEvent) => {
      if (e.value instanceof UI && e.value.className === LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION) {
        return;
      }
      const newValue = LeaferHelper.formatIUIArray(e.value);
      const oldValue = LeaferHelper.formatIUIArray(e.oldValue);
      const newValueIds = newValue.map(item => item.innerId);
      const oldValueIds = oldValue.map(item => item.innerId);
      // 取消
      const cancelSelectIuiList = oldValueIds.filter(id => !newValueIds.includes(id))
        .map(innerId => oldValue.find(item => item.innerId === innerId)!)
        .filter(item => {
          const data = item.getAttr('data');
          return data && data.code === LeaferConstant.CODE_SERIAL_NUMBER;
        }).map(item => {
          return LeaferHelper.findFather(item, (iui) => iui.className === LeaferConstant.ClASS_SERIAL_NUMBER);
        }).reduce((prev, cur) => {
          if (cur) {
            const res = prev.find(item => item.innerId === cur.innerId)
            if (!res) {
              return [...prev, cur];
            }
          }
          return prev;
        }, [] as IUI[]);
      for (const cancelSelectIui of cancelSelectIuiList) {
        const deleteIUI = cancelSelectIui.find('.' + LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION);
        console.log(deleteIUI)
        let textIUI = LeaferHelper.findOneInArray(deleteIUI, (iui) => iui.findTag('Text'));
        if (isArray(textIUI) && textIUI.length) {
          textIUI = textIUI[0];
        }
        if (textIUI && textIUI instanceof Text
          && (!textIUI.text || textIUI.text === '请输入文字')) {
          deleteIUI.map(iui => iui.remove());
        }
      }
    })
  }

  protected doUse() {
    const toolConfigStore = useToolConfigStore();
    watch(toolConfigStore.getSerialNumberConfig.textConfig, (newValue) => {
      const selectTextBoxList = this.getSelectedCurrentTools()
        .map(item => {
          const data = item.get('data') as ILeaferDataBase;
          if (data && data.code === LeaferConstant.CODE_SERIAL_NUMBER) {
            const serialNumberData = data as ISerialNumberData;
            if (serialNumberData.textBoxInnerId) {
              return item.findOne(serialNumberData.textBoxInnerId);
            }
            return null;
          }
        }).filter(item => item) as IUI[];
      for (const selectTextBox of selectTextBoxList) {
        this.setTextBoxConfig(selectTextBox, newValue);
      }
    }, {deep: true})
  }

  mouseDown(e: PointerEvent) {
    if (!this.isLeftClick(e)) {
      return;
    }
    if (this._app!.editor.target || Date.now() - this.__cancelTime < 80) {
      return;
    }
    // 找出目前最大的序号
    const serialNumber =  this.getExistMaxSerialNumber();
    if (serialNumber == 99) {
      return;
    }
    this.recordHistory();
    this.applyHistory();
    const localPoint = this.getMiddleGroup().getLocalPoint(e);
    const serialNumberGroup = new Group({
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER,
      },
      className: LeaferConstant.ClASS_SERIAL_NUMBER
    });
    this.getMiddleGroup().add(serialNumberGroup);
    const box = new Box({
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER
      },
      width: 26,
      height: 26,
      className: LeaferConstant.ClASS_SERIAL_INNER_NUMBER,
      fill: "rgb(255, 71, 87)",
      editable: true,
    });
    defineKey(box, 'editConfig', {
      get() { return {
        rotateable: false,
        resizeable: false,
        skewable: false,
      } }
    });
    serialNumberGroup.add(box);

    const text = new Text({
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER
      },
      fill: 'rgb(255, 255, 255)',
      text: (serialNumber + 1).toString(),
      fontSize: 14,
      fontWeight: 700,
      fontFamily: 'Arial'
    });


    text.on(PropertyEvent.CHANGE, (e: PropertyEvent) => {
      if (e.attrName === 'text') {
        text.set({
          padding: [0, 0]
        });
        const textBounds = text.getBounds('content');
        const bounds = box.getBounds('content');
        const scale = this.getAppTree().zoomLayer.scale as number;
        if (text.padding && text.padding instanceof Array) {
          text.set({
            padding: [
              Math.floor((bounds.height - textBounds.height) / 2),
              Math.floor((bounds.width - textBounds.width) / scale / 2),
            ]
          });
        }
      }
    });

    const scale = this.getAppTree().zoomLayer.scale as number;
    box.add(text);
    const textBounds = text.getBounds('content');
    const bounds = box.getBounds('content');
    console.log('textBounds', textBounds, 'bounds', bounds,
      bounds.height - (textBounds.height * scale) / 2, (bounds.width - (textBounds.width * scale)) / 2);
    text.set({
      padding: [
        Math.floor((bounds.height - textBounds.height) / 2),
        Math.floor((bounds.width - textBounds.width) / scale / 2),
      ]
    });
    box.set({
      cornerRadius: bounds.width,
      x: localPoint.x - bounds.width / 2,
      y: localPoint.y - bounds.width / 2,
    });

    this.createSerialNumberTextBox(serialNumberGroup, box);
  }

  /**
   * 创建序号文本框
   * @param serialNumberGroup 整个序号 group
   * @param serialNumberBox 序号 box
   */
  public createSerialNumberTextBox(serialNumberGroup: Group, serialNumberBox?: Box) {
    if (!serialNumberBox) {
      serialNumberBox = serialNumberGroup.findOne('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER) as Box;
    }
    // 文本
    const textBox = LeaferElement.createTextBox(this.getApp(), serialNumberGroup, {
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER
      },
      className: LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION,
    }, {
      className: LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION,
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER
      },
    });

    const textBoxBounds = textBox.getBounds();
    const boxBounds = serialNumberBox.getBounds();
    const scale = this.getAppTree().zoomLayer.scale as number;
    const serialNumberInnerPoint = serialNumberGroup.getInnerPoint(boxBounds)
    const textBoxPos: IPointData = {
      x: serialNumberInnerPoint.x + boxBounds.width + 20,
      y: serialNumberInnerPoint.y - textBoxBounds.height / 2 + boxBounds.height / 2 - 1.5,
    }
    if (textBoxPos.x + 200 * scale > this.getApp().width! * scale) {
      textBoxPos.x = boxBounds.x - textBoxBounds.width - 20;
    }
    textBox.set({
      ...textBoxPos,
    })

    LeaferHelper.setElementData(serialNumberGroup, {
      textBoxInnerId: textBox.innerId
    });

    const {serialNumberConfig} = useToolConfigStore()
    this.setTextBoxConfig(textBox, serialNumberConfig.textConfig);
    serialNumberGroup.add(textBox);

    const connector = new LeaferXQnConnector(serialNumberBox, textBox, {
      padding: 0,
    });
    defineKey(connector, 'editConfig', {
      get() { return {
        rotateable: false,
        resizeable: false,
        moveable: false,
        skewable: false
      } }
    });
    connector.className = LeaferConstant.ClASS_SERIAL_INNER_TEXT_REGION;
    connector.set({
      // editable: true,
      stroke: 'red',
      data: {
        code: LeaferConstant.CODE_SERIAL_NUMBER_LINE
      },
    });

    const iui = textBox.children[0];


    iui.on(InnerEditorEvent.CLOSE, (e) => {
      console.log(InnerEditorEvent.CLOSE, e);
      // connector.opacity = 1;
      connector._draw();
    });
    iui.on(PropertyEvent.CHANGE, (e: PointerEvent) => {
      console.log('textBox.text.CHANGE', e, e.target.getLayoutBounds());
      connector._draw();
    })
    textBox.on(PropertyEvent.CHANGE, (e) => {
      console.log('textBox.CHANGE', e);
      connector._draw();
    });
    serialNumberBox.on(PropertyEvent.CHANGE, (e) => {
      console.log('serialNumberBox.CHANGE', e);
      connector._draw();
    });
    serialNumberGroup.add(connector);
  }

  private setTextBoxConfig(tool: IUI, userConfig: ITextConfig) {
    let box: Box | null = null;
    let text: Text | null = null;
    if (tool instanceof Box) {
      box = tool;
      text = box.children[0] as Text;
    } else if (tool instanceof Text) {
      text = tool;
      box = text.parent as Box;
    }
    if (!box || !text) {
      return;
    }
    const fillBackground = userConfig.fillBackgroundSelectOptions
      .find(item => userConfig.fillBackgroundId === item.id);
    const fill = userConfig.fillSelectOptions.find(item =>  userConfig.fillId === item.id);
    box.set({
      fill: fillBackground,
    });
    text.set({
      fill: fill,
      fontSize: userConfig.fontSize
    })
  }


  /**
   * 获取当前选择序号元素分组 <br/>
   * 这里返回是序号 group
   */
  getSelectedCurrentTools(): IUI[] {
    const iuis = LeaferHelper.getSelectTargets(this.getApp());
    return iuis.filter(item => {
      const data = item.getAttr('data');
      return data && data.code === LeaferConstant.CODE_SERIAL_NUMBER;
    }).map(item => {
      return LeaferHelper.findFather(item, (iui) => iui.className === LeaferConstant.ClASS_SERIAL_NUMBER);
    }).reduce((prev, cur) => {
      if (cur) {
        const res = prev.find(item => item.innerId === cur.innerId)
        if (!res) {
          return [...prev, cur];
        }
      }
      return prev;
    }, [] as IUI[]);
  }

  /**
   * 找出目前最大的序号
   * @private
   */
  private getExistMaxSerialNumber() {
    const serialNumberList = this.getApp()
      .find('.' + LeaferConstant.ClASS_SERIAL_INNER_NUMBER)
      .filter(item => item)
      .map(item => item.findTag('Text')[0])
      .filter(item => item)
      .map(item => (item as Text).text)
      .map(item => Number(item));
    return serialNumberList.length ? Math.max(...serialNumberList) : 0;
  }
}
