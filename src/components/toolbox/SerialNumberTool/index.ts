import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { SerialNumberTool } from '@/components/toolbox/SerialNumberTool/SerialNumberTool'
import SerialNumberToolBox from '@/components/toolbox/SerialNumberTool/SerialNumberToolBox.vue'
import SerialNumberSceneSetting from './SerialNumberSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'serialNumber',
  title: '序号',
  sort: 9,
  group: 'draw',
  keyboard: {
    key: '',
  },
  toolIcon: 'i-u-u-serial',
  config: true,
  sceneSetting: async () => SerialNumberSceneSetting,
}

export default {
  info: CONFIG,
  tool: new SerialNumberTool(),
  component: async () => SerialNumberToolBox
} as ToolboxItem;
