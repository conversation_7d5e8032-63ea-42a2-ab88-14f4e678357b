import CloseToolBox from './CloseToolBox.vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import CloseTool from './CloseTool'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'close',
  title: '关闭',
  sort: 101,
  group: 'action',
  instant: false,
  keyboard: {
    key: 'ESC'
  },
  toolIcon: 'i-u-close',
  config: false
}
export default {
  info: CONFIG,
  tool: new CloseTool(),
  component: async () => CloseToolBox
} as ToolboxItem;
