<script setup lang="tsx">
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import LocalOcrModal from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal/LocalOcrModal.vue'
import OcrSettingModal from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal/OcrSettingModal.vue'
import { computed, onMounted, ref } from 'vue'
import { SYSTEM_OCR_LIST } from '@/views/toolSetting/templates/OcrSetting/OcrConstants'
import type {
  ILocalOcrModalInstance,
  IOcrSettingModalInstance
} from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal'
import { getOcrPlatformInfoList } from '@/leaferApp/ocr'
import {
  getInstalledLocalOcrModelList,
  type ILocalOcrModelItem
} from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'
import DataUtils from '@/utils/DataUtils'
import EnvironmentUtils from '@/utils/EnvironmentUtils'

import { SwitchPlusEnable } from '@xiaou66/u-web-ui';
import type { TableProps } from 'tdesign-vue-next'
import { Link, Popconfirm, Button, Icon } from 'tdesign-vue-next'

const props = defineProps<{
  sceneCode: string
}>();
const toolConfigStore = useToolConfigStore();
const ocrConfig = toolConfigStore.getOcrConfig(props.sceneCode);


const { getOcrCloudConfig, ocrCloudRemoveConfig } = useToolConfigStore();
const orcSelectListData = computed(() => {
  console.log('getOcrCloudConfig', getOcrCloudConfig)
  return [
    ...SYSTEM_OCR_LIST,
    ...getOcrCloudConfig,
  ]
});

function handleOcrConfigDelete(key: number) {
  ocrCloudRemoveConfig(key)
}

const ocrSettingModalRef = ref<IOcrSettingModalInstance>();
function handleOpenInfo(record: any) {
  ocrSettingModalRef.value.onShow(record);
}


const ocrPlatformMap = computed<Record<string, string>>(() => {
  return getOcrPlatformInfoList().reduce((obj, cur) => {
    obj[cur.code] = cur.name;
    return obj;
  }, {} as Record<string, string>);
});
onMounted(() => {
  refreshInstalledLocalOcrModel();
});
const localOcrModalRef = ref<ILocalOcrModalInstance>();
const installedLocalOcrModelList = ref<ILocalOcrModelItem[]>([]);
function refreshInstalledLocalOcrModel() {
  installedLocalOcrModelList.value = getInstalledLocalOcrModelList();
  if (!DataUtils.getDataOcrActiveModel() && installedLocalOcrModelList.value.length) {
    // 当前使用的模型被卸载了并且安装模型列表中还有模型选择列表第一个模型
    handleEnableLocalOcrModel(installedLocalOcrModelList.value[0].installDir);
  } else {
    refreshLocalOcrEnable();
  }
}
// 卸载 Ocr 模型
function handleUninstallOcrModel(model: ILocalOcrModelItem) {
  EnvironmentUtils.uninstallOcrModel(model);
  refreshInstalledLocalOcrModel();
}
const activeModel = ref()
// 刷新激活
function refreshLocalOcrEnable() {
  activeModel.value = DataUtils.getDataOcrActiveModel();
}
function handleEnableLocalOcrModel(installDir: string) {
  window.fs.writeFileSync(window.path.join(DataUtils.getDataOcrPath(), 'enable'), installDir, 'utf-8');
  refreshLocalOcrEnable();
}

const cloudColumns: TableProps['columns'] = [
  {
    // title: '单选',
    // align: 'center',
    colKey: 'row-select',
    type: 'single',
    width: 50,
  },
  {
    title: '平台',
    colKey: 'platform',
    cell: (h, { row }) => {
      return (
        <div>
          {ocrPlatformMap.value[row.platform]}
        </div>
      );
    },
    width: 120,
  },
  {
    title: '名称',
    colKey: 'name',
  },
  {
    title: '操作',
    colKey: 'active',
    width: 88,
    align: 'center',
    title: () => (
      <Button
        theme={'default'}
        size={'small'}
        onClick={() => ocrSettingModalRef.value.onShow()}
        v-slots={{
          icon: () => <Icon class="i-u-plus" />,
          default: () => '新增'
        }}
      />
    ),
    cell: (h, { row }) => {
      return (
        <div class="flex gap-2">
          <Popconfirm  content="确认删除吗"
                      theme="danger"
                      onConfirm={ () => handleOcrConfigDelete(row.key) }>
            <Link size="small" theme="danger">
              删除
            </Link>
          </Popconfirm>

          <Link size="small" theme="primary"
                onClick={ () => handleOpenInfo(row) }>详情</Link>
        </div>
      );
    },
  },
];
const rehandleSelectChange = (value) => {
  ocrConfig.value.cloudConfigKey = value[0];
};

const localColumns: TableProps['columns'] = [
  {
    // title: '单选',
    // align: 'center',
    colKey: 'row-select',
    type: 'single',
    width: 40,
  },
  {
    title: '模型',
    colKey: 'modelName',
  },
  {
    title: '操作',
    colKey: 'active',
    width: 45,
    align: 'center',
    cell: (h, { row }) => {
      return (
        <div class="flex gap-2">
          <Popconfirm  content="确认卸载吗"
                       theme="danger"
                       onConfirm={ () => handleUninstallOcrModel(row) }>
            <Link size="small" theme="danger">
              卸载
            </Link>
          </Popconfirm>
        </div>
      );
    },
  }
]
</script>

<template>
  <LocalOcrModal ref="localOcrModalRef"
                 @hide="refreshInstalledLocalOcrModel" />
  <OcrSettingModal ref="ocrSettingModalRef" />
  <div class="scene-setting-form">
    <t-form-item>
      <div class="flex justify-between w-full">
        <div>
          <t-radio-group v-model:model-value="ocrConfig.globalType"
                         size="small"
                         variant="default-filled"
                         type="button">
            <t-tooltip content="会将图片发送给其他插件实现 OCR 识别">
              <t-radio-button value="plugIn">utools 插件支持</t-radio-button>
            </t-tooltip>
            <t-radio-button value="cloud">云平台 OCR</t-radio-button>
            <t-radio-button value="local">本地 OCR</t-radio-button>
          </t-radio-group>
        </div>
        <div v-if="ocrConfig.globalType !== 'plugIn'"
             class="u-fx u-fac u-gap10">
          <div class="text-sm">自动识别</div>
          <SwitchPlusEnable v-model:value="toolConfigStore.ocrConfig.autoOcr"
                            size="small"
                            style="width: 38px">
          </SwitchPlusEnable>
        </div>
      </div>
      <template #help>
        <div class="mt-3">
          <div v-if="ocrConfig.globalType === 'plugIn'">
            <div>
              <t-select size="small"
                        style="width: 200px;"
                        v-model:value="ocrConfig.plugInKeyWord">
                <t-option label="OCR 文字识别插件" value="OCR 文字识别|OCR文字识别" />
                <t-option label="OCR Pro 插件" value="OCR Pro|剪切板图片OCR识别" />
              </t-select>
            </div>
            <div class="u-mt10 u-fx u-f-between"
                 style="width: 200px;">
              <div>
                识别后关闭截图
              </div>
              <SwitchPlusEnable v-model:value="ocrConfig.plugInAutoClose"
                                style="width: 32px"
                                size="mini">
              </SwitchPlusEnable>
            </div>
          </div>
          <div v-if="ocrConfig.globalType === 'cloud'">
            <t-table class="u-web-table"
                     size="small"
                     :selected-row-keys="[ocrConfig.cloudConfigKey]"
                     style="width: 100%"
                     :data="orcSelectListData"
                     :pagination="null"
                     :columns="cloudColumns"
                     rowKey="key"
                     hover
                     disableDataPage
                     selectOnRowClick
                     @select-change="rehandleSelectChange" />
          </div>
          <div v-if="ocrConfig.globalType === 'local'">
            <div>
              <div class="u-fx u-fac u-f-between u-mb10">
                <div class="local-title">模型已安装列表</div>
                <div class="u-fx u-fac u-gap10">
                  <t-button size="small"
                            theme="primary"
                            shape="round"
                            @click="() => localOcrModalRef.show()">
                    <template #icon>
                      <div class="i-u-download w-4 h-4"></div>
                    </template>
                    <div class="pl-1">模型下载</div>
                  </t-button>
                </div>
              </div>
              <t-table size="small"
                       class="u-web-table"
                       rowKey="installDir"
                       :selected-row-keys="[activeModel]"
                       :data="installedLocalOcrModelList"
                       :columns="localColumns"
                       :pagination="null"
                       hover
                       disableDataPage
                       selectOnRowClick
                       @select-change="(value, { currentRowData }) => handleEnableLocalOcrModel(currentRowData.installDir)">
              </t-table>
            </div>
          </div>
        </div>
      </template>
    </t-form-item>
  </div>
</template>
<style lang="less">
.t-popconfirm__content {
  padding: 12px;
}
</style>
<style scoped lang="less">

.scene-setting-form {
  width: 100%;
}
.download-trigger {
  flex-direction: column;
  background: var(--main-background);
  padding: 14px;
  border-radius: 12px;
}
.local-title {
  color: var( --text-color);
  font-size: 12px;
}
:deep(.arco-form-item-extra) {
  width: 100%;
}
</style>
