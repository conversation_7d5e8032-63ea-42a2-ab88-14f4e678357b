import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { getOcrPlatformInstanceByCode } from '@/leaferApp/ocr'
import OcrContentBase, { type IOcrPlatFromConfigItem } from '@/leaferApp/ocr/OcrContentBase'
import DataUtils from '@/utils/DataUtils'
import { nanoid } from 'nanoid'
import FileUtils from '@/utils/FileUtils'
import MainApi from '@/core/sdk/MainApi'
import type { IOcrPlugInConfig } from '@/stores/ToolConfigStore/store/OcrToolConfigStore'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { Button } from 'tdesign-vue-next'
import { h } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { SYSTEM_OCR_LIST } from '@/views/toolSetting/templates/OcrSetting/OcrConstants'
import type { ILocalOcrContentConfig } from '@/leaferApp/ocr/impl/LocalOcrContent'
import { MainImageLoadedEvent } from '@/events/MainImageLoadedEvent'
import { OcrSuccessEvent } from '@/events/OcrSuccessEvent'
import { isPromise } from 'es-toolkit'


/**
 * 文字识别
 */
export default class OcrTool extends BaseUseTool {

  private __tips: boolean = true;

  private __platformInstance: OcrContentBase;

  constructor() {
    console.log('文字识别')
    super();
  }

  doInstall() {
    window.api.onOcrResult = (args: any, webContentsId: number) => {
      this.imageOcr(args);
    }


    const { ocrConfig } = useToolConfigStore();
    window.addEventListener(MainImageLoadedEvent.MAIN_IMAGE_LOADED_EVENT, (e) => {
      if (ocrConfig.autoOcr && ocrConfig.globalType !== 'plugIn') {
        this.__tips = false;
        setTimeout(() => {
          this.doUse();
        }, 800)
      }
    });
  }

  imageOcr(ocrResult: any) {
    const toolConfigStore = useToolConfigStore();
    const ocrConfig = toolConfigStore.ocrConfig;
    const platformInstance = getOcrPlatformInstanceByCode(ocrConfig.globalType);
    this.__platformInstance = platformInstance;
    platformInstance.init(this.getApp())
      .ocrResult(ocrResult.data.textBlocks);
    console.log('imageOcr', ocrResult.data.textBlocks);
    new OcrSuccessEvent().dispatchEvent();
  }


  protected doUse() {
    this.ocr();
  }

  public ocr() {
    const toolConfigStore = useToolConfigStore();
    const ocrConfig = toolConfigStore.ocrConfig;
    if (ocrConfig.globalType === 'plugIn') {
      this.handlePlugIn(ocrConfig);
    } else if (ocrConfig.globalType === 'cloud') {
      this.handleCloud(ocrConfig);
    } else if (ocrConfig.globalType === 'local') {
      this.handleLocal(ocrConfig);
    }
  }


  private async handleLocal(ocrConfig: IOcrPlugInConfig) {
    if (!EnvironmentUtils.haveLocalOcrEnvironment()) {
      await ScreenshotCapacity.showConfirmNotification('warning', {
        title: '提示',
        content: () => (
          <div>
            <div>未查询到安装到本地 OCR, 请重新安装</div>
            <div class="u-fx u-mt10" style={{ justifyContent: 'flex-end' }}>
              <Button
                size="small"
                onClick={() => {
                  utools.redirect(['截图工具 Plus', '打开插件页面'],
                    `#ui.router?router=sceneManager&sceneCode=${window.sceneCode}&toolCode=${this.toolInfo.code}`)
                }}
              >
                去下载
              </Button>
            </div>
          </div>
        ),
      });
      return;
    }
    const platformInstance = getOcrPlatformInstanceByCode(ocrConfig.globalType);
    this.__platformInstance = platformInstance;
    if (!document.getElementById('ocr').hasChildNodes()) {
      await ScreenshotCapacity.showTipsNotification('info', {
        title: '提示',
        content: '识别中, 请等待',
        duration: 1000
      });
    }

    // const show = platformInstance.show();
    if (platformInstance.isAlreadyOcr) {
      return;
    }
    const ocrTempFilename = nanoid(64) + '.png';
    const ocrTempPath = window.path.join(DataUtils.getDataSaveTempPath(), 'ocr');
    FileUtils.createDirIfAbsent(ocrTempPath);
    ScreenshotCapacity.exportScreenshotBlob(this.getApp())
      .then(blob => {
        const ocrFilePath = window.path.join(ocrTempPath, ocrTempFilename);
        blob.arrayBuffer().then(arrayBuffer => {
          window.fs.writeFileSync(ocrFilePath, window.nodeBuffer.Buffer.from(arrayBuffer as any, 'utf-8'));

          const activeModel = DataUtils.getDataOcrActiveModel();

          if (activeModel.includes('paddleOcr')) {
            MainApi.invokeImageOcr(window.winHelper.mainId, {imagePath: ocrFilePath});
          } else {
            platformInstance.init(this.getApp())
              .ocr({ saveFilePath: ocrFilePath } as ILocalOcrContentConfig)
              .then(res => {
                console.log('提示--识别完成')
                ScreenshotCapacity.showTipsNotification('info', {
                  title: '提示',
                  content: '识别完成',
                  duration: 1000
                });
                new OcrSuccessEvent().dispatchEvent();
              });
          }
        });
      });
  }


  private handleCloud(ocrConfig: IOcrPlugInConfig) {
    const toolConfigStore = useToolConfigStore();
    let platFromConfigItem: IOcrPlatFromConfigItem | null = null;


    if (ocrConfig.cloudConfigKey > 0) {
      // 用户自己保存的配置
      platFromConfigItem = toolConfigStore.getOcrCloudConfig.find(item =>
        item.key === ocrConfig.cloudConfigKey);
    } else {
      // 系统内置的配置
      platFromConfigItem = SYSTEM_OCR_LIST.find(item =>
        item.key === ocrConfig.cloudConfigKey);
    }
    if (!platFromConfigItem.platform) {
      // 没有配置云 OCR 项
      return;
    }
    this.__platformInstance = getOcrPlatformInstanceByCode(platFromConfigItem.platform);
    const res  = this.platformInstance.init(this.getApp())
      .ocr(platFromConfigItem)
    if (isPromise(res)) {
      res.then(res => {
        console.log('识别完成', res);
        ScreenshotCapacity.showTipsNotification('info', {
          title: '提示',
          content: '识别完成',
          duration: 1000
        });
        new OcrSuccessEvent().dispatchEvent();
      });
    }
  }


  private handlePlugIn(ocrConfig: IOcrPlugInConfig) {
    ScreenshotCapacity.exportScreenshotDataUrl(this.getApp())
      .then(base64 => {
        utools.redirect(ocrConfig.plugInKeyWord.split('|'), {
          type: 'img',
          data: base64
        });
        const { ocrConfig: { plugInAutoClose } } = useToolConfigStore();
        if (window.environment === 'capture' || plugInAutoClose) {
          ScreenshotCapacity.screenshotClose().then(r => {});
        }
      });
  }

  public get platformInstance() {
    return this.__platformInstance;
  }

  protected doDestroy() {
    // ocrHide();
  }
}
