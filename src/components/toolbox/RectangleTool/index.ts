import DrawRectangleToolBox from './DrawRectangleToolBox.vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { RectangleUseTool } from './DrawRectangleTool'
import DrawRectangleSceneSetting from './DrawRectangleSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'rectangle',
  title: '矩形',
  sort: 2,
  group: 'draw',
  keyboard: {
    key: '1'
  },
  toolIcon: 'i-u-rectangle-one',
  config: true,
  sceneSetting: async () => DrawRectangleSceneSetting,
}


export default {
  info: CONFIG,
  tool: new  RectangleUseTool(),
  component: async () => DrawRectangleToolBox,
} as ToolboxItem;
