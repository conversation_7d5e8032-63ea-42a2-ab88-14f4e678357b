<script lang="ts" setup>

import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import { toRefs } from 'vue'
import StrokeWidthSelectPanel from '@/components/panel/StrokeWidthSelectPanel.vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { CONFIG } from '@/components/toolbox/RectangleTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG;

const { rectangleConfig } = toRefs(useToolConfigStore())

</script>
<template>
<BaseToolBox v-bind="toolInfo">
  <template #config>
    <div class="toolbox-config">
      <StrokeWidthSelectPanel v-model:model-value="rectangleConfig.strokeWidth" />
      <StrokeSelectPanel v-model:stroke-select-options="rectangleConfig.strokeSelectOptions"
                         v-model:model-value="rectangleConfig.currentStroke"/>
      <div class="u-fx corner-radius" style="justify-content: space-between">
        <t-checkbox v-model:model-value="rectangleConfig.fill"
                    class="select-none">
          <div class="u-font-size-smail">填充</div>
        </t-checkbox>
        <t-radio-group v-model:model-value="rectangleConfig.shape"
                       size="small"
                       variant="default-filled">
          <t-radio-button value="normal">正常</t-radio-button>
          <t-radio-button value="round">圆角</t-radio-button>
        </t-radio-group>
      </div>
    </div>
  </template>
</BaseToolBox>
</template>
<style lang="less" scoped>
:deep(.arco-checkbox-label) {
  font-size: 12px;
}
.corner-radius {
  justify-content: flex-end;
}
</style>
