import { PointerEvent, Rect } from 'leafer-ui'
import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { watch, type WatchStopHandle } from 'vue'
import type { IRectangleConfig } from '@/stores/ToolConfigStore/store/RectangleToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

export class RectangleUseTool extends BaseDrawTool<Rect> {

  private __configWatch?: WatchStopHandle;

  constructor() {
    super(Rect);
  }

  doInstall() {
    this.installHollowDragElement();
    this.installHoverStyle();
  }

  protected doUse() {
    this.modifyCursor('crosshair');
    const toolConfigStore = useToolConfigStore()
    this.__configWatch = watch(toolConfigStore.getRectangleConfig, (userConfig) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        this.setUserConfig(tool, userConfig);
      }
    })
  }

  doMouseDown(e: PointerEvent) {
    console.log('doMouseDown', e)
    const pointData = this._downPoint!.get()
    const toolConfigStore = useToolConfigStore();
    const rect = new Rect({
      className: this.toolInfo.code,
      ...pointData,
      editable: true,
      width: 0,
      height: 0,
    });
    this.setUserConfig(rect, toolConfigStore.getRectangleConfig);
    // this.downPoint = pointData;
    // this.ui = rect;
    return rect;
  }

  private setUserConfig(tool: Rect, userConfig: IRectangleConfig) {
    const cornerRadius = userConfig.shape === 'round'
      ? [10, 10, 10, 10]
      : [0, 0, 0, 0];

    const stroke =  userConfig.strokeSelectOptions
      .find(item => userConfig.currentStroke === item.id)!;
    if (userConfig.fill) {
      tool.set({
        fill: stroke
      });
    } else {
      tool.set({ fill: null });
    }
    tool.set({
      cornerRadius,
      stroke,
      strokeWidth: userConfig.strokeWidth,
    });
  }

  doMouseMove(e: PointerEvent) {
    const word = {x: e.x, y: e.y};
    this._app!.tree.worldToInner(word);
    const ui = this.ui!;
    const width = word.x - ui.x;
    const height = word.y - ui.y;
    ui.set({
      scaleX: width > 0 ? 1 : -1,
      scaleY: height > 0 ? 1 : -1,
      width: Math.abs(width),
      height: Math.abs(height),
    });
  }

  protected doDestroy() {
    this.modifyCursor('default');
    this.__configWatch && this.__configWatch();
  }
}
