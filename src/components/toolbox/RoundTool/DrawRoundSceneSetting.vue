<script setup lang="ts">
import { ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const sceneRoundConfig = toolConfigStore.getSceneRoundConfig(props.sceneCode);
</script>

<template>
  <div  v-if="sceneRoundConfig" class="scene-setting-form">
    <t-form :model="modal" :label-width="80">
      <t-form-item label="边框大小">
        <t-input-number v-model:value="sceneRoundConfig.strokeWidth"
                        theme="column" size="small" />
      </t-form-item>
      <t-form-item label="颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneRoundConfig.strokeSelectOptions"
                            v-model:model-value="sceneRoundConfig.currentStroke"
                            hide-label />
      </t-form-item>
      <t-form-item label="是否填充">
        <t-checkbox v-model:checked="sceneRoundConfig.fill" />
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
