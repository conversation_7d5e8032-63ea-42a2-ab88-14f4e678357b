<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import StrokeWidthSelectPanel from '@/components/panel/StrokeWidthSelectPanel.vue'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { toRefs } from 'vue'
import { CONFIG } from '@/components/toolbox/RoundTool/index'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG;

const { roundConfig } = toRefs(useToolConfigStore())
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="toolbox-config">
        <StrokeWidthSelectPanel v-model:model-value="roundConfig.strokeWidth" />
        <StrokeSelectPanel v-model:stroke-select-options="roundConfig.strokeSelectOptions"
                           v-model:model-value="roundConfig.currentStroke"/>
        <t-checkbox v-model:checked="roundConfig.fill"
                    class="select-none">
          <div class="u-font-size-smail">填充</div>
        </t-checkbox>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
:deep(.arco-checkbox-label) {
  font-size: 12px;
}
</style>
