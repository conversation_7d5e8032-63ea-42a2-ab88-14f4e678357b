import { Ellipse, PointerEvent } from 'leafer-ui'
import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import { watch, type WatchStopHandle } from 'vue'
import type { IRoundConfig } from '@/stores/ToolConfigStore/store/RoundToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import type { IPointData } from '@leafer-ui/interface'

export class DrawRoundTool extends BaseDrawTool<Ellipse> {
  private downPoint: IPointData;
  private __configWatch?: WatchStopHandle;

  constructor() {
    super(Ellipse)
  }

  doInstall() {
    this.installHollowDragElement();
    this.installHoverStyle();
  }

  protected doUse() {
    this.modifyCursor('crosshair');
    const toolConfigStore = useToolConfigStore()
    this.__configWatch = watch(toolConfigStore.getRoundConfig, (userConfig) => {
      const selectedCurrentTools = this.getSelectedCurrentTools();
      if (!selectedCurrentTools.length) {
        return;
      }
      for (const tool of selectedCurrentTools) {
        this.setUserConfig(tool, userConfig);
      }
    })
  }

  doMouseDown(e: PointerEvent) {
    const pointData = this._downPoint!.get();

    const ellipse = new  Ellipse({
      className: this.toolInfo.code,
      ...pointData,
      width: 0,
      height: 0,
      editable: true,
    })
    this.downPoint = pointData;
    const toolConfigStore = useToolConfigStore();
    this.setUserConfig(ellipse, toolConfigStore.getRoundConfig);
    return ellipse;
  }

  private setUserConfig(tool: Ellipse, userConfig: IRoundConfig) {
    const stroke =  userConfig.strokeSelectOptions
      .find(item => userConfig.currentStroke === item.id)!;
    if (userConfig.fill) {
      tool.set({
        fill: stroke
      });
    } else {
      tool.set({ fill: null });
    }
    tool.set({
      stroke,
      strokeWidth: userConfig.strokeWidth,
    });
  }

  doMouseMove(e: PointerEvent) {
    const innerPoint = this.getAppTree().getInnerPoint(e)
    const ui = this.ui!;
    const width = innerPoint.x - this.downPoint.x;
    const height = innerPoint.y - this.downPoint.y;
    ui.set({
      scaleX: width > 0 ? 1 : -1,
      scaleY: height > 0 ? 1 : -1,
      width: Math.abs(width),
      height: Math.abs(height),
    });
  }

  protected doDestroy() {
    this.modifyCursor('default');
    this.__configWatch && this.__configWatch();
  }

}
