import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import DrawRoundToolBox from './DrawRoundToolBox.vue'
import { DrawRoundTool } from './DrawRoundTool'
import DrawRoundSceneSetting from './DrawRoundSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'round',
  title: '圆形',
  sort: 3,
  group: 'draw',
  keyboard: {
    key: '2'
  },
  toolIcon: 'i-u-round',
  config: true,
  sceneSetting: async () => DrawRoundSceneSetting,
}
export default {
  info: CONFIG,
  tool: new DrawRoundTool(),
  component: async () => DrawRoundToolBox,
} as ToolboxItem;
