import { BaseUseTool } from '../BaseUseTool'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { AiBaiduTranslate, TranslateError } from '@/components/toolbox/TranslateTool/translate'
import { BaiduTranslate } from '@/components/toolbox/TranslateTool/translate/impl/BaiduTranslate'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { TencentTranslate } from '@/components/toolbox/TranslateTool/translate/impl/TencentTranslate'
import { Button } from 'tdesign-vue-next'

/**
 * 翻译工具实现
 */
export default class TranslateTool extends BaseUseTool {

  constructor() {
    console.log('图片翻译')
    super();
  }

  protected doUse(): void {
    if (document.getElementById('translate').childNodes.length > 0) {
      return;
    }
    this.translate().then(() => {});
  }
  public async translate() {
    const { translateActiveId, getTranslateConfig } = useToolConfigStore();
    if (!translateActiveId) {
      await ScreenshotCapacity.showConfirmNotification('warning', {
        title: '提示',
        content: () => (
          <div>
            <div>未查询未配置翻译源, 请先配置再使用</div>
            <div class="u-fx u-mt10" style={{ justifyContent: 'flex-end' }}>
              <Button
                size="small"
                theme="primary"
                onClick={() => {
                  utools.redirect(['截图工具 Plus', '打开插件页面'],
                    `#ui.router?router=sceneManager&sceneCode=${window.sceneCode}&toolCode=${this.toolInfo.code}`)
                }}
              >
                去配置
              </Button>
            </div>
          </div>
        ),
      });
      return;
    }
    const translateConfig = getTranslateConfig(translateActiveId);

    let res: undefined | Promise<any> = undefined;
    switch (translateConfig.code) {
      case "aiBaidu":
        res = new AiBaiduTranslate().translate(this.getApp(), translateConfig as any);
        break;
      case "baidu":
        res = new BaiduTranslate().translate(this.getApp(), translateConfig as any);
        break;
      case 'tencent':
        res = new TencentTranslate().translate(this.getApp(), translateConfig as any);
    }
    if (res) {
      try {
        await res;
        ScreenshotCapacity.showTipsNotification('info', {
          title: '提示',
          content: '翻译完成',
          duration: 1000
        });
      } catch (e: any) {
       if (e instanceof TranslateError) {
         ScreenshotCapacity.showTipsNotification('error', {
           title: '提示',
           content: `code: ${e.platform}, error: ${e.message}`,
           duration: 5000
         });
        } else {
         ScreenshotCapacity.showTipsNotification('warning', {
           title: '提示',
           content: e,
           duration: 5000
         });
       }
      }
    }
  }
}
