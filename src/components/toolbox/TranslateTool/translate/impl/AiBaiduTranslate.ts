import BaiduApi from '@/api/baiduApi'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import type { App } from 'leafer-ui'
import type { BaseTranslateConfig, ITranslateInfo } from '../BaseTranslate'
import { BaseTranslate, TranslateError } from '../BaseTranslate'

export interface AiBaiduTranslateConfig extends BaseTranslateConfig {
  apiKey: string
  secretKey: string
}

export const info: ITranslateInfo  = {
  name: 'AI百度翻译',
  code: 'aiBaidu'
}


export class AiBaiduTranslate extends BaseTranslate {

  private calculateOptimalFontSize(height: number, width: number, text: string): number {
    // 基础字体大小：使用高度的 85%
    let fontSize = Math.round(height * 0.6)

    // 短文本可以更大
    if (text && text.length <= 8) {
      fontSize = Math.round(height * 0.6)
    }

    // 考虑宽度限制，但更宽松
    if (text && width) {
      const avgCharWidth = 0.6 // Times New Roman 实际字符宽度比例
      const maxFontSizeByWidth = Math.floor(width / (text.length * avgCharWidth))
      fontSize = Math.min(fontSize, maxFontSizeByWidth)
    }

    // 设置更合理的边界
    fontSize = Math.max(8, Math.min(fontSize, 72))

    return fontSize
  }

  // {"error_code":"0","error_msg":"success","data":{"from":"en","to":"zh","content":[{"src":"Support for people and the","dst":"支持人民和","rect":"49 28 795 214","lineCount":2,"points":[{"x":49,"y":31},{"x":840,"y":22},{"x":842,"y":230},{"x":51,"y":239}],"pasteImg":""},{"src":"planet—certified.","dst":"行星认证。","rect":"47 252 906 98","lineCount":1,"points":[{"x":47,"y":258},{"x":950,"y":252},{"x":951,"y":342},{"x":48,"y":348}],"pasteImg":""}],"sumSrc":"Support for people and the\nplanet—certified.","sumDst":"支持人民和\n行星认证。","pasteImg":""}}
  public async translate(app: App, config: AiBaiduTranslateConfig) {
    const token = await this.getAccessToken(config);
    const blob = await ScreenshotCapacity.exportScreenshotBlob(app)
    const translateResult = await BaiduApi.getTranslateImage(blob, token)
    const devicePixelRatio = window.devicePixelRatio
    if (translateResult.error_msg !== 'success') {
      console.log('translateResult', translateResult)
      throw new TranslateError(translateResult.error_code,
        config.code, config.apiKey, translateResult.error_msg);
    }
    const data = translateResult.data
    if (data.content && data.content.length > 0) {
      for (const item of data.content) {
        const points = item.points
        const textContainer = this.getTextContainerDom({
          left: points[0].x,
          top: points[0].y,
          content: item.dst
        })
        const width = (points[1].x - points[0].x) / devicePixelRatio
        const height = (points[3].y - points[0].y) / devicePixelRatio
        textContainer.style.width = `${width}px`
        textContainer.style.height = `${height}px`
        textContainer.style.lineHeight = `${height}px`;
        textContainer.style.fontSize = `12px`
        textContainer.style.backdropFilter = 'blur(20px)'
        textContainer.style.backgroundColor = 'rgb(255 255 255)'
        textContainer.style.color = '#000000'
        textContainer.style.textAlign = 'left'
      }
    }
  }

  private async getAccessToken(config: Pick<AiBaiduTranslateConfig, 'apiKey' | 'secretKey'>) {
    const res = await BaiduApi.getAccessToken(config)
    return res.token
  }
}
