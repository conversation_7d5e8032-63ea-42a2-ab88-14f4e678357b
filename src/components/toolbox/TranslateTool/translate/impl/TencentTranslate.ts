import {
  BaseTranslate,
  type BaseTranslateConfig,
  type ITranslateInfo,
  TranslateError
} from '@/components/toolbox/TranslateTool/translate'
import type { App } from 'leafer-ui'
import SHA256 from 'crypto-js/sha256'
import HmacSHA256 from 'crypto-js/hmac-sha256'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'

export const info: ITranslateInfo = {
  name: '腾讯翻译',
  code: 'tencent'
}

interface TencentTranslateConfig extends BaseTranslateConfig {
  secretId: string
  secretKey: string
  region?: string
  service?: string
  projectId?: number
}

// 图片翻译请求参数
interface ImageTranslateRequest {
  SessionUuid: string
  Scene: string
  Data: string // Base64编码的图片数据
  Source: string
  Target: string
  ProjectId: number
}

// 图片翻译响应结果
interface ImageTranslateResponse {
  Response: {
    Error?: {
      Code: string;
      Message: string;
    },
    SessionUuid: string
    Source: string
    Target: string
    ImageRecord: {
      Value: Array<{
        SourceText: string
        TargetText: string
        X: number
        Y: number
        W: number
        H: number
      }>
    }
    RequestId: string
  }
}

export class TencentTranslate extends BaseTranslate {
  constructor() {
    super()
  }

  /**
   * 生成腾讯云API的Authorization签名
   * @param secretId 密钥ID
   * @param secretKey 密钥Key
   * @param timestamp 时间戳
   * @param service 服务名称，默认为'tmt'
   * @param region 地域，默认为'ap-guangzhou'
   * @param action API动作
   * @param payload 请求体
   * @returns Authorization字符串
   */
  private generateAuthorization(
    secretId: string,
    secretKey: string,
    timestamp: number,
    service: string = 'tmt',
    region: string = 'ap-guangzhou',
    action: string,
    payload: string
  ): string {
    // 步骤1: 拼接规范请求串
    const httpRequestMethod = 'POST'
    const canonicalUri = '/'
    const canonicalQueryString = ''

    // 获取UTC日期 - 格式应该是 YYYY-MM-DD
    const date = new Date(timestamp * 1000).toISOString().slice(0, 10)

    // 构建规范请求头
    const host = `${service}.tencentcloudapi.com`
    const contentType = 'application/json; charset=utf-8'
    const canonicalHeaders = `content-type:${contentType}\nhost:${host}\nx-tc-action:${action.toLowerCase()}\n`
    const signedHeaders = 'content-type;host;x-tc-action'

    // 计算请求体哈希
    const hashedRequestPayload = SHA256(payload).toString()

    // 拼接规范请求串
    const canonicalRequest = [
      httpRequestMethod,
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      hashedRequestPayload
    ].join('\n')

    // 步骤2: 拼接待签名字符串
    const algorithm = 'TC3-HMAC-SHA256'
    const requestTimestamp = timestamp.toString()
    const credentialScope = `${date}/${service}/tc3_request`
    const hashedCanonicalRequest = SHA256(canonicalRequest).toString()

    const stringToSign = [
      algorithm,
      requestTimestamp,
      credentialScope,
      hashedCanonicalRequest
    ].join('\n')

    // 步骤3: 计算签名
    const kKey = HmacSHA256(date, 'TC3' + secretKey)
    const kService = HmacSHA256(service, kKey)
    const kSigning = HmacSHA256('tc3_request', kService)
    const signature = HmacSHA256(stringToSign, kSigning).toString()

    // 步骤4: 拼接Authorization
    const authorization = `${algorithm} Credential=${secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`

    return authorization
  }

  /**
   * 生成完整的请求头
   * @param config 配置信息
   * @param action API动作
   * @param payload 请求体
   * @returns 请求头对象
   */
  private generateHeaders(
    config: TencentTranslateConfig,
    action: string,
    payload: string
  ): Record<string, string> {
    const timestamp = Math.floor(Date.now() / 1000)
    const service = config.service || 'tmt'
    const region = config.region || 'ap-guangzhou'

    const authorization = this.generateAuthorization(
      config.secretId,
      config.secretKey,
      timestamp,
      service,
      region,
      action,
      payload
    )

    return {
      Authorization: authorization,
      'Content-Type': 'application/json; charset=utf-8',
      Host: `${service}.tencentcloudapi.com`,
      'X-TC-Action': action,
      'X-TC-Timestamp': timestamp.toString(),
      'X-TC-Version': '2018-03-21',
      'X-TC-Region': region
    }
  }

  /**
   * 图片翻译
   * @param imageBase64 Base64编码的图片数据
   * @param source 源语言
   * @param target 目标语言
   * @param config 配置信息
   * @returns 翻译结果
   */
  public async imageTranslate(
    imageBase64: string,
    source: string = 'auto',
    target: string = 'zh',
    config: TencentTranslateConfig
  ): Promise<ImageTranslateResponse> {
    if (!config.secretId || !config.secretKey) {
      throw new Error('腾讯翻译需要配置SecretId和SecretKey')
    }

    // 检查图片大小限制（7MB）
    const imageSize = Math.ceil(imageBase64.length * 0.75) // Base64编码会增加约33%的大小
    if (imageSize > 7 * 1024 * 1024) {
      throw new Error('图片大小不能超过7MB')
    }

    const action = 'ImageTranslate'
    const sessionUuid = `session-${Date.now()}`

    const requestData: ImageTranslateRequest = {
      SessionUuid: sessionUuid,
      Scene: 'doc', // 文档扫描场景
      Data: imageBase64,
      Source: source,
      Target: target,
      ProjectId: config.projectId || 0
    }

    const payload = JSON.stringify(requestData)
    const headers = this.generateHeaders(config, action, payload)

    try {
      const response = await fetch(`https://${config.service || 'tmt'}.tencentcloudapi.com`, {
        method: 'POST',
        headers,
        body: payload
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ImageTranslateResponse = await response.json()

      // 检查API返回的错误
      if (result.Response && 'Error' in result.Response) {
        throw new Error(`API Error: ${(result.Response as any).Error.Message}`)
      }

      return result
    } catch (error) {
      console.error('图片翻译请求失败:', error)
      throw error
    }
  }

  public async translate(app: App, config: TencentTranslateConfig) {
    // 这里实现具体的翻译逻辑
    if (!config.secretId || !config.secretKey) {
      throw new Error('腾讯翻译需要配置SecretId和SecretKey')
    }

    const dataUrl = await ScreenshotCapacity.exportScreenshotDataUrl(app)
    // 移除data:image/xxx;base64,前缀，只保留纯Base64字符串
    const base64 = dataUrl.replace(/^data:image\/\w+;base64,/, '')
    const res = await this.imageTranslate(base64, 'auto', 'zh', config);
    console.log('res', res)
    if (res.Response.Error) {
      const error = res.Response.Error;
      throw new TranslateError(error.Code, 'tencent', error.Code, error.Message);
    }

    if (!res.Response.ImageRecord ||  !res.Response.ImageRecord.Value || !res.Response.ImageRecord.Value.length) {
      throw new Error('未识别到内容');
    }
    const valueList = res.Response.ImageRecord.Value;
    for (const item of valueList) {
      const textContainer = this.getTextContainerDom({
        left: item.X,
        top: item.Y,
        content: item.TargetText
      });

      const width = item.W / devicePixelRatio
      const height = item.H / devicePixelRatio
      textContainer.style.width = `${width}px`
      textContainer.style.height = `${height}px`
      textContainer.style.lineHeight = `${height}px`;
      textContainer.style.fontSize = `12px`
      textContainer.style.backdropFilter = 'blur(20px)'
      textContainer.style.backgroundColor = 'rgb(255 255 255)'
      textContainer.style.color = '#000000'
      textContainer.style.textAlign = 'left'
    }
  }
}
