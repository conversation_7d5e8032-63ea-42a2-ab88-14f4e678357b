import {
  BaseTranslate,
  type BaseTranslateConfig,
  type ITranslateInfo,
  TranslateError
} from '../../translate/BaseTranslate'
import { App } from 'leafer-ui'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import MD5 from 'crypto-js/md5'
import { lib } from 'crypto-js'

import baiduApi from '@/api/baiduApi'
import { isArray } from '@/utils/jsUtil'

export interface BaiduTranslateConfig extends BaseTranslateConfig {
  apiKey: string
  secretKey: string
}
export const info: ITranslateInfo  = {
  name: '百度翻译',
  code: 'baidu'
}

async function calculateMd5(blob: Blob): Promise<string> {
  const arrayBuffer = await blob.arrayBuffer();
  const wordArray = MD5(lib.WordArray.create(arrayBuffer));
  return wordArray.toString();
}

/**
 * 百度翻译
 */
export class BaiduTranslate extends BaseTranslate {


  public async translate(app: App, config: BaiduTranslateConfig) {
    const blob = await ScreenshotCapacity.exportScreenshotBlob(app);
    const fileMd5 = await calculateMd5(blob);
    const mac = 'mac';
    const cuid = 'APICUID';
    const salt = (Math.floor(Math.random() * (10000000 - 1000001)) + 1000001).toString();
    const appid = config.apiKey;
    const sk = config.secretKey;
    const signString = `${appid}${fileMd5}${salt}${cuid}${mac}${sk}`;
    const sign = MD5(signString).toString();
    const res = await baiduApi.fyTranslateImage({mac, cuid, image: blob, sign, appid, salt});
    if (res.error_code !== '0') {
      throw new TranslateError(res.error_code, info.code, config.id, res.error_msg);
    }
    if (!res.data || !res.data.content || !isArray(res.data.content) &&  !res.data.content.length) {
      throw new Error('未识别到内容')
    }
    const contentList = res.data.content;
    for (const item of contentList) {
      const points = item.points;
      const textContainer = this.getTextContainerDom({
        left: points[0].x,
        top: points[0].y,
        content: item.dst
      });

      const width = (points[1].x - points[0].x) / devicePixelRatio
      const height = (points[3].y - points[0].y) / devicePixelRatio
      textContainer.style.width = `${width}px`
      textContainer.style.height = `${height}px`
      textContainer.style.lineHeight = `${height}px`;
      textContainer.style.fontSize = `12px`
      textContainer.style.backdropFilter = 'blur(20px)'
      textContainer.style.backgroundColor = 'rgb(255 255 255)'
      textContainer.style.color = '#000000'
      textContainer.style.textAlign = 'left'
    }
  }

}
