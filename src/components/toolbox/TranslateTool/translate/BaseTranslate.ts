import type { App } from 'leafer-ui'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'

type translateCodeType = 'aiBaidu'  | 'baidu' | 'tencent'
export interface BaseTranslateConfig {
  id?: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 平台唯一码
   */
  code: translateCodeType;
  /**
   * 创建时间
   */
  createAt: number;
}
export interface ITranslateInfo {
  code: translateCodeType;
  name: string;
}

export abstract class BaseTranslate {
  public abstract translate(app: App, config: BaseTranslateConfig): void;

  /**
   * 初始化文字容器样式
   */
  protected getTextContainerDom(options: { left: number; top: number; content: string }) {
    const dom = document.createElement('div')
    dom.style.position = 'fixed'
    dom.style.fontFamily = 'Times New Roman'
    // dom.style.color = 'transparent';
    dom.style.fontSize = '12px'
    dom.style.zIndex = '999'
    dom.style.userSelect = 'text'
    const pixelRatio = window.devicePixelRatio
    const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer)
    const __screenshotContainerRect = screenshotContainer.getBoundingClientRect()
    const paddingLeft = __screenshotContainerRect.left
    const paddingTop = __screenshotContainerRect.top
    dom.style.left = `${Math.ceil(options.left / pixelRatio + paddingLeft)}px`
    dom.style.top = `${Math.ceil(options.top / pixelRatio + paddingTop)}px`
    dom.innerHTML = `${options.content}`
    document.getElementById('translate').append(dom)
    return dom
  }
}

export class TranslateError extends Error {
  private readonly __platForm: string;
  private readonly __accountKey: string;
  private readonly __errorCode: string;


  constructor(errorCode: string, code: string, accountKey: string, msg: string) {
    super(msg);
    this.__platForm = code;
    this.__accountKey = accountKey;
    this.__errorCode = errorCode;
  }

  public get platform() {
    return this.__platForm;
  }
}
