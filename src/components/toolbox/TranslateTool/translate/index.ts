import type { ITranslateInfo } from './BaseTranslate'

export * from './BaseTranslate';
export * from './impl/AiBaiduTranslate';

function importToList(obj: Record<string, any>) {
  return Object.values(obj).map(item => item);
}

const translateInfoList: ITranslateInfo[] = importToList(import.meta.glob('./impl/*Translate.ts', {
  eager: true,
  import: 'info',
}));

export function getTranslateInfoList(): ITranslateInfo[] {
  return translateInfoList;
}
