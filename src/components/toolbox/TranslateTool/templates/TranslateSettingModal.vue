<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstanceFunctions } from 'tdesign-vue-next'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import type { TranslateSettingModalInstance } from '@/components/toolbox/TranslateTool/templates/TranslateSettingModal'
import { type BaseTranslateConfig, getTranslateInfoList } from '../translate'


const emits = defineEmits<{
  refresh: [];
}>();
const visible = ref(false);

const modalInfo = ref({
  title: '新建翻译源',
});

const initFormData = {
  code: '',
  name: '',
};

const translateInfoList = getTranslateInfoList();

const formData = ref<Record<string, string>>(initFormData);
function show(config?: BaseTranslateConfig) {
  if (config) {
    formData.value = config as any;
  } else {
    formData.value = {...initFormData};
  }
  visible.value = true;
}

const formRef = ref<FormInstanceFunctions>();

const toolConfigStore = useToolConfigStore()
async function handleSaveTranslateSource() {
  const success = await formRef.value.validate();
  console.log(formData.value);
  if (success !== true) {
    return false;
  }
  toolConfigStore.saveTranslateConfig(formData.value as any);
  visible.value = false;
}

function handleClose() {
  emits('refresh');
}

defineExpose<TranslateSettingModalInstance>({
  show
});
</script>

<template>
  <t-dialog v-model:visible="visible"
           :header="modalInfo.title"
           unmount-on-close
           @confirm="handleSaveTranslateSource"
           @closed="handleClose">
    <t-form :data="formData"
            ref="formRef">
      <t-form-item label="平台"
                   :rules="[{required: true}]"
                   name="code">
        <t-select v-model:value="formData.code">
          <t-option v-for="(item, index) in translateInfoList"
                    :key="index"
                    :value="item.code"
                    :label="item.name" />
        </t-select>
      </t-form-item>
      <t-form-item label="名称"
                   :rules="[{required: true}]"
                   name="name">
        <t-input v-model:value="formData.name"></t-input>
      </t-form-item>
      <div v-if="formData.code === 'aiBaidu' || formData.code === 'baidu'">
        <t-form-item label="API Key"
                     :rules="[{required: true}]"
                     name="apiKey">
          <t-input v-model:value="formData.apiKey"></t-input>
        </t-form-item>
        <t-form-item label="Secret Key"
                     :rules="[{required: true}]"
                     name="secretKey">
          <t-input type="password" v-model:value="formData.secretKey"></t-input>
        </t-form-item>
      </div>
      <div v-if="formData.code === 'tencent'">
        <t-form-item label="SecretId"
                     :rules="[{required: true}]"
                     name="secretId">
          <t-input v-model:value="formData.secretId"></t-input>
        </t-form-item>
        <t-form-item label="SecretKey"
                     :rules="[{required: true}]"
                     name="secretKey">
          <t-input type="password"
                   v-model:value="formData.secretKey"></t-input>
        </t-form-item>
      </div>
    </t-form>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
