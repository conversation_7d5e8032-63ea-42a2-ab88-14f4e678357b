<script setup lang="tsx">
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { computed, onMounted, ref, useTemplateRef } from 'vue'
import { type BaseTranslateConfig, getTranslateInfoList } from './translate'
import TranslateSettingModal from '@/components/toolbox/TranslateTool/templates/TranslateSettingModal.vue'
import type { TranslateSettingModalInstance } from '@/components/toolbox/TranslateTool/templates/TranslateSettingModal'
import type { TableProps } from 'tdesign-vue-next'
import  { Button, Popconfirm, Icon, Link } from 'tdesign-vue-next'

const props = defineProps<{
  sceneCode: string
}>();
const data = ref<BaseTranslateConfig[]>([]);

const toolConfigStore = useToolConfigStore();
const sceneActiveTranslateId = toolConfigStore.getSceneActiveTranslateId(props.sceneCode);

const translateInfoMap = computed(() => {
  return getTranslateInfoList().reduce((obj, cur) => {
    obj[cur.code] = cur.name;
    return obj;
  }, {} as Record<string, string>);
})
const translateSettingModalRef = useTemplateRef<TranslateSettingModalInstance>('translateSettingModalRef');
onMounted(() => {
  refreshData();
})
function refreshData() {
  data.value = toolConfigStore.getTranslateConfigList();
  if (sceneActiveTranslateId.value === undefined) {
    if (data.value.length >= 2) {
      // @ts-ignore
      sceneActiveTranslateId.value = data.value[0].id;
    }
  }
}
function handleDeleteTranslate(config: BaseTranslateConfig) {
  if (config.id === sceneActiveTranslateId.value) {
    sceneActiveTranslateId.value = undefined;
  }
  toolConfigStore.removeTranslateConfig(config.id);
  refreshData();
}

const translateColumns: TableProps['columns'] = [
  {
    // title: '单选',
    // align: 'center',
    colKey: 'row-select',
    type: 'single',
    width: 40,
  },
  {
    title: '平台',
    colKey: 'platform',
    width: 120,
    cell: (h, { row }) => {
      return (
        <div>
          {translateInfoMap.value[row.platform]}
        </div>
      );
    },
  },
  {
    title: '名称',
    colKey: 'name',
  },
  {
    title: '操作',
    colKey: 'active',
    width: 88,
    align: 'center',
    title: () => (
      <Button
        theme={'default'}
        size={'small'}
        onClick={() => translateSettingModalRef.value?.show()}
        v-slots={{
          icon: () => <Icon class="i-u-plus" />,
          default: () => '新增'
        }}
      />
    ),
    cell: (h, { row }) => {
      return (
        <div class="flex gap-2">
          <Popconfirm  content="确认删除吗"
                       theme="danger"
                       onConfirm={ () => handleDeleteTranslate(row) }>
            <Link size="small" theme="danger">
              删除
            </Link>
          </Popconfirm>

          <Link size="small" theme="primary"
                onClick={ () => translateSettingModalRef.value?.show(row)  }>详情</Link>
        </div>
      );
    },
  },
]
function handleSelectChange(value) {
  sceneActiveTranslateId.value = value[0];
}
</script>

<template>
  <TranslateSettingModal ref="translateSettingModalRef"
                         @refresh="refreshData" />
  <t-table size="small"
           class="u-web-table"
           row-key="id"
           :selectedRowKeys="[sceneActiveTranslateId]"
           :columns="translateColumns"
           :data="data"
           :pagination="null"
           hover
           disableDataPage
           selectOnRowClick
           @select-change="handleSelectChange"/>
</template>

<style scoped lang="less">

</style>
