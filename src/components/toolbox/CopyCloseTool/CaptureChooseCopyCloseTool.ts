import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import leaferConstant from '@/leaferApp/LeaferConstant'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'

export default class CaptureChooseCopyCloseTool extends BaseUseTool {
  constructor() {
    super()
  }
  protected doUse() {
    this.getEditor().cancel();
    const chooseAreaRect = this.getAppTree().findId(leaferConstant.ElementChooseAreaRect)!;
    const chooseAreaRectBounds = chooseAreaRect.getBounds();
    const dx = chooseAreaRectBounds.x + 4;
    const dy = chooseAreaRectBounds.y + 4;
    const dw = chooseAreaRectBounds.width - 6;
    const dh = chooseAreaRectBounds.height - 6;
    const ratio = window.devicePixelRatio;
    this.getApp().export('png', {
      quality: 1,
      screenshot: {
        x: dx * ratio, y: dy * ratio,
        width: dw, height: dh,
      } }).then(async (res) => {
        utools.copyImage(res.data);
        ScreenshotCapacity.screenshotClose();
    });
  }
}
