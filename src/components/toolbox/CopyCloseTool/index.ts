import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import CopyCloseToolBox from './CopyCloseToolBox.vue'
import CopyCloseTool from './CopyCloseTool'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'copyAndClose',
  title: '复制并关闭',
  sort: 102,
  group: 'action',
  instant: false,
  keyboard: {
    key: 'ENTER'
  },
  toolIcon: 'i-u-check-small',
  config: false
}


export default {
  info: CONFIG,
  tool: new CopyCloseTool(),
  component: async () => CopyCloseToolBox
} as ToolboxItem;
