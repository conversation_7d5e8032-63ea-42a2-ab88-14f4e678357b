import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'

export default class CopyCloseTool extends BaseUseTool {
  constructor() {
    super();
  }

  protected doUse() {
    this.getEditor().closeInnerEditor();
    this.getEditor().cancel();
    setTimeout(() => {
      ScreenshotCapacity.exportScreenshotDataUrl(this.getApp())
        .then(base64 => {
          utools.copyImage(base64);
          ScreenshotCapacity.screenshotClose()
            .then(() => {});
        });
    }, 50);
  }
}
