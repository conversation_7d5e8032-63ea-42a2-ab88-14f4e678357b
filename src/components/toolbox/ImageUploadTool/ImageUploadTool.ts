import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { NotifyPlugin } from 'tdesign-vue-next'
import PictureBedPlus from '@/api/service/PictureBedPlus'


export default class ImageUploadTool extends BaseUseTool {

  constructor() {
    super()
  }

  protected doUse(): void {
    const toolConfigStore = useToolConfigStore();
    ScreenshotCapacity.exportScreenshotDataUrl(this.getApp())
      .then(async (imageBase64: string) => {
        const { imageUploadType, uploadWay } = toolConfigStore.getImageUploadConfig;
        if (imageUploadType === 'plugIn') {
          utools.redirect(['图床 Plus', '图片上传'], {
            type: 'img',
            data: imageBase64
          });
        } else {
          ScreenshotCapacity.showTipsNotification('info', {
            content: '开始上传...',
          });
          try {
            const res: any = await PictureBedPlus.getImageUploadSync({
              base64: imageBase64,
              uploadWay,
            });
            if (!res.autoCopy) {
              utools.copyText(res.url);
            }
            ScreenshotCapacity.showTipsNotification('info', {
              content: '上传成功并复制链接'
            });
          }catch (e) {
            utools.redirect(['图床 Plus', '图片上传'], {
              type: 'img',
              data: imageBase64
            });
          }
        }
    })
  }

  private async imageUploadApi(imageBase64: string, imageServicePort: number) {
    const port = imageServicePort;
    const bed = '';
    try {
      const url = await fetch(`http://localhost:${port}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: bed
          ? `base64=${imageBase64}&autoCopy=true&bed=${bed}`
          : `base64=${imageBase64}&autoCopy=true`
      }).then(res => res.text());
      utools.copyText(url);
      NotifyPlugin.success({
        title: '提示',
        placement: 'top-left',
        content: '上传并复制成功',
        duration: 3 * 1000,
        closeBtn: true,
      });
    }catch (e) {
      NotifyPlugin.warning({
        title: '提示',
        placement: 'top-left',
        content: '上传失败',
        duration: 3 * 1000,
        closeBtn: true,
      });
    }
  }
}
