import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import ImageUploadToolBox from './ImageUploadToolBox.vue'
import ImageUploadTool from './ImageUploadTool'
import ImageUploadSceneSetting from './ImageUploadSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'imageUpload',
  title: '上传图床',
  sort: 3,
  group: 'tool',
  keyboard: {
    key: ''
  },
  instant: false,
  toolIcon: 'i-u-image-upload',
  config: false,
  sceneSetting: async () => ImageUploadSceneSetting,
}

export default {
  info: CONFIG,
  tool: new ImageUploadTool(),
  component: async () => ImageUploadToolBox,
} as ToolboxItem;
