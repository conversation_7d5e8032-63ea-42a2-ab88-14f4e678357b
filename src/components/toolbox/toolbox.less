.arco-tooltip-content {
  padding: 7px !important;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.04) 0 3px 5px !important;
}

// 工具图标
.toolbox {
  box-sizing: border-box;
  display: flex;
  padding: 7px;
  font-size: 16px;
  &.medium {
    font-size: 20px;
  }
  border-radius: 10px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 300ms ease-in-out;
  &:hover {
    background: var(--u-hover-color);
  }
  &.active {
    background: var(--color-fill-3);
    color: var(--select-selested-text-color);
  }
}

.tooltip-content {
  padding: 10px;
  background: var(--utools-background);
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  .tooltip-title {
    font-size: 12px;
    font-weight: 500;
  }
  .label {
    font-size: 14px;
    color: var(--text-color);
  }
}
.toolbox-config {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 8px;
  margin-top: 5px;
}

.toolbox-form-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 12px;
}

.toolbox-trigger {
  box-sizing: border-box;
  background: var(--main-background);
  //box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  border-radius: 0 0 10px 10px;
}
.toolbox-tooltip {
  font-size: 12px;
}

// 工具条
.toolbox-bar {
  background: var(--main-background);
  padding: 6px;
  border-radius: 10px;
}
// form-item
.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 12px;
  > div:first-child {
    width: 60px;
  }
}
