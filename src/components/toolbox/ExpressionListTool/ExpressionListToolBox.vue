<script setup lang="ts">

import { CONFIG } from './index'
import type ExpressionListTool from './ExpressionListTool'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import { computed, ref, watch } from 'vue'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { nanoid } from 'nanoid'
import { type IExpressionUserItem, useExpressionListStore } from '@/stores/ExpressionListStore/ExpressionListStore'
import { useScroll } from '@vueuse/core'
import fileResourceApi from '@/api/ucloud/fileResourceApi'
import DataUtils from '@/utils/DataUtils'
import HttpUtils from '@/utils/HttpUtils'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import FileUtils from '@/utils/FileUtils'

// ---------------------tab-------------------------
const switchButton = ref({
  left: false,
  right: false,
});
const toolboxTabRef = ref<HTMLDivElement>();
const toolTabWidth = computed(() => {
  let baseWidth = 180;
  if (!switchButton.value.right) {
    baseWidth += 32.5;
  }
  if (!switchButton.value.left) {
    baseWidth += 32.5;
  }
  return baseWidth;
});

function handlePrefix() {
  if (!toolboxTabRef.value) {
    return;
  }
  const element = toolboxTabRef.value;
  element.scrollLeft -= 32;
}
function handleNext() {
  if (!toolboxTabRef.value) {
    return;
  }
  const element = toolboxTabRef.value;
  element.scrollLeft += 32;
}
function refreshSwitchButton() {
  if (!toolboxTabRef.value) {
    return;
  }
  const element = toolboxTabRef.value;
  if (toolboxTabRef.value.scrollWidth > 0) {
    switchButton.value.left = toolboxTabRef.value.scrollLeft > 0;
    switchButton.value.right = element.scrollWidth - element.scrollLeft > element.clientWidth;
  }
}
// -----------------------------------------------------

// 打开配置弹框
function handleOpenConfig() {
  refreshInstallExpressionPackage();
  getExpressionList(1).then(() => {});
  setTimeout(() => {
    refreshSwitchButton();
    toolboxTabRef.value.addEventListener('scroll', refreshSwitchButton);
  });
}

function handleCloseConfig() {
  toolboxTabRef.value.removeEventListener('scroll', refreshSwitchButton);
}

const currentToolBoxStore = useCurrentToolBoxStore();

function handleExpressionClick(imageUrl: string) {
  const element = document.getElementById(imageUrl) as HTMLImageElement;
  if (element) {
    const tool = currentToolBoxStore.getToolByCode<ExpressionListTool>(CONFIG.code)!;
    tool.addImage(element.src);
  }
}

export interface ExpressionTypeItem {
  code: string;
  title: string;
  iconType: 'iconPark' | 'image';
  icon: string;
  path: string;
  business?: boolean;
}


const defaultTabList: ExpressionTypeItem[] = [{
  code: 'like',
  title: '我到收藏',
  iconType: 'iconPark',
  icon: 'i-u-like',
  path: 'utools:attachment://expression/userLike',
},{
  code: 'application',
  title: '图包',
  iconType: 'iconPark',
  icon: 'i-u-application',
  path: 'utools:db://expression/package',
}];
const tabList = ref<ExpressionTypeItem[]>([...defaultTabList]);

const tabActive = ref('like');
const activeTabItem = computed(() =>
  tabList.value.find(item => item.code === tabActive.value));

const expressionListStore = useExpressionListStore();

function handleSelectActiveTab({ code }: ExpressionTypeItem) {
  tabActive.value = code;
  console.log('tabActive', tabActive.value);
  getExpressionList(1).then(() => {});
}

const pageData = ref<{
  page: number,
  list: IExpressionUserItem[],
}>({
  page: 1,
  list: [],
});

function getPageUploadUserLike(page: number) {
  return expressionListStore.getPageExpressionUserList(page);
}

function getPageExpressionPackage(page: number) {
  const activeItem = activeTabItem.value;
  const pageSize = 16;
  const startIndex = (page - 1) * pageSize;
  return window.fs.readdirSync(activeItem.path)
    .slice(startIndex, startIndex + pageSize)
    .filter(item => item.startsWith(activeItem.code.split(":")[1]))
    .map((filename) => {
      return {
        imageUrl: `${window.path.join(activeItem.path, filename)}`,
      }
    });
}

async function getExpressionList(page = 1) {
  let originList = page === 1 ? [] : pageData.value.list;
  let result = [] as any;
  if (tabActive.value === 'like') {
    result = getPageUploadUserLike(page);
    console.log('like', result);
  } else if (tabActive.value.startsWith('package')) {
    result = getPageExpressionPackage(page);
  }
  console.log(result);
  pageData.value = {
    page,
    list: [...originList, ...result],
  }
}

const userLikeRef = ref<HTMLDivElement>();
const { arrivedState } = useScroll(userLikeRef, {
  offset: {  bottom: 30 },
});

watch(() => arrivedState.bottom, (val) => {
  if (val) {
    getExpressionList(pageData.value.page + 1);
  }
});

const groupRef = ref<HTMLDivElement>();

const groupScroll= useScroll(groupRef, {
  offset: {  bottom: 30 },
});

watch(() => groupScroll.arrivedState.bottom, (val) => {
  if (val) {
    getExpressionList(pageData.value.page + 1);
  }
});

function handleUploadUserLike() {
  const paths = utools.showOpenDialog({
    filters: [{ name: 'image', extensions: ['png', 'svg', 'jpg', 'jpeg', 'webp'] }],
    properties: ['openFile']
  });

  if (!paths || !paths.length) {
    return;
  }

  for (const path of paths) {
    const fileStat = window.fs.statSync(path);
    if (fileStat.size > 10 * 1024 * 1024) {
      ScreenshotCapacity.showTipsNotification('warning', {
        title: '文件大小限制',
        content: '上传素材的文件大小不能超过10MB'
      });
      continue;
    }
    const extname = window.path.extname(path)
    const nativeImage = window.nativeImage.createFromPath(path);
    const base64 = nativeImage.toDataURL();
    const buffer =  new window.nodeBuffer.Buffer(base64.replace(/^data:image\/\w+;base64,/, ''), 'base64');
    const docId = `expression/userLike/${nanoid(36)}${extname}`;
    utools.db.postAttachment(docId, buffer, 'image');
    expressionListStore.addExpressionUserList({
      imageUrl: `utools:attachment://${docId}`,
      createTime: Date.now()
    });
  }
  getExpressionList(1)
    .then(() => {});
}
function handleDeleteUserLike(imageUrl: string) {
  expressionListStore.removeExpressionUserList(imageUrl);
  const idx = pageData.value.list.findIndex(item => item.imageUrl === imageUrl);
  if (idx !== -1) {
    pageData.value.list.splice(idx, 1);
    if (pageData.value.list.length < 20) {
      // 删除内容少于第一页数量刷新分页
      getExpressionList(1)
          .then(() => {});
    }
  }
}

const installExpressionPackage = ref([]);
function refreshInstallExpressionPackage() {
  const result = window.fs.readdirSync(DataUtils.getDataExpressionPackagePath()) || [];
  const expressionPackages = result.filter(item => !item.startsWith('.'));
  installExpressionPackage.value = [];
  tabList.value = [...defaultTabList];
  for (const expressionPackage of expressionPackages) {
    const configPath = window.path.join(DataUtils.getDataExpressionPackagePath(), expressionPackage, 'config.json');
    console.log('configPath', configPath)
    const existsConfigFile = window.fs.existsSync(configPath);
    if (!existsConfigFile) {
      continue;
    }
    const config = JSON.parse(window.fs.readFileSync(configPath, 'utf8')) as any
    tabList.value.push({
      iconType: 'image',
      icon: window.path.join(window.path.dirname(configPath), config.cover),
      code: 'package:' + config.code,
      path: window.path.dirname(configPath),
      title: config.title,
      business: config.business || false
    });
    installExpressionPackage.value.push(expressionPackage);
  }
}


async function handleInstallExpression(resourceCode: string) {
  const expressionPackage = expressionPackageList.value.find(item => item.resourceCode === resourceCode);
  expressionPackage.loading = true;
  const resource = await fileResourceApi.getResource(resourceCode);
  const filePreview = resource.filePreviewUrl;
  const previewUrl = filePreview.previewUrl;
  const fileName = filePreview.fileName;
  const filePath = window.path.join(DataUtils.getDataExpressionPackagePath(), fileName);
  try {
    await HttpUtils.downloadFile(previewUrl, filePath);
    await FileUtils.unTar(filePath,
      window.path.join(window.path.dirname(filePath), fileName.replace(window.path.extname(fileName), '')),
      true);
    refreshInstallExpressionPackage();
  }catch (e) {
    console.error(e);
    ScreenshotCapacity.showTipsNotification('warning', {
      title: '提醒',
      content: '下载失败, 重新下载或检查网络'
    });
  }finally {
    expressionPackage.loading = false;
  }
}
async function handleUninstallExpression(resourceCode: string) {
  const code = resourceCode.split(':')[1];
  try {
    const fileDir = window.path.join(DataUtils.getDataExpressionPackagePath(), code);
    window.fs.rmSync(fileDir, {recursive: true});
  } finally {
    refreshInstallExpressionPackage();
  }
}

const expressionPackageList = ref([{
  title: '小鸟',
  resourceCode: 'expression:birdie',
  cover: 'https://on-u.cn/upload/时尚小鸟.svg',
  loading: false,
  business: true,
}]);
</script>

<template>
  <BaseToolBox v-bind="CONFIG"
               @openConfig="handleOpenConfig"
               @closeConfig="handleCloseConfig">
    <template #config>
      <div class="toolbox-config">
        <div class="toolbox-tab u-fx u-gap5">
          <t-button class="switch-button"
                    v-if="switchButton.left"
                    @click="handlePrefix">
            <t-icon class="i-u-arrow-left"></t-icon>
          </t-button>
          <div class="u-fx u-gap5 select-content"
               ref="toolboxTabRef"
               :style="{minWidth: `${toolTabWidth}px`, maxWidth: `${toolTabWidth}px`}">
            <div class="classify"
                 :class="tabActive === item.code ? ['active'] : []"
                 v-for="(item, index) in tabList"
                 :key="index" @click="handleSelectActiveTab(item)">
              <div  v-if="item.iconType === 'iconPark'"
                    :class="item.icon"
                    class="w-5.5 h-5.5" />
              <img v-else
                   style="width: 28px; height: 28px;"
                   :src="item.icon"
                   alt="" />
            </div>
            <div class="classify">

            </div>
          </div>
          <t-button class="switch-button"
                    v-if="switchButton.right"
                    @click="handleNext">
            <template #icon>
              <div class="i-u-arrow-right w-4 h-4"></div>
            </template>
          </t-button>
        </div>
        <div>
          <!-- toolBox 列表 -->
          <div v-if="tabActive === 'application'" class="u-fx u-gap10 toolbox-list">
            <div class="u-fx u-fac item"
                 v-for="(item) in expressionPackageList" :key="item.resourceCode">
              <div class="u-fx u-fac u-gap10">
                <div>
                  <a-avatar shape="square"
                            :size="32"
                            style="padding-bottom: 2px;"
                            :image-url="item.cover">
                  </a-avatar>
                </div>
                <div>{{item.title}}</div>
              </div>
              <div>
                <a-button v-if="!installExpressionPackage.includes(item.resourceCode.split(':')[1])"
                          size="mini"
                          shape="round"
                          :loading="item.loading"
                          @click="() => handleInstallExpression(item.resourceCode)">
                  <template #icon>
                    <icon-download />
                  </template>
                  下载
                </a-button>
                <a-button v-else
                          size="mini"
                          shape="round"
                          @click="() => handleUninstallExpression(item.resourceCode)">
                  <template #icon>
                    <icon-close />
                  </template>
                  卸载
                </a-button>
              </div>
            </div>
          </div>
          <!-- 用户收藏列表 -->
          <div class="user-like" v-else-if="tabActive === 'like'">
            <div class="toolbox-body" ref="userLikeRef">
              <div class="u-fx u-fc u-fac u-pointer item"
                   @click="handleUploadUserLike"
                   style="color: #b2bec3">
                <iconpark-icon name="upload" size="28" />
              </div>
              <div class="u-fx u-fc u-fac u-pos-rel item"
                   v-for="item in pageData.list" :key="item.imageUrl"
                   @click="handleExpressionClick(item.imageUrl)">
                <div class="u-fx u-fc u-fac action">
                  <div class="u-fx u-fc u-fac" @click.stop="handleDeleteUserLike(item.imageUrl)">
                    <icon-delete size="12" style="color: #e74c3c" />
                  </div>
                </div>
                <img style="width: 100%; height: 100%;"
                     v-imageUrl="item.imageUrl"
                     alt="" />
              </div>
            </div>
            <div class="u-tips"
                 style="text-align: center; padding-top: 5px;">
              借助 utools 数据同步功能实现多设备同步
            </div>
          </div>
          <!-- toolBox Group -->
          <div v-else>
            <div ref="groupRef" class="toolbox-body">
              <div class="item"
                   v-for="item in pageData.list" :key="item.imageUrl"
                   @click="handleExpressionClick(item.imageUrl)">
                <img style="width: 100%; height: auto;"
                     :id="item.imageUrl"
                     :src="item.imageUrl"
                     alt="" />
              </div>
            </div>
            <div v-if="activeTabItem.business" class="u-tips"
                 style="text-align: center; padding-top: 5px;">
              素材具有商业授权
            </div>
          </div>
       </div>

     </div>
   </template>
 </BaseToolBox>
</template>

<style scoped lang="less">
.toolbox-config {
  box-sizing: border-box;
  padding: 4px;
}
:deep(.arco-list-item) {
 padding: 0 !important;
}
:deep(.arco-list-bordered) {
 border: none !important;
}
:deep(.arco-list-split .arco-list-header) {
 border: none !important;
}
:deep(.arco-list-split .arco-list-item) {
 border: none !important;
}
.toolbox-tab {
 flex-shrink: 1;
 .select-content {
   overflow-x: auto;
   &::-webkit-scrollbar {
     display: none;
   }
   .classify {
     width: 32px;
     height: 32px;
     display: flex;
     justify-content: center;
     align-items: center;
     cursor: pointer;
     position: relative;
   }
   .active::after {
     content: '';
     position: absolute;
     left: 0;
     bottom: -1px;
     width: 32px;
     height: 2px;
     border-radius: 32px;
     background: rgba(52, 152, 219, 1);
   }
 }
}

.toolbox-body, .toolbox-list {
  height: 170px;
  max-height: 170px;
  overflow-y: auto;
}
.toolbox-body {
 display: grid;
 grid-template-columns: repeat(auto-fill, 50px);
 gap: 10px;
 .item {
   flex-shrink: 1;
   width: 100%;
   height: 50px;
   cursor: pointer;
   &:hover {
     .action {
       opacity: 1;
     }
   }
   .action {
     position: absolute;
     right: 0;
     top: 0;
     opacity: 0;
     transition: all 250ms linear;
     >div {
       width: 16px;
       height: 16px;
       background: rgba(0, 0, 0, .4);
       border-radius: 50%;
       transition: all 250ms linear;
       &:hover {
         background: rgba(0, 0, 0, .6);
       }
     }
   }
 }
}
.toolbox-list {
 flex-direction: column;
 .item {
   justify-content: space-between;
 }
}
</style>
