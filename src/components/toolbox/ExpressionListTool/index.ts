import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import ExpressionListTool from './ExpressionListTool'
import ExpressionListToolBox from './ExpressionListToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'shape',
  title: '素材库',
  sort: 3,
  group: 'tool',
  keyboard: {
    key: ''
  },
  instant: false,
  toolIcon: 'i-u-triangle-round-rectangle',
  config: true,
}

export default {
  info: CONFIG,
  tool: new ExpressionListTool(),
  component: async () => ExpressionListToolBox
} as ToolboxItem;
