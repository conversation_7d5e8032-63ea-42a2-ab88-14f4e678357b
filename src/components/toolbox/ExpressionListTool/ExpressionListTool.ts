import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { Image, ImageEvent } from 'leafer-ui'
// 引入监听是否进入视口
export default class ExpressionListTool extends BaseUseTool {

  constructor() {
    super()
  }

  protected doUse(): void {

  }

  public addImage(url: string) {
    this.recordHistory();
    this.applyHistory();
    const image = new Image({
      url,
      editable: true,
      pixelRatio: window.devicePixelRatio,
    });
    image.on(ImageEvent.LOADED, (e: ImageEvent) => {
      const appBounds = this.getApp().getBounds();
      const maxWidth = appBounds.width / 2;
      const maxHeight = appBounds.height / 2;
      const {width, height} = e.image;
      if (width > maxWidth || height > maxHeight) {
        const widthRatio = maxWidth / width;
        const heightRatio = maxHeight / height;
        image.set({
          scale: Math.min(widthRatio, heightRatio),
        });
      }
    });
    image.load();
    this.getMiddleGroup().add(image);
    this.getEditor().select(image);
  }
}
