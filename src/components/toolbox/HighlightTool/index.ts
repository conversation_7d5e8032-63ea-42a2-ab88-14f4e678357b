import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import HighlightTool from './HighlightTool'
import HighlightToolBox from './HighlightToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'highlight',
  title: '高亮',
  sort: 7,
  group: 'draw',
  keyboard: {
    key: ''
  },
  instant: false,
  toolIcon: 'i-u-highlight',
  config: true,
}

export default {
  info: CONFIG,
  tool: new HighlightTool(),
  component: async () => HighlightToolBox
} as ToolboxItem;
