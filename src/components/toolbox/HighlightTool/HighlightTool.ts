import { BaseDrawTool } from '@/components/toolbox/BaseDrawTool'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import { Box, Group, type PointerEvent, Rect, ZoomEvent } from 'leafer-ui'

export default class HighlightTool extends BaseDrawTool<Rect> {

  constructor() {
    super(Rect)
  }


  doInstall() {
    this.getAppTree().on(ZoomEvent.ZOOM, (e: ZoomEvent) => {
      if (!this.getHighlightGroup()) {
        return
      }
      const group = this.getHighlightGroup()
      const pagePoint = this.getAppTree().getPagePoint({ x: 0, y: 0 });
      console.log(pagePoint)
      group.set({
        x: pagePoint.x,
        y: pagePoint.y,
        width: group.width * e.scale,
        height: group.height * e.scale
      })
    })
  }

  protected doUse() {
    const children = this.getOrCreateHighlightGroup()?.children || []
    children.map(item => {
      item.set({
        hitFill: 'all',
        hittable: true
      })
    })
  }

  doMouseDown(e: PointerEvent): any {
    const box = this.getOrCreateHighlightGroup()
    const word = { x: e.x, y: e.y }
    const innerPoint = this.getOrCreateHighlightGroup().getInnerPoint(word)
    console.log('innerPoint', innerPoint)
    const eraser = new Rect({
      ...innerPoint,
      width: 0,
      height: 0,
      fill: 'black',
      eraser: true,
      // draggable: true,
      editable: true,
      hitFill: 'all',
      cornerRadius: [10, 10, 10, 10]
    });
    box.add(eraser)
    this.ui = eraser
    // return eraser
  }

  doMouseMove(e: PointerEvent) {
    const word = { x: e.x, y: e.y }
    const innerPoint = this.getOrCreateHighlightGroup().getInnerPoint(word)
    const ui = this.ui!
    const width = innerPoint.x - ui.x
    const height = innerPoint.y - ui.y
    ui.set({
      scaleX: width > 0 ? 1 : -1,
      scaleY: height > 0 ? 1 : -1,
      width: Math.abs(width),
      height: Math.abs(height)
    })
  }

  protected doDestroy() {
    const box = this.getAllHighlights()
    if (!box.length) {
      this.getHighlightGroup().remove()
    } else {
      this.getHighlightGroup().children.map(item => {
        item.set({
          hitFill: 'none',
          hittable: false
        })
      })
    }
  }

  protected getHighlightGroup() {
    return this.getAppTree().findId(LeaferConstant.ID_HIGHLIGHT_GROUP)
  }

  protected getOrCreateHighlightGroup() {
    let highLightGroup = this.getHighlightGroup()
    if (!highLightGroup) {
      const bounds = this.getApp().getBounds('render');
      const pagePoint = this.getApp().getPagePoint(bounds);
      highLightGroup = new Group({
        x: pagePoint.x,
        y: pagePoint.y,
        id: LeaferConstant.ID_HIGHLIGHT_GROUP,
        width: bounds.width * window.devicePixelRatio,
        height: bounds.height  * window.devicePixelRatio
      });
      this.getAppTree().addBefore(highLightGroup,
        this.getAppTree().findId(LeaferConstant.ElementDrawGroup))

      const box = new Box({
        id: LeaferConstant.ID_HIGHLIGHT_BOX,
        x: 0,
        y: 0,
        width: highLightGroup.width,
        height: highLightGroup.height,
        fill: 'rgba(0, 0, 0, 0.5)',
        hitSelf: false
      });
      highLightGroup.add(box)

      const eraserGroup = new Group({
        id: LeaferConstant.ID_HIGHLIGHT_ERASER_GROUP,
        x: 0,
        y: 0,
        eraser: true
      })
      highLightGroup.add(eraserGroup)
    }
    return highLightGroup
  }

  private getAllHighlights() {
    return this.getOrCreateHighlightGroup()
      .children.filter(item => item.id !== LeaferConstant.ID_HIGHLIGHT_BOX && item.id !== LeaferConstant.ID_HIGHLIGHT_ERASER_GROUP)
  }

  clearAllHighlights() {
    this.getEditor().cancel()
    this.getAllHighlights().forEach(item => item.remove())
  }
}
