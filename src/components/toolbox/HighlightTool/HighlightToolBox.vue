<script setup lang="ts">
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import { CONFIG } from './index'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import type HighlightTool from './HighlightTool'

const currentToolBoxStore = useCurrentToolBoxStore();
const tool = currentToolBoxStore.getToolByCode<HighlightTool>(CONFIG.code)!;

function handleClearAllHighlights(): void {
  tool.clearAllHighlights();
}
</script>

<template>
  <BaseToolBox v-bind="CONFIG">
    <template #config>
      <div class="toolbox-config">
        <div class="u-font-size-smail u-tips">当没有高亮, 切换工具将自动移除背景</div>
<!--        <div class="form-item">
          <div>模式</div>
          <div>
            <a-radio-group type="button" size="mini">
              <a-radio>
                <iconpark-icon name="rectangle-one" />
              </a-radio>
              <a-radio>
                <iconpark-icon name="round" />
              </a-radio>
            </a-radio-group>
          </div>
        </div>-->
        <t-button size="small"
                  theme="default"
                  @click="handleClearAllHighlights">
          清理高亮
        </t-button>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">

</style>
