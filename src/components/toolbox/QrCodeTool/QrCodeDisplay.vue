<script setup lang="ts">
import { computed, ref } from 'vue'
import screenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'

const props = defineProps<{
  content: string;
  left: number,
  top: number;
  width: number;
  height: number;
}>();

function openUrl() {
  window.utools.shellOpenExternal(props.content);
}

function copyContent() {
  utools.copyText(props.content);
  screenshotCapacity.showTipsNotification('success', {
    title: '提示',
    content: '复制成功',
  })
}
const type = computed(() => {
  if (/.*:\/\/.*./.exec(props.content)) {
    return 'link';
  }
  return 'txt';
});
const textTrigger = ref(false);

const { getLeaferApp } = useCurrentToolBoxStore();
const textContentStyle = computed(() => {
  return {
    width: `${getLeaferApp().width - 100}px`,
  }
})
</script>

<template>
  <div v-if="type === 'link'"
       class="u-fx u-fac u-fc u-pos-abs qrcode-link"
       :style="{ left: left + 'px', top: top + 'px', width: width + 'px', height: height + 'px' }">
    <div class="btn" @click="openUrl">
      <iconpark-icon name="link-two"></iconpark-icon>
    </div>
  </div>
  <div v-else-if="type === 'txt'"
       class="u-fx u-fac u-fc u-pos-abs qrcode-text"
       :style="{ left: left + 'px', top: top + 'px', width: width + 'px', height: height + 'px' }">
    <a-trigger v-model:popup-visible="textTrigger"
               :popup-offset="3"
               unmount-on-close>
      <template #content>
        <div :style="{...textContentStyle}"
             class="qrcode-text-content"
             v-html="content" />
      </template>
      <div>
        <a-button v-if="!textTrigger" size="large" type="primary" shape="round">
          <template #icon>
            <iconpark-icon name="add-text-two"></iconpark-icon>
          </template>
        </a-button>
        <a-button v-else size="large" type="primary"
                  shape="round"
                  @click="copyContent()">
          <template #icon>
            <iconpark-icon name="copy"></iconpark-icon>
          </template>
        </a-button>
      </div>
    </a-trigger>
  </div>
</template>
<style lang="less">
.qrcode-text-content {
  padding: 10px;
  background: var(--main-ui-background);
  border-radius: 10px;
  white-space: pre-wrap;
}
</style>
<style scoped lang="less">
.qrcode-link {
  z-index: 666;
  left: 100px;
  top: 100px;
  .btn,
  .btn:hover,
  .btn:focus {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    width: 32px;
    height: 32px;
    padding: 8px;
    border-radius: 50%;
    border: none;
    background: #0078d4;
    color: var(--text-color);
    font-size: 1.5rem;
    transition: box-shadow 400ms cubic-bezier(0.2, 0, 0.7, 1), transform 200ms cubic-bezier(0.2, 0, 0.7, 1);
    cursor: pointer;
  }
  .btn:hover {
    transform: rotate(45deg);
    box-shadow: 0 0 1px 15px rgba(0, 90, 158, 0.4), 0 0 1px 30px rgba(0, 90, 158, 0.1), 0 0 1px 45px rgba(0, 90, 158, 0.1);
  }
}

.qrcode-text {
  z-index: 666;
}
</style>
