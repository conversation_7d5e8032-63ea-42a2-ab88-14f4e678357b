import { cloneDeep } from 'es-toolkit'

export interface ScriptItem {
  scriptId: string;
  /**
   * 脚本名称
   */
  scriptName: string;
  /**
   * 脚本内容
   */
  scriptContent: string;
  /**
   * 脚本参数
   */
  scriptArgs?: {
    params: string;
    value: string;
  }[];
}

const allScript: ScriptItem[] = [
  {
    scriptId: '0',
    scriptName: '获取格式化时间',
    scriptContent: 'this.dayjs().format(args.timeFormat)',
    scriptArgs: [
      {
        params: 'timeFormat',
        value: 'YYYY-MM-DD HH:mm:ss'
      }
    ]
  }
] as ScriptItem[];

/**
 * 获取脚本
 * @param scriptId
 */
function getScript(scriptId: string): ScriptItem {
  return cloneDeep(allScript.find(item => item.scriptId === scriptId));
}

export default {
  getScript,
}
