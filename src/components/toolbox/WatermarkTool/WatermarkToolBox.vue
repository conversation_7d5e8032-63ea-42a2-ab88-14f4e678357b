<script setup lang="ts">
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import { CONFIG } from './index'
import { computed, ref, toRefs, watch } from 'vue'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import WatermarkTool, { type WatermarkData } from './WatermarkTool'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import AngleInput from '@/components/common/AngleInput/AngleInput.vue'
import type { IUI } from '@leafer-ui/interface'
import type { ISingleWatermarkConfig } from '@/stores/ToolConfigStore/store/WatermarkToolConfigStore'
import dayjs from 'dayjs'

const toolInfo: ToolboxInfoItemConfig = CONFIG;

const { singleWatermarkConfig, tiledWatermarkConfig } = toRefs(useToolConfigStore());
const currentToolBoxStore = useCurrentToolBoxStore()
const tool = currentToolBoxStore.getToolByCode<WatermarkTool>(toolInfo.code)!;

const disabled = computed(() => {
  return tool.getSelectUI.value === null;
});
const watermarkMode = ref<'single' | 'tiled'>('tiled');
watch(() => disabled.value, (newValue) => {
  if (newValue) {
    watermarkMode.value = 'tiled';
  } else {
    watermarkMode.value = 'single';
  }
}, { deep: true });

watch(() => tool.getSelectUI.value, (newValue) => {
  if (newValue) {
    const val = newValue as IUI;
    if (val) {
      if (tool.isSingleWatermark(val)) {
        const data = val.getAttr('data') as WatermarkData;
        singleWatermarkConfig.value = data.config as ISingleWatermarkConfig;
      } else {
        singleWatermarkConfig.value = {
          opacity: 100,
        } as ISingleWatermarkConfig;
      }
    }
  }
});

function handleConfirmWatermark() {
  tool.confirmWatermark();
}
function handleCancelWatermark() {
  tool.cancelWatermark();
}

function handleFocus() {
  window.winHelper.getCurrentWindow().setAlwaysOnTop(false);
}
function handleBlur() {
  window.winHelper.getCurrentWindow().setAlwaysOnTop(true, 'modal-panel');
}
function handleOpenConfig() {
  if (disabled.value) {
    watermarkMode.value = 'tiled';
  } else {
    watermarkMode.value = 'single';
  }
}
const isScreenshotEnv = computed(() => window.environment === 'screenshot');

const isTiled = computed(() => watermarkMode.value === 'tiled');
const isSingle = computed(() => watermarkMode.value === 'single');

// 添加特殊水印
function handleAddSpecialWatermark() {
  tool.addSpecialWatermark();
}
</script>

<template>
  <BaseToolBox v-bind="toolInfo" @open-config="handleOpenConfig">
    <template #icon>
      <div :class="toolInfo.toolIcon"></div>
    </template>
    <template #config>
      <div class="toolbox-config">
        <div class="form-item">
          <div>模式</div>
          <div class="u-fx u-gap5 toolbox-width-item">
            <t-radio-group type="button"
                           variant="default-filled"
                           size="small"
                           v-model:model-value="watermarkMode">
              <t-tooltip>
                <template #content>
                  <span v-if="!isScreenshotEnv">目前不支持当前截图方式</span>
                  <span v-else>请先选择作为水印的元素</span>
                </template>
                <t-radio-button value="single" :disabled="disabled">单个</t-radio-button>
              </t-tooltip>
              <t-radio-button value="tiled" :disabled="!disabled">平铺</t-radio-button>
            </t-radio-group>
            <t-popup overlayClassName="u-web-popup"
                     placement="right">
              <template #content>
                <div class="toolbox-config toolbox-sub-config">
                  <div class="u-fx u-fac u-f-between u-gap5">
                    <div class="u-font-size-smail">时间水印</div>
                    <div>
                      <t-button size="small"
                                theme="default"
                                @click="handleAddSpecialWatermark">
                        <div class="i-u-plus w-3 h-3"></div>
                      </t-button>
                    </div>
                  </div>
                </div>
              </template>
              <t-tooltip content="特殊水印">
                <t-button size="small"
                          theme="default">
                  <div class="i-u-effects"></div>
                </t-button>
              </t-tooltip>
            </t-popup>
          </div>
        </div>
        <div v-if="isTiled" class="form-item" >
          <div>内容</div>
          <t-input size="small"
                   theme="default"
                   class="toolbox-width-item"
                   v-model:value="tiledWatermarkConfig.text"
                   clearable
                   @focus="handleFocus"
                   @blur="handleBlur"  />
        </div>
        <div v-if="isTiled" class="form-item">
          <div>间距</div>
          <t-input-number v-model:value="tiledWatermarkConfig.gap"
                          class="toolbox-width-item"
                          size="small"
                          theme="column"
                          :min="0" />
        </div>
        <div v-if="isTiled" class="form-item">
          <div>角度</div>
          <angle-input v-model:angle="tiledWatermarkConfig.rotation"
                       size="mini">
          </angle-input>
        </div>
        <div v-if="isTiled" class="u-mb12">
          <stroke-select-panel v-model:model-value="tiledWatermarkConfig.fillId"
                               :stroke-select-options="tiledWatermarkConfig.fillSelectOptions" />
        </div>
        <div class="form-item">
          <div>透明</div>
          <div class="u-fx u-fc toolbox-width-item">
            <t-slider v-if="isTiled"
                      :min="10"
                      :default-value="100"
                      v-model:value="tiledWatermarkConfig.opacity"
                      :style="{ width: '98%' }" />
            <t-slider v-else
                      :default-value="100"
                      v-model:value="singleWatermarkConfig.opacity"
                      :style="{ width: '98%' }" />
          </div>
        </div>
        <div v-if="isSingle" class="form-item">
          <div>位置</div>
          <div class="toolbox-width-item">
            <t-select v-model:model-value="singleWatermarkConfig.position"
                      style="width: 100%;"
                      size="small"
                      class="toolbox-width-item"
                      :disabled="disabled && isSingle">
               <t-option value="top-left" label="左上" />
               <t-option value="top-right" label="右上" />
               <t-option value="left" label="左中" />
               <t-option value="right" label="右中" />
               <t-option value="bottom-left" label="左下" />
               <t-option value="bottom-right" label="右下" />
            </t-select>
          </div>
        </div>
        <div v-if="isSingle"
             class="form-item">
          <div>填充</div>
          <div class="toolbox-width-item">
            <t-input-number v-model:model-value="singleWatermarkConfig.padding"
                            :default-value="0"
                            :min="0"
                            :max="100"
                            size="small"
                            theme="column"
                            class="w-full"
            />
          </div>
        </div>
        <div v-if="isSingle && singleWatermarkConfig.contentScript
                    && singleWatermarkConfig.contentScript.args">
          <div v-for="scriptArg in singleWatermarkConfig.contentScript.args"
               :key="scriptArg.params">
            <div v-if="scriptArg.params === 'timeFormat'"
                 class="form-item">
              <div>时间格式</div>
              <div class="toolbox-width-item">
                <t-select v-model:model-value="scriptArg.value"
                          size="small">
                  <t-option value="YYYY-MM-DD HH:mm:ss" :label="dayjs().format('YYYY-MM-DD HH:mm:ss')" />
                  <t-option value="YYYY-MM-DD HH:mm" :label="dayjs().format('YYYY-MM-DD HH:mm')" />
                  <t-option value="YYYY-MM-DD" :label="dayjs().format('YYYY-MM-DD')" />
                </t-select>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!singleWatermarkConfig.direct"
             class="u-fx u-gap10"
             style="justify-content: flex-end">
          <t-tooltip v-if="isSingle && tool.isSingleWatermark(tool.getSelectUI.value)">
            <template #content>
              将当前元素移除水印属性
            </template>
            <t-button size="small"
                      theme="default"
                      :disabled="disabled"
                      @click="handleCancelWatermark">
              <span class="u-font-size-smail">降级普通元素</span>
            </t-button>
          </t-tooltip>
          <t-tooltip  v-if="isSingle && !tool.isSingleWatermark(tool.getSelectUI.value)">
            <template #content>
              将当前元素添加上水印属性
            </template>
            <t-button size="small"
                      theme="primary"
                      :disabled="disabled"
                      @click="handleConfirmWatermark">
              <span class="u-font-size-smail">升级水印元素</span>
            </t-button>
          </t-tooltip>
        </div>
        <div v-else>
          <div class="u-fx u-gap10"
               style="justify-content: flex-end">
            <t-tooltip v-if="isSingle && tool.isSingleWatermark(tool.getSelectUI.value)">
              <template #content>
                将当前水印删除
              </template>
              <t-button size="small"
                        theme="default"
                        :disabled="disabled"
                        @click="handleCancelWatermark">
                <span class="u-font-size-smail"
                      @click="handleCancelWatermark">移除水印</span>
              </t-button>
            </t-tooltip>
          </div>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">
.toolbox-config {
  gap: 0;
}

.arco-select-option {
  line-height: 24px;
  font-size: 12px;
}
.toolbox-sub-config {
  padding: 10px;
  background: var(--main-background);
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
}
.toolbox-width-item {
  width: 150px;
}
</style>
