import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import WatermarkTool from './WatermarkTool'
import WatermarkToolBox from './WatermarkToolBox.vue'
import WatermarkSceneSetting from './WatermarkSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'watermark',
  title: '水印',
  sort: 2,
  group: 'tool',
  keyboard: {
    key: ''
  },
  toolIcon: 'i-u-watermark',
  config: true,
  sceneSetting: async () => WatermarkSceneSetting,
}
export default {
  info: CONFIG,
  tool: new WatermarkTool(),
  component: async () => WatermarkToolBox,
} as ToolboxItem;
