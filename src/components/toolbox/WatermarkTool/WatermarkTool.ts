import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { EditorEvent } from '@leafer-in/editor'
import jsUtil from '@/utils/jsUtil'
import JsUtil, { isArray } from '@/utils/jsUtil'
import type { I<PERSON> } from '@leafer-ui/interface'
import { ref, toRefs, watch, type WatchStopHandle } from 'vue'
import type { IAlign } from '@leafer/interface'
import { AlignHelper, Group, Point, Text, UI } from 'leafer-ui'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import type {
  ITiledWatermarkConfig,
  WatermarkConfigType
} from '@/stores/ToolConfigStore/store/WatermarkToolConfigStore'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { nanoid } from 'nanoid'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { ToolElementEvent } from '@/events/ToolElementEvent'
import dayjs from 'dayjs'
import systemScript from '@/components/toolbox/WatermarkTool/script/systemScript'

/**
 * 水印元素
 */
export interface WatermarkData  {
  docId: string;
  oldData: any;
  oldClassName: string;
  config: WatermarkConfigType;
}

export default class WatermarkTool extends BaseUseTool {

  private __selectUI = ref<IUI | null>();

  private __tiledWatermarkConfigWatch?: WatchStopHandle;
  private __singleWatermarkConfigWatch?: WatchStopHandle;
  constructor() {
    super()
  }

  doInstall() {
    // @ts-ignore
    window.addEventListener(ToolElementEvent.ELEMENT_DELETE, (e: ToolElementEvent) => {
      const target = e.target;
      if (this.isSingleWatermark(target)) {
        const data = target.getAttr('data') as WatermarkData;
        if (data.docId) {
          window.utools.dbStorage.removeItem(data.docId);
        }
      }
    });
  }

  protected doUse(): void {
    const app = this.getApp();
    this.selectWatermarkElement(app.editor.target);
    app.editor.on(EditorEvent.SELECT, (e: EditorEvent) => {
      this.selectWatermarkElement(e.value);
    })
    const { singleWatermarkConfig, tiledWatermarkConfig} = toRefs(useToolConfigStore());
    this.__tiledWatermarkConfigWatch = watch(() => tiledWatermarkConfig.value,
      (newVal) => {
        this.operateWatermark(newVal as any);
    }, { deep: true });

    this.__singleWatermarkConfigWatch = watch(() => singleWatermarkConfig.value,
      (newVal) => {
        console.log('newVal', newVal)
        this.operateWatermark(newVal as any);
    }, { deep: true, immediate: true });
  }

  private selectWatermarkElement(ui?: IUI | IUI[]) {
    if (!ui || jsUtil.isArray(ui)) {
      this.__selectUI.value = null;
    } else {
      // 将编辑元素传递到工具类中
      this.__selectUI.value = ui;
    }
  }


  /**
   * 操作水印元素
   * @param watermarkConfig 水印元素配置
   */
  public operateWatermark(watermarkConfig: WatermarkConfigType) {
    if (this.__selectUI.value) {
      const selectedIUI = this.getSelectedIUI();
      if (!selectedIUI || isArray(selectedIUI)) {
        return;
      }

      // 填充
      if (watermarkConfig.padding) {
        selectedIUI.padding = watermarkConfig.padding;
      }

      if (watermarkConfig.opacity) {
        selectedIUI.set({
          opacity: watermarkConfig.opacity / 100,
        });
      }


      if (watermarkConfig.position) {
        this.layoutAlign(watermarkConfig.position as any, this.getSelectedIUI());
      }

      if (watermarkConfig.contentScript) {
        setTimeout(() => {
          this.executeScript(this.getSelectedIUI());
          if (watermarkConfig.position) {
            this.layoutAlign(watermarkConfig.position as any, this.getSelectedIUI());
          }
        });
      }

      if (this.isSingleWatermark(selectedIUI)) {
        // 只有水印元素配置才保存
        LeaferHelper.setElementData<WatermarkData>(selectedIUI, {
          config: watermarkConfig as any,
        })
        this.saveSingleData(selectedIUI);
      }
    } else {
      this.drawTiledWatermark(watermarkConfig);
    }
  }


  /**
   * 确认为水印元素
   */
  public confirmWatermark() {
    console.log('1111111')
    const iui = this.getSelectedIUI();
    const toolConfigStore = useToolConfigStore();
    iui.set({
      className: 'watermark-single'
    });
    const saveConfig: WatermarkData = {
      config: JSON.parse(JSON.stringify(toolConfigStore.singleWatermarkConfig)),
    } as any
    if (!this.isSingleWatermark(iui)) {
      saveConfig.oldClassName = iui.getAttr('className');
      saveConfig.oldData = iui.getAttr('data');
    }
    LeaferHelper.setElementData<WatermarkData>(iui, saveConfig, true);
    const value = this.__selectUI.value;
    this.getEditor().cancel();
    this.getEditor().select(value);
    setTimeout(() => {
      this.saveSingleData(iui);
    });
  }

  public cancelWatermark() {
    const iui = this.getSelectedIUI();
    const watermarkData = iui.get('data') as WatermarkData;
    if (this.isAutoSaveConfig() && watermarkData.docId) {
      window.utools.dbStorage.removeItem(watermarkData.docId);
    }

    if (watermarkData.config.direct) {
      iui.remove();
      this.getEditor().cancel();
    } else {
      iui.set({
        className: watermarkData?.oldClassName || null,
        data: watermarkData?.oldData || null
      });
      const value = this.__selectUI.value;
      this.getEditor().cancel();
      this.getEditor().select(value);
    }
  }

  public layoutAlign(align: IAlign, content: IUI) {
    const contentBounds = {...content.getBounds(), x: 0, y: 0};
    const containerBounds = {width: this.getApp().width, height: this.getApp().height, x: 0, y: 0};
    console.log('contentBounds', contentBounds, containerBounds);
    const point = new Point();
    AlignHelper.toPoint(align,contentBounds, containerBounds, point, false);
    content.set(this.getAppTree().getPagePoint(point));
  }

  /**
   * 平铺水印
   * @param config
   * @private
   */
  private drawTiledWatermark(config: ITiledWatermarkConfig) {
    this.getAppTree().remove(this.getAppTree().findId(LeaferConstant.ElementWatermarkTiled))
    if (!config.text) {
      return;
    }
    // 平铺
    const pagePoint = this.getApp().getPagePoint({x: 0, y: 0})
    const group = new Group({
      id: LeaferConstant.ElementWatermarkTiled,
      ...pagePoint,
      scale: 1,
      opacity: config.opacity / 100,
      data: {
        config,
      } as WatermarkData
    });
    const textFillColor = config.fillSelectOptions.find(item => item.id === config.fillId);
    const text = new Text({
      x: 0,
      y: 0,
      text: config.text,
      fill: textFillColor || '#c3c3c3',
      rotation: config.rotation,
    });

    const {width: watermarkWidth, height: watermarkHeight} = text.getBounds();
    const scale = this.getAppTree().zoomLayer.scale as number;
    const {width: appWidth, height: appHeight} = this.getApp();
    const drawWidth = appWidth / scale;
    const drawHeight = appHeight / scale;
    for (let i = 0; i < drawWidth; i += watermarkWidth + config.gap) {
      for (let j = 0; j < drawHeight; j += watermarkHeight + config.gap) {
        if (i == 0 && j == 0) {
          continue;
        }
        const iui = text.clone();
        iui.set({
          x: i,
          y: j,
        });
        group.add(iui);
      }
    }
    group.add(text);
    this._app?.tree.add(group)
    console.log('group', group)
  }
  public canvasSizeChange() {
    const iuiList = this.getApp().find('.watermark-single');
    if (iuiList && iuiList.length) {
      for (const iui of iuiList) {
        const data = iui.get('data') as WatermarkData;
        this.layoutAlign(data.config.position as any, iui);
      }
    } else {
      const watermark = this.getAppTree().findId(LeaferConstant.ElementWatermarkTiled);
      if (watermark) {
        const data = watermark.get('data') as WatermarkData;
        this.drawTiledWatermark(data.config);
      }
    }

  }

  private getSelectedIUI()  {
    return this.getApp().editor.target as IUI;
  }

  protected doDestroy() {
    if (this.__tiledWatermarkConfigWatch) {
      this.__tiledWatermarkConfigWatch();
    }
    if (this.__singleWatermarkConfigWatch) {
      this.__singleWatermarkConfigWatch();
    }
  }


  public get getSelectUI() {
    return this.__selectUI;
  }


  private saveSingleData(watermarkIUI: IUI) {
    if (!this.isAutoSaveConfig()) {
      return;
    }
    const data = watermarkIUI.get('data') as WatermarkData;
    let docId = data.docId;
    if (!docId) {
      docId = `${this.getSceneToolSingleDocIdPrefix()}/${Date.now()}/${nanoid(12)}`;
      LeaferHelper.setElementData<WatermarkData>(watermarkIUI, {
        docId
      });
    }
    window.utools.dbStorage.removeItem(docId);
    // @ts-ignore
    window.utools.db.postAttachment(docId, window.nodeBuffer.Buffer.from(JSON.stringify(watermarkIUI.toJSON(), 'utf-8')) , 'text/plain');
  }

  /**
   * 判断当前元素是否是水印元素
   * @param element 元素
   */
  public isSingleWatermark(element: IUI | null) {
    return element ? element.className === 'watermark-single' : false;
  }


  protected doReloadConfig() {
    setTimeout(() => {
      this.loadSingleWaterMark();
      const { tiledWatermarkConfig } = useToolConfigStore();
      this.drawTiledWatermark(tiledWatermarkConfig);
    }, 100);
  }

  public addSpecialWatermark() {
    const text = new Text({
      className: 'watermark-single',
      text: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      fill: '#b2b6b6',
      fontSize: 12,
      editable: true,
      data: {
        docId: '',
        oldData: undefined,
        oldClassName: 'NONE',
        config: {
          position: 'bottom-right',
          opacity: 100,
          padding: 0,
          direct: true,
          contentScript: {
            scriptId: '0',
            args: [
              {
                params: 'timeFormat',
                value: 'YYYY-MM-DD HH:mm:ss'
              }
            ]
          },
        }
      } as WatermarkData,
    });

    this.addSingleWatermark(text);
    this.getEditor().select(text);
  }


  private executeScript(element: IUI) {
    const data = element.get('data') as WatermarkData;
    if (data.config.contentScript) {
      const contentScript = data.config.contentScript
      const script = systemScript.getScript(contentScript.scriptId);
      if (!script) {
        return;
      }
      // 执行脚本
      if (!script.scriptContent.includes('function')
        && !script.scriptContent.trim().startsWith("return")) {
        script.scriptContent = 'return ' + script.scriptContent;
      }

      const func = new Function('args', script.scriptContent).bind({
        dayjs
      });

      const args = (contentScript?.args || []).reduce((obj: any, arg) => {
        obj[arg.params] = arg.value;
        return obj;
      }, {} as Record<string, any>);

      const returnValue = func(args);
      console.log('执行脚本结果', returnValue);
      if (JsUtil.isObject(returnValue)) {
        element.set({
          ...returnValue
        })
      } else {
        element.set({
          text: returnValue
        })
      }
    }
  }
  private addSingleWatermark(element: IUI) {
    element.visible = false;
    console.log('addSingleWatermark::添加元素', element)
    this.getMiddleGroup().add(element);
    this.executeScript(element);
    const data = element.get('data') as WatermarkData;
    setTimeout(() => {
      element.visible = true;
      this.layoutAlign(data.config.position as any, element);
    }, 200);
  }

  private loadSingleWaterMark() {
    // 清除目前已经存在的单体水印
    this.getAppTree().find('.watermark-single')
      .map(item => item.remove());
    const singleConfigPrefix = this.getSceneToolSingleDocIdPrefix();
    const singleConfigDocIds = (window.utools.db.allDocs(singleConfigPrefix) || [])
      .map(({ _id }) => _id);
    singleConfigDocIds.map(docId => {
      const dataBuffer = utools.db.getAttachment(docId);
      if (dataBuffer) {
        const dataStr = window.nodeBuffer.Buffer.from(utools.db.getAttachment(docId)).toString();
        const watermarkIUI = UI.one(JSON.parse(dataStr));
        this.addSingleWatermark(watermarkIUI)
      }
    });
  }

  public getSceneToolSingleDocIdPrefix(sceneCode = window.sceneCode) {
    return `sceneTool/watermark/${sceneCode}/element`;
  }
}
