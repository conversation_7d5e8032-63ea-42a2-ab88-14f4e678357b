<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import AngleInput from '@/components/common/AngleInput/AngleInput.vue'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'

const props = defineProps<{
  sceneCode: string
}>();

const modal = ref({});

const toolConfigStore = useToolConfigStore();
const sceneWatermarkConfig = toolConfigStore.getSceneWatermarkConfig(props.sceneCode);
const watermarkMode = ref<'single' | 'tiled'>('tiled');
const isTiled = computed(() => watermarkMode.value === 'tiled');
const isSingle = computed(() => watermarkMode.value === 'single');

const isSingleData = ref(false);
function refreshSingleData() {
  const docId = `sceneTool/watermark/${props.sceneCode}/element`;
  isSingleData.value = utools.db.getAttachment(docId) !== null;
}
function handleClearElement() {
  const docId = `sceneTool/watermark/${props.sceneCode}/element`;
  window.utools.dbStorage.removeItem(docId);
  refreshSingleData();
}
onMounted(() => {
  refreshSingleData();
})
</script>

<template>
  <div  v-if="sceneWatermarkConfig"
        class="scene-setting-form">
    <t-form :data="modal"
            :label-width="60">
      <t-form-item label="模式">
        <t-radio-group type="button"
                       size="small"
                       variant="default-filled"
                       v-model:value="watermarkMode">
          <t-tooltip>
            <template #content>
              目前可以截图工具类配置单个元素水印
            </template>
            <t-radio-button value="single" disabled>单个</t-radio-button>
          </t-tooltip>
          <t-radio-button value="tiled">平铺</t-radio-button>
        </t-radio-group>
      </t-form-item>
      <t-form-item v-if="isTiled"
                   label="内容">
        <t-input v-model:value="sceneWatermarkConfig.text"
                 size="small"/>
      </t-form-item>
      <t-form-item v-if="isTiled"
                   label="间距">
        <t-input-number v-model:value="sceneWatermarkConfig.gap"
                        size="small"
                        theme="column" />
      </t-form-item>
      <t-form-item v-if="isTiled"
                   label="角度">
        <AngleInput v-model:angle="sceneWatermarkConfig.rotation"
                    size="small" />
      </t-form-item>
      <t-form-item v-if="isTiled"
                   label="颜色">
        <StrokeSelectPanel  v-model:stroke-select-options="sceneWatermarkConfig.fillSelectOptions"
                            v-model:model-value="sceneWatermarkConfig.fillId"
                            hide-label />
      </t-form-item>
      <t-form-item v-if="isSingle"
                   label="元素内容">
        <t-button v-if="isSingleData"
                  size="small"
                  shape="round"
                  @click="handleClearElement">清除元素内容</t-button>
        <div  v-else>无</div>
      </t-form-item>
      <t-form-item label="透明度">
        <t-slider v-model:value="sceneWatermarkConfig.opacity"  />
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
:deep(.arco-form-item-layout-horizontal) {
  align-items: center;
}
</style>
