import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import { Point, PointerEvent, UI } from 'leafer-ui'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { EditorEvent } from '@leafer-in/editor'
import type { IStateStyle, IUI } from '@leafer-ui/interface'

class DrawToolError extends Error {
  readonly isThrow: boolean;
  constructor(msg: string, isThrow: boolean) {
    super(msg);
    this.isThrow = isThrow;
  }
}

/**
 * 绘画工具基础类
 */
export abstract class BaseDrawTool<T extends UI> extends BaseUseTool {
  private readonly _uiType: typeof UI;
  protected _downPoint?: Point;
  protected _draw: boolean = false;
  protected ui?: T;

  constructor(uiType: typeof UI) {
    super();
    this._uiType = uiType;
    super.register(PointerEvent.DOWN, this.mouseDown);
    super.register(PointerEvent.MOVE, this.mouseMove);
    super.register(PointerEvent.UP, this.mouseUp);
    this._draw = false;
  }

  protected doUse() {}





  mouseDown(e: PointerEvent) {
    if (!this.isLeftClick(e)) {
      return;
    }
   try {
     this.recordHistory();
     this.checkInvalidPosition(e);
     this.disableEditor();
     const innerPoint =  this.getAppTree().getInnerPoint(e)
     this._downPoint = new Point(innerPoint);
     const ui = this.doMouseDown(e);
     console.log('ui', ui);
     if (ui) {
       this.ui = ui;
       this.addDrawGroup(ui);
     }
     this._draw = true;
   } catch (e) {
     if (e instanceof DrawToolError && !e.isThrow) {
       // 只是想中断程序, 满足预期无需外部再次处理了
     } else {
       throw e;
     }
   }
  }

  /**
   * 按下事件-绘画开始事件 <br/>
   * 实现建议: 一般情况下需要在这个阶段准备好这次需要 ui 对象
   * @param e
   */
  doMouseDown(e: PointerEvent): T | void {

  }

  mouseMove(e: PointerEvent) {
    console.log('draw', this._draw)
    if (this._draw) {
      this.doMouseMove(e);
    }
  }

  /**
   * 移动事件-绘画中事件 <br/>
   * @param e
   */
  doMouseMove(e: PointerEvent) {

  }

  mouseUp(e: PointerEvent) {
    this.checkInvalidDraw(e);
    this.selectDefaultUI();
    this.enableEditor();
    this.doMouseUp(e);
    this.ui && this.applyHistory();
    this.cleanDrawData();
  }


  /**
   * 抬起事件-绘画完成事件
   * @param e
   */
  doMouseUp(e: PointerEvent) {

  }

  destroy() {
    super.destroy();
    this.enableEditor();
  }


  protected addDrawGroup(ui: UI) {
    this.getDrawGroup().add(ui);
  }

  /**
   * 清理当前绘画的数据
   * @protected
   */
  protected cleanDrawData() {
    this._draw = false;
    this.ui = null;
    this._downPoint = undefined;
  }

  /**
   * 禁用编辑器
   * @private
   */
  private disableEditor() {
    this._app!.editor.visible = false;
  }
  /**
   * 启用编辑器
   * @private
   */
  private enableEditor() {
    if (!this._app!.editor.visible) {
      this._app!.editor.visible = true;
    }
  }

  /**
   * 检查是否是有效的绘画
   * @param x
   * @param y
   * @private
   */
  private checkInvalidDraw({x, y}: PointerEvent) {
    if (this._draw) {
      const word = {x, y};
      this._app!.tree.worldToInner(word);
      if (word.x === this._downPoint!.x && word.y ===this._downPoint!.y) {
        LeaferHelper.removeElement(this.getApp(), this.ui);
        this.ui = null;
      }
    }
  }

  /**
   * 进行绘画前的位置检查, 如果不通过不会触发开始绘画事件 {@link doMouseDown} <br/>
   * 1. 是否有图形中编辑状态 是: 不通过 <br/>
   * 如果不需要检查, 直接覆盖这个方法
   * @param e
   * @protected
   */
  protected checkInvalidPosition(e: PointerEvent) {
    const app = this.getApp()
    const editorTarget = app.editor.target;
    if (!editorTarget) {
      return;
    }
    if (editorTarget instanceof Array) {
      throw new DrawToolError('当前在编辑中无法绘画', false);
    } else {
      if (editorTarget.id && editorTarget.id === 'chooseAreaRect') {
        return;
      }
    }
    if (editorTarget) {
      throw new DrawToolError('当前在编辑中无法绘画', false);
    }
  }

  getUI(): UI {
    return this.ui!;
  }

  /**
   * 获取当前工具, 被用户选择的全部元素
   * @summary 为了解决元素更新问题
   * @protected
   */
  public getSelectedCurrentTools(): T[] {
    return super.getSelectedCurrentTools() as T[];
  }

  getCancelSelectTools(e: EditorEvent): IUI[] {
    return super.getCancelSelectTools(e) as T[];
  }

  protected selectDefaultUI() {
    if (this.ui) {
      let ui = this.ui;
      setTimeout(() => {
        console.log('检查是否是有效的绘画');
        this.getEditor().select(ui);
        ui = null;
      });
    }
  }

  /**
   * 镂空图形拖拽
   * @protected
   */
  protected installHollowDragElement() {
    this.getEditor().on(EditorEvent.SELECT, (e: EditorEvent) => {
      const selectedTools = this.getSelectedCurrentTools();
      const cancelSelect = this.getCancelSelectTools(e);
      if (cancelSelect.length) {
        cancelSelect.filter(item => item.set({
          hitFill: 'path'
        }));
      }

      if (!selectedTools.length || selectedTools.length > 1) {
        return;
      }

      selectedTools.filter(item => item.set({
        hitFill: 'all'
      }));
    });
  }

  protected installHoverStyle() {
    this.getEditor().on(EditorEvent.HOVER, (e: EditorEvent) => {
      const selectedTools = LeaferHelper.formatIUIArray(e.value)
        .filter(item => item.className === this.toolInfo.code);
      const cancelSelect = LeaferHelper.formatIUIArray(e.oldValue)
        .filter(item => item.className === this.toolInfo.code);
      if (cancelSelect.length) {
        cancelSelect.filter(item => item.set({
          hoverStyle: null
        }));
      }
      selectedTools.filter(item => item.set({
        ...this.getHoverStyle(),
      }));
    });
  }

  protected getHoverStyle(): IStateStyle {
    return {
      hoverStyle: {
        shadow: {
          x: 0,
          y: 0,
          blur: 5,
          color: '#262626'
        }
      }
    }
  }
}
