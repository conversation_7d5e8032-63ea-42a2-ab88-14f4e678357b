import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import leaferConstant from '@/leaferApp/LeaferConstant'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
import { Editor } from '@leafer-in/editor'

interface LongCapture {
  longList: { src: HTMLCanvasElement; temp: HTMLCanvasElement | null; after: HTMLCanvasElement }[];
  oCanvas: HTMLCanvasElement | null;
  p: { x: number, y: number };
  pList: { x: number, y: number }[];
}
export default class LongCaptureTool extends BaseUseTool {
  private canvas: HTMLCanvasElement | null = null;

  private wheelTime:  number = 0;
  private longCapture: LongCapture = {
    longList: [],
    oCanvas: null,
    p: { x: 0, y: 0 },
    pList: []
  }

  public constructor() {
    super()
  }

  protected showToolBar() {
    this.getScreenshotGroup().remove();
    this.getAppTree().findId(LeaferConstant.ElementCaptureContainerCover)!.remove();
    document.getElementById('toolboxContainer')!.style.display = 'none';
    const longCaptureToolboxContainer = document.getElementById(HtmlDomConstants.LongCaptureToolboxContainer);
    const toolboxContainerWrapper = document.getElementById(HtmlDomConstants.ToolboxContainerWrapper);
    const longCaptureToolPreview = document.getElementById(HtmlDomConstants.LongCaptureToolPreview);
    const chooseAreaRect = this.getAppTree().findId(leaferConstant.ElementChooseAreaRect)!;
    if (longCaptureToolboxContainer && toolboxContainerWrapper && longCaptureToolPreview) {
      longCaptureToolboxContainer.style.display = 'flex';

      const longCaptureToolboxBound = longCaptureToolboxContainer.getBoundingClientRect();
      const bounds = chooseAreaRect.getBounds();
      const leftX = bounds.x + bounds.width;
      const posX = leftX - longCaptureToolboxBound.width;
      toolboxContainerWrapper.style.left = `${posX}px`;

      // 预览
      longCaptureToolPreview.style.display = 'block';
      longCaptureToolPreview.style.overflowY = `auto`;
      longCaptureToolPreview.style.maxHeight = `600px`;
      longCaptureToolPreview.style.left = `${leftX}px`;
      longCaptureToolPreview.style.top = `${bounds.y}px`;
    }

  }
  protected doUse() {
    const chooseAreaRect = this.getAppTree().findId(leaferConstant.ElementChooseAreaRect)!;

    //region 编辑器状态修改
    for (const child of this.getApp().sky.children) {
      if (child instanceof Editor) {
        child.config.pointSize = 0;
        // 刷新状态
        child.cancel();
        child.select(chooseAreaRect);
      }
    }
    //endregion

    this.showToolBar();
    const bounds = chooseAreaRect.getBounds();
    this.longCapture.p = {x: 0, y: 0};
    this.longCapture.oCanvas = document.createElement("canvas");
    this.longCapture.oCanvas.width = bounds.width * window.devicePixelRatio;
    this.longCapture.oCanvas.height = bounds.height * window.devicePixelRatio;
    this.captureScreenshot().then(() => {})
    window.uiohook.uIOhook.on('wheel', (e) => {
      const nowTimer = Date.now();
      if (nowTimer - this.wheelTime > 300) {
        this.wheelTime = nowTimer;
        this.captureScreenshot().then(() => {});
      }
      console.log('wheel', e);
    });
    window.uiohook.uIOhook.start();
  }

  private async captureScreenshot() {
    window.screenshot.listDisplays().then(res =>{
      console.log(res);
    })
    console.log('captureDisplayId', window.winHelper.getData('captureDisplayId'))
    const screenBuffer = await window.screenshot({
      format: 'png',
      screen: window.winHelper.getData('captureDisplayId')
    });
    const base64 = "data:image/png;base64," + screenBuffer.toString("base64")
    const mainImage = window.nativeImage.createFromBuffer(screenBuffer);
    const {width, height} = mainImage.getSize();
    this.addLong(mainImage.getBitmap(), width, height);
  }

  private addLong(buffer: Buffer, width: number, height: number) {
    if (!buffer) {
      window.uiohook.uIOhook.stop();
      return;
    }
    // 原始全屏
    const srcCanvas = document.createElement('canvas');
    srcCanvas.width = width;
    srcCanvas.height = height;
    srcCanvas.style.width = `${width / window.devicePixelRatio}px`;
    srcCanvas.style.height = `${width / window.devicePixelRatio}px`;


    // 原始区域
    const canvas = document.createElement("canvas");
    // 对比模板
    const canvasTop = document.createElement("canvas");
    // 要拼接的图片
    const canvasAfter = document.createElement("canvas");

    const longList = this.longCapture.longList;
    //
    //
    for (let i = 0; i < buffer.length; i += 4) {
      [buffer[i], buffer[i + 2]] = [buffer[i + 2], buffer[i]];
    }
    console.log('width, height', width, height)
    //
    const imageData  = new ImageData(Uint8ClampedArray.from(buffer), width, height);

    srcCanvas.getContext("2d")!.putImageData(imageData, 0, 0);


    const chooseAreaRect = this.getAppTree().findId(leaferConstant.ElementChooseAreaRect)!;
    const chooseAreaRectBounds = chooseAreaRect.getBounds();
    const dx = chooseAreaRectBounds.x + 4;
    const dy = chooseAreaRectBounds.y + 4;
    const dw = chooseAreaRectBounds.width - 6;
    const dh = chooseAreaRectBounds.height - 6;

    const ratio = window.devicePixelRatio;
    const rdx = dx * ratio;
    const rdy = dy * ratio;
    const rdw = dw * ratio;
    const rdh = dh * ratio;

    canvas.width = rdw;
    canvas.height = rdh;

    // 裁剪
    const tailorImageData = srcCanvas.getContext("2d")!.getImageData(rdx, rdy, rdw, rdh);

    canvas.getContext('2d')!.putImageData(tailorImageData, 0, 0);

    canvasTop.width = canvasAfter.width = rdw;

    const recHeight = Math.min(50, rdh);
    const recTop = Math.floor(rdh / (2 * ratio) - recHeight / ratio);

    // 设定 canvas 宽高并设置裁剪后的图像
    canvasTop.height = recHeight; // 只是用于模板对比，小一点
    canvasAfter.height = rdh - recTop; // 裁剪顶部
    canvasTop.getContext("2d")!.putImageData(tailorImageData, 0, -recTop);
    canvasAfter.getContext("2d")!.putImageData(tailorImageData, 0, -recTop);
    console.log('canvasAfter', canvasAfter.toDataURL());
    console.log('canvasTop', canvasTop.toDataURL());

    // 将新的截图数据加入 longList
    longList.push({ src: canvas, temp: canvasTop, after: canvasAfter });

    // 对比
    const pos = longList.length - 2;
    if (pos < 0) {
      return
    }
    const cv =  window.cv;
    const src = cv.imread(longList[pos].src);
    const templ = cv.imread(longList[pos + 1].temp!);
    const dst = new cv.Mat();
    const mask = new cv.Mat();
    cv.matchTemplate(src, templ, dst, cv.TM_CCORR_NORMED, mask);
    const result = cv.minMaxLoc(dst, mask);
    console.log('result', result)
    const maxPoint = result.maxLoc;
    const maxVal = result.maxVal;
    // 检查是否需要拼接
    // 设置匹配阈值
    const matchThreshold = 0.95; // 可以根据实际情况调整
    console.log(maxVal)
    if (maxVal < matchThreshold) {
      console.log('匹配度不足，跳过拼接');
      src.delete();
      dst.delete();
      mask.delete();
      this.longCapture.longList.splice(pos + 1, 1);
      // this.longCapture.longList[pos + 1].temp = null;
      return;
    }

    // 更新拼接画布尺寸和位置
    const oCanvas = this.longCapture.oCanvas!;
    console.log('maxPoint.x', maxPoint.x)
    oCanvas.width += maxPoint.x ;
    oCanvas.height += maxPoint.y ;
    this.longCapture.p.x += maxPoint.x;
    this.longCapture.p.y += maxPoint.y;
    this.longCapture.pList.push({ x: this.longCapture.p.x, y: this.longCapture.p.y });
    oCanvas.height -= recTop;
    this.longCapture.p.y -= recTop;
    console.log({ x: this.longCapture.p.x * ratio, y: this.longCapture.p.y * ratio })
    //
    // 释放 OpenCV 资源
    src.delete();
    dst.delete();
    mask.delete();
    this.longCapture.longList[pos + 1].temp = null;

    // 先画顶部图片，使用原始区域
    oCanvas.getContext("2d")!.drawImage(longList[0].src, 0, 0);
    // console.log('toDataURL', oCanvas.toDataURL())
    // console.log(oCanvas.width, oCanvas.height)
    for (let i = 0; i < longList.length - 1; i++) {
      oCanvas.getContext("2d")!.drawImage(longList[i + 1].after, this.longCapture.pList[i].x, this.longCapture.pList[i].y); // 每次拼接覆盖时底部总会被覆盖，所以不用管底部
    }

    const longCapturePreviewCanvas = document.getElementById(HtmlDomConstants.LongCapturePreviewCanvas) as HTMLCanvasElement;
    if (longCapturePreviewCanvas) {
      longCapturePreviewCanvas.width = oCanvas.width;
      longCapturePreviewCanvas.height = oCanvas.height;
      longCapturePreviewCanvas.getContext('2d')!.drawImage(oCanvas, 0, 0);
    }
    oCanvas.getContext("2d")!.clearRect(0, 0, oCanvas.width, oCanvas.height);
  }

  /**
   * 停止长截图
   */
  public stopLong() {
    const oCanvas = this.longCapture.oCanvas!;
    const longList = this.longCapture.longList;
    oCanvas.getContext("2d")!.drawImage(longList[0].src, 0, 0);
    // console.log('toDataURL', oCanvas.toDataURL())
    // console.log(oCanvas.width, oCanvas.height)
    for (let i = 0; i < longList.length - 1; i++) {
      oCanvas.getContext("2d")!.drawImage(longList[i + 1].after, this.longCapture.pList[i].x, this.longCapture.pList[i].y); // 每次拼接覆盖时底部总会被覆盖，所以不用管底部
    }
    this.longCapture.longList.length = 0;
    // 停止监听
    window.uiohook.uIOhook.stop();
    return window.nativeImage.createFromDataURL(oCanvas.toDataURL()).toDataURL();
  }
}
