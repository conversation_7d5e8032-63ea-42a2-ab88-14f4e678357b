<script setup lang="ts">
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import { CONFIG } from './index'
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'

const toolInfo: ToolboxInfoItemConfig = CONFIG

// const { lineConfig } = toRefs(useToolConfigStore())
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="toolbox-config">
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">

</style>
