import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import LongCaptureTool from '@/components/toolbox/LongCaptureTool/LongCaptureTool'
import LongCaptureToolBox from '@/components/toolbox/LongCaptureTool/LongCaptureToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'longCapture',
  title: '长截图',
  sort: 1,
  group: 'tool',
  environment: 'capture',
  // os: 'mac',
  toolIcon: 'i-u-long-screenshot',
  hideScene: true,
}

export default {
  info: CONFIG,
  tool: new LongCaptureTool(),
  component: async () => LongCaptureToolBox,
} as ToolboxItem;
