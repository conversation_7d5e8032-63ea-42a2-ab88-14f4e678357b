import { App, type Image, PointerEvent, type UI } from 'leafer-ui'
import type { IEventListener, IEventListenerId } from '@leafer/interface'
import type { ToolboxInfoItemConfig } from '@/components/toolbox/index'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import type { IUI, IUIJSONData } from '@leafer-ui/interface'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import LeaferElement from '@/leaferApp/LeaferElement'
import { EditorEvent } from '@leafer-in/editor'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'

export abstract class BaseUseTool {
  protected _app?: App;
  private readonly eventMap: Map<string, IEventListener>;
  protected readonly events: IEventListenerId[] = [];
  private _toolInfo?: ToolboxInfoItemConfig;

  private _useBeforeHistory?: IUIJSONData;


  protected constructor() {
    this.eventMap = new Map();
  }

  protected recordHistory() {
    this._useBeforeHistory = window.leaferHistoryList.getHistoryJson();
  }

  protected applyHistory() {
    if (this._useBeforeHistory) {
      window.leaferHistoryList.addHistoryByJson(this._useBeforeHistory);
      this._useBeforeHistory = undefined;
    }
  }

  public install(app: App, toolInfo: ToolboxInfoItemConfig) {
    this._app = app;
    this._toolInfo = toolInfo;
    this.initHotKey(toolInfo);
    this.doInstall();
  }

  public doInstall() {}


  protected initHotKey(toolInfo: ToolboxInfoItemConfig) {
    if (toolInfo.keyboard) {
      const currentToolBoxStore = useCurrentToolBoxStore();
      const {toolBoxKeyboardConfig} = useKeyboardConfigStore()
      const keyboardConfig = toolBoxKeyboardConfig[toolInfo.code] || toolInfo.keyboard;
      window.keyboardManager.addKeyboardConfig(toolInfo.code, keyboardConfig, {
        downHandle: () => {
          currentToolBoxStore.setCurrentToolBoxByCode(toolInfo.code, true);
        },
        upHandle: () => {
          currentToolBoxStore.rollbackToolBox();
        }
      });
    }
  }
  protected register(eventName: string, event: IEventListener) {
    this.eventMap.set(eventName, event);
  }
  public use() {
    for (const [eventName, event] of this.eventMap) {
      this.events.push(this.eventSource().on_(eventName, event, this));
    }
    this.doUse();
  }

  protected abstract doUse(): void;

  public destroy() {
     if (this.events.length) {
       this.eventSource().off_(this.events);
       this.events.length = 0;
     }
     this.doDestroy();
   }

   protected doDestroy() {};


   protected getDrawGroup() {
     return this.getAppTree().findId(LeaferConstant.ElementDrawGroup)!;
   }


   protected getMiddleGroup() {
     return this.getAppTree().findId(LeaferConstant.ElementMiddleGroup)!;
   }

   protected getAppTree() {
     return this._app!.tree;
   }

   protected getMainImage() {
     return this.getAppTree().findId(LeaferConstant.ElementScreenShot)! as Image;
   }

   protected getScreenshotGroup() {
     return LeaferElement.getScreenshotGroup(this._app!);
   }


   protected get toolInfo() {
     return this._toolInfo!;
   }

   public getUI(): UI | null {
     return null;
   }

   protected getApp(): App {
     return this._app!;
   }

   protected getEditor() {
     return this.getApp().editor;
   }

   protected batchAddEvent(events: IEventListenerId[]) {
     this.events.push(...events);
   }
   getSelectedCurrentTools(): IUI[] {
     return LeaferHelper.formatIUIArray(this.getEditor().target)
       .filter(item => item.className === this.toolInfo.code);
   }
   getCancelSelectTools(e: EditorEvent) {
     return LeaferHelper.formatIUIArray(e.oldValue)
       .filter(item => item.className === this.toolInfo.code);
   }

  /**
   * 修改光标
   */
   modifyCursor(cursor: 'default' | 'text' | 'crosshair') {
    if (window.environment === 'screenshot') {
      document.getElementById('contentWrapper').style.cursor = `${cursor}`;
    }
  }

  protected eventSource(): IUI {
     return this.getApp();
  }

  public async reloadConfig() {
     if (!this.isEnableTool()) {
       // 如果当前工具未启用就不调用配置重载功能了
       return;
     }
     setTimeout(() => {
       this.doReloadConfig();
     });
  }
  /**
   * 重载配置必调用这个函数
   * @protected
   */
  protected doReloadConfig() {

  }

  /**
   * 是否自动保存配置
   * @protected
   */
  protected isAutoSaveConfig() {
    const sceneStore = useSceneStore();
    const sceneConfig = sceneStore.getSceneConfig(window.sceneCode);
    return sceneConfig.saveMode === 'auto' && !sceneConfig.disableAutoSaveTools.includes(this._toolInfo.code);
  }

  /**
   * 当前工具是否启用
   * @protected
   */
  protected isEnableTool() {
    const sceneStore = useSceneStore();
    const sceneConfig = sceneStore.getSceneConfig(window.sceneCode);
    return !sceneConfig.disableCodeTools.includes(this._toolInfo.code);
  }

  /**
   * 判断是否是鼠标左键
   * @protected
   */
  protected isLeftClick(e: PointerEvent) {
    return e.buttons === 1;
  }
}
