import type { AsyncComponentLoader } from 'vue'
import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import type { PopupProps } from 'tdesign-vue-next'
import type { KeyboardConfig } from '@/utils/KeyboardManager'

export interface ToolboxInfoItemConfig {
  code: string;
  title: string;
  sort: number;
  group: string;
  os?: 'mac' | 'windows' | 'linux';
  environment?: 'capture' | 'screenshot' | 'all';
  /**
   * 是否是 瞬时 工具, 即按下即有效果
   */
  instant?: boolean;
  holdKey?: boolean;
  keyboard?: KeyboardConfig,
  toolIcon?: string;
  config?: boolean;
  triggerOptions?: PopupProps;
  /**
   * 场景配置
   */
  sceneSetting?: AsyncComponentLoader;
  /**
   * 场景是否显示
   */
  hideScene?: boolean;
}

export interface ToolboxItem {
  info: ToolboxInfoItemConfig,
  component: AsyncComponentLoader;
  tool?: BaseUseTool;
}
