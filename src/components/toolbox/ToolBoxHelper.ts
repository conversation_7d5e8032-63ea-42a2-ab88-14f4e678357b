import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox/index'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'

const toolboxObj: Record<string, ToolboxItem> = import.meta.glob('./*/index.ts', {
  eager: true,
  import: 'default',
});

export const toolConfigObj: Record<string, ToolboxInfoItemConfig> = import.meta.glob('./*/index.ts', {
  eager: true,
  import: 'CONFIG'
})

const groupSort = ['default', 'draw', 'tool', 'action'];


export function getToolConfigList(): ToolboxInfoItemConfig[] {
  const toolConfigList  = Object.keys(toolConfigObj)
    .map(key => toolConfigObj[key]);
  const toolBoxObj = toolConfigList.reduce((groups: Record<string, ToolboxInfoItemConfig[]>, item) => {
    // 如果分组中还没有当前 item 的 category，则创建一个新组
    if (!groups[item.group]) {
      groups[item.group] = [];
    }
    // 将当前 item 添加到相应的分组中
    groups[item.group].push(item);
    return groups;
  }, {});

  const toolboxInfoConfigSortList = [];
  for (const key of groupSort) {
    toolBoxObj[key] = toolBoxObj[key]
      .sort((a, b) => a.sort - b.sort);
    toolboxInfoConfigSortList.push(...toolBoxObj[key]);
  }
  return toolboxInfoConfigSortList;
}


export function getAllToolBox(filter?: (item: ToolboxItem) => boolean) {
  let toolboxList = Object.keys(toolboxObj)
    .map(item => toolboxObj[item])
    .filter(item => {
      if (item.info.os && window.utools) {
        if (item.info.os === 'mac' && utools.isMacOS()) {
          return true;
        } else if (item.info.os === 'windows' && utools.isWindows()) {
          return true;
        } else if (item.info.os === 'linux' && utools.isLinux()) {
          return true;
        }
        return false;
      }
      return true;
    }).filter(item => {
      if(!item.info.environment || item.info.environment === 'all') {
        return true;
      }
      return item.info.environment === window.environment;
    });

  // 特殊过滤
  if (filter) {
    toolboxList = toolboxList.filter(filter);
  }

  const toolBoxObj = toolboxList.reduce((groups: Record<string, ToolboxItem[]>, item) => {
    // 如果分组中还没有当前 item 的 category，则创建一个新组
    if (!groups[item.info.group]) {
      groups[item.info.group] = [];
    }
    // 将当前 item 添加到相应的分组中
    groups[item.info.group].push(item);
    return groups;
  }, {});
  const toolboxSortList = [];
  for (const key of Object.keys(toolBoxObj)) {
    toolBoxObj[key] = toolBoxObj[key]
      .sort((a, b) => a.info.sort - b.info.sort);
    toolboxSortList.push(...toolBoxObj[key]);
  }

  return {
    groupSort: Object.keys(toolBoxObj)
      .sort((a, b) => groupSort.indexOf(a) - groupSort.indexOf(b)),
    toolBoxObj,
    toolboxSortList,
  }
}

/**
 * 获取显示工具的集合
 */
export function getShowToolList() {
  const sceneStore = useSceneStore();
  const sceneConfig = sceneStore.getSceneConfig(window.sceneCode);
  return getAllToolBox(( item ) => !sceneConfig.disableCodeTools.includes(item.info.code));
}
