<script lang="ts" setup>
import { App } from 'leafer-ui'
import { ref } from 'vue'

const contentRef = ref();

const app = new App({
  view: contentRef.value,
  tree: {},
  ground: { type: 'draw' },
  editor: {},
  move: {
    disabled: true
  },
  zoom: {
    disabled: true
  },
  pixelSnap: true,
  pointSnap: true,
  smooth: true,
});
</script>
<template>
  <div id="recordContainer">
    <div id="content" ref="contentRef" />
  </div>
</template>
<style lang="less" scoped>
#content {
  width: 100vw;
  height: 100vh;
}
</style>
