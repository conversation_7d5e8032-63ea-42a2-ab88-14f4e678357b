import type { Clipboard, Ipc<PERSON><PERSON><PERSON> } from 'electron'
import { nativeImage } from 'electron'
import type { WinHelper } from '@/core/utils/WinHelper'
import type * as fs from 'node:fs'
import type * as Net from 'node:net'
import type * as buffer from 'node:buffer'
import type * as path from 'node:path'
import type * as http from 'node:http'
import type * as https from 'node:https'
import type * as zip from 'cross-zip'
import type * as readLine from 'node:readline'
import type { exec, spawn, execSync } from 'node:child_process'
import type Phin from 'phin'
import type Screenshot from 'screenshot-desktop'
import type UIOhook from 'uiohook-napi'
import type CV from 'opencv-ts'
import KeyboardManager from '@/utils/KeyboardManager'
import GlobalKeyboardManager from '@/utils/GlobalKeyboardManager'
import LocalOcrService from '@/leaferApp/ocr/LocalOcrService'
import LeaferHistoryList from '@/leaferApp/LeaferHistoryList'
import PlugInUtils from '@/core/utils/PlugInUtils'
import type SqliteUtils from '@/utils/SqliteUtils'

declare global {
  interface Window {
    db: SqliteUtils,
    net: typeof Net,
    nodeCrypto: typeof Crypto
    nodeProcess?: typeof process;
    sceneCode?: string;
    utools: UToolsApi,
    environment: 'capture' | 'screenshot'
    api: Record<any, any>;
    plugInUtils: PlugInUtils;
    winHelper: WinHelper;
    ipcRenderer: IpcRenderer;
    tempData: Record<string, any>;
    globalParams: Record<string, any>;
    leaferHistoryList: LeaferHistoryList;
    fs: typeof fs;
    nodeBuffer: typeof buffer;
    path: typeof path;
    screenshot: typeof Screenshot;
    uiohook: typeof UIOhook;
    nativeImage: typeof nativeImage;
    localCapture?: (executePath?: string) => Promise<string>;
    https: typeof https;
    http: typeof http;
    cv: typeof CV;
    keyboardManager: KeyboardManager;
    globalKeyboardManager: GlobalKeyboardManager;
    nodeHttp: typeof Phin;
    clipboard: Clipboard;
    zip: typeof zip;
    spawn: typeof spawn;
    exec: typeof exec;
    execSync: typeof execSync;
    readLine: typeof readLine;
    localOcrService: LocalOcrService;
    loadToolList: () => void;
    localOcr: {
      localOcr: () => Promise<string>;
      testOcr: () => any;
    }
  }
}
