import type { FuncKeyBoardConfig } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import type { GlobalKeyBoardConfig } from '@/stores/KeyboardConfigStore/store/GlobalKeyboardConfigStore'


/**
 * 贴图
 */
export const PASTE_PICTURE = "pastePicture";
/**
 * 切换工具条显示
 */
export const SWITCHOVER_TOOLBAR = "switchoverToolbar";

/**
 * 删除元素
 */
export const DELETE_ELEMENT = "deleteElement";

/**
 * 向右旋转 90
 */
const ROTATE_RIGHT_PICTURE = "rotateRightPicture";
/**
 * 向右旋转 45
 */
const ROTATE_RIGHT_45_PICTURE = "rotateRight45Picture";
/**
 * 向左旋转 90
 */
const ROTATE_LEFT_PICTURE = "rotateLeftPicture";
/**
 * 向左旋转 45
 */
const ROTATE_LEFT_45_PICTURE = "rotateLeft45Picture";

/**
 * 复制截图
 */
const COPY_PICTURE = "copyPicture";
/**
 * 降低透明度
 */
export const TRANSPARENCY_LOWER_PICTURE = "transparencyLowerPicture";
/**
 * 提高透明度
 */
export const TRANSPARENCY_INCREASE_PICTURE = "transparencyIncreasePicture";


/**
 * code 对应功能
 */
export const DEFAULT_FUNC_KEYBOARD_CONFIG: Record<string, FuncKeyBoardConfig> = {
  copyPicture: {
    code: 'copyPicture',
    title: '复制截图',
    keyboard: {
      ctrl: true,
      key: 'C'
    }
  },
  pastePicture: {
    code: 'pastePicture',
    title: '贴图',
    keyboard: {
      ctrl: true,
      key: 'V'
    }
  },
  switchoverToolbar: {
    code: 'switchoverToolbar',
    title: '切换工具条显示',
    keyboard: {
      ctrl: false,
      key: 'Tab'
    }
  },
  deleteElement: {
    code: 'deleteElement',
    title: '删除元素',
    keyboard: {
      ctrl: false,
      key: 'Backspace'
    }
  },
  rotateLeftPicture: {
    code: 'rotateLeftPicture',
    title: '向左旋转 90 度',
    keyboard: {
      ctrl: false,
      key: '['
    }
  },
  rotateLeft45Picture: {
    code: 'rotateLeft45Picture',
    title: '向左旋转 45 度',
    keyboard: {
      ctrl: true,
      key: '['
    }
  },
  rotateRightPicture: {
    code: 'rotateRightPicture',
    title: '向右旋转 90%',
    keyboard: {
      ctrl: false,
      key: ']'
    }
  },
  rotateRight45Picture: {
    code: 'rotateRight45Picture',
    title: '向右旋转 45 度',
    keyboard: {
      ctrl: true,
      key: ']'
    }
  },
  transparencyLowerPicture: {
    code: 'transparencyLowerPicture',
    title: '降低透明度',
    keyboard: {
      ctrl: false,
      key: '-'
    }
  },
  transparencyIncreasePicture: {
    code: 'transparencyIncreasePicture',
    title: '提高透明度',
    keyboard: {
      ctrl: false,
      key: '='
    }
  },
}


export const DEFAULT_GLOBAL_KEYBOARD_CONFIG: Record<string, GlobalKeyBoardConfig> = {
  codeScreenshotCapture: {
    code: 'codeScreenshotCapture',
    title: 'sc 关键字截图',
    pluginCode: 'screenshot.capture?sceneCode=default',
    keyboard: {
      ctrl: false,
      key: ''
    }
  }
}

export default {
  PASTE_PICTURE,
  SWITCHOVER_TOOLBAR,
  DELETE_ELEMENT,
  ROTATE_RIGHT_PICTURE,
  ROTATE_RIGHT_45_PICTURE,
  ROTATE_LEFT_PICTURE,
  ROTATE_LEFT_45_PICTURE,
  COPY_PICTURE,
  TRANSPARENCY_LOWER_PICTURE,
  TRANSPARENCY_INCREASE_PICTURE
}
