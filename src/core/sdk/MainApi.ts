import { MainApi } from '@/core/sdk/mainApiProvider'


/**
 * 调用截图容器 API
 * @param webContentsId 容器 ID
 * @param invoke 调用方法
 * @param args 调用方法参数
 */
export function sendMainContainerApi(webContentsId: number, invoke: string, args: any) {
  window.ipcRenderer.sendTo(webContentsId, MainApi, {invoke, args});
}

function invokeImageOcr(webContentsId: number, args: {imagePath: string}) {
  sendMainContainerApi(webContentsId, 'imageOcr',  args);
}

export default {
  invokeImageOcr
}
