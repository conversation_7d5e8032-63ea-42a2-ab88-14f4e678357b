import { sendScreenshot<PERSON><PERSON>r<PERSON><PERSON> } from '@/core/sdk/mainApiProvider'

function captureScreenshot(webContentsId: number) {
  sendScreenshot<PERSON>ontainer<PERSON>pi(webContentsId, 'captureScreenshot');
}

function sendBase64Screenshot(webContentsId: number, base64: string,
                              options?: { hideToolBar?: boolean,
                                autoSize?: boolean,
                                screenshotLocation?: {x: number, y: number}}) {
  sendScreenshot<PERSON>ontainer<PERSON>pi(webContentsId,'sendBase64Screenshot', base64, options);
}

function sendOtherWindowIds(webContentsId: number, winId: number, winIds: number[]) {
  winIds = winIds.filter(item => item !== winId);
  sendScreenshot<PERSON>ontainer<PERSON>pi(webContentsId,'setOtherWindowsIds', winIds);
}

function sendOcrResult(webContentsId: number, result: any) {
  sendScreenshot<PERSON>ontainer<PERSON><PERSON>(webContentsId,'onOcrResult', result);
}


function reloadScene(webContentsId: number, sceneCode: string) {
  sendScreenshot<PERSON><PERSON><PERSON><PERSON><PERSON>(webContentsId,'reloadScene', sceneCode);
}

function setGlobalParams(webContentsId: number, key: string, value: any) {
  sendScreenshotContainerApi(webContentsId,'setGlobalParams', key, value);
}

function sendScreenshotFileId(webContentsId: number, id: string) {
  sendScreenshotContainerApi(webContentsId,'sendScreenshotFileId', id);
}

export default {
  captureScreenshot,
  sendBase64Screenshot,
  sendOtherWindowIds,
  sendOcrResult,
  reloadScene,
  setGlobalParams,
  sendScreenshotFileId,
}
