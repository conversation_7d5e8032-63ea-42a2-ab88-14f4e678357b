/**
 * 本窗口调用主窗口方法
 * @param invoke
 * @param args
 */
export function invoke(invoke: string, args: any) {
  window.winHelper.sendMainMessage({
    invoke,
    args
  });
}

export const ScreenshotContainerApi = 'screenshot.container.api';
export function initScreenshotApiProvider() {
  // 主窗口调用本窗口的方法
  window.winHelper.ipcRenderer.on(ScreenshotContainerApi, (_, data) => {
    console.log(ScreenshotContainerApi, data);
    console.log(data.args)
    window.api[data.invoke](...data.args);
  });
}
