import { ScreenshotContainerApi } from '@/core/sdk/screenshotApiProvider'


export const MainApi = 'main.api';


/**
 * 调用截图容器 API
 * @param webContentsId 容器 ID
 * @param invoke 调用方法
 * @param args 调用方法参数
 */
export function sendScreenshot<PERSON>ontainer<PERSON>pi(webContentsId: number, invoke: string, ...args: any) {
  console.log('sendScreenshotContainerApi', webContentsId, invoke, args)
  window.ipcRenderer.sendTo(webContentsId, ScreenshotContainerApi, {invoke, args});
}

export function initMainApiProvider() {
  // 主窗口调用本窗口的方法
  window.ipcRenderer.on(MainApi, (_, data) => {
    console.log('screenshotApi', data);
    window.api[data.invoke](data.args, _.senderId);
  });
  window.api.imageOcr = (params: Record<string, any>, webContentsId: number) => {
    window.localOcrService.start();
    window.localOcrService.ocr(webContentsId, params).then(() => {});
  }
}

