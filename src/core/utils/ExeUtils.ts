
function executeJson<T=any>(command: string): Promise<T> {
  return execute(command).then(res => JSON.parse(res));
}


function execute<T=any>(command: string): Promise<T> {
  return new Promise((resolve, reject) => {
    window.exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(`<PERSON>rror executing command: ${error.message}`)
        return;
      }
      if (stderr) {
        reject(`stderr: ${stderr}`);
        return;
      }
      console.log('execute', stdout)
      resolve(stdout);
    })
  });
}
export default {
  execute,
  executeJson
}
