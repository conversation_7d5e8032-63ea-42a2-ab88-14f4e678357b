import type { BrowserWindow } from 'electron'

const winIpc = {
  windowMethods: [
    'destroy',
    'close',
    'focus',
    'blur',
    'isFocused',
    'isDestroyed',
    'show',
    'showInactive',
    'hide',
    'isVisible',
    'maximize',
    'unmaximize',
    'isMaximized',
    'minimize',
    'restore',
    'isMinimized',
    'setFullScreen',
    'isFullScreen',
    'setSimpleFullScreen',
    'isSimpleFullScreen',
    'isNormal',
    'setAspectRatio',
    'setBackgroundColor',
    'previewFile',
    'closeFilePreview',
    'setBounds',
    'getBounds',
    'getBackgroundColor',
    'setContentBounds',
    'getContentBounds',
    'getNormalBounds',
    'setEnabled',
    'isEnabled',
    'setSize',
    'getSize',
    'setContentSize',
    'getContentSize',
    'setMinimumSize',
    'getMinimumSize',
    'setMaximumSize',
    'getMaximumSize',
    'setResizable',
    'isResizable',
    'setMovable',
    'isMovable',
    'setMinimizable',
    'isMinimizable',
    'setMaximizable',
    'isMaximizable',
    'setFullScreenable',
    'isFullScreenable',
    'setClosable',
    'isClosable',
    'setAlwaysOnTop',
    'isAlwaysOnTop',
    'moveAbove',
    'moveTop',
    'center',
    'setPosition',
    'getPosition',
    'setTitle',
    'getTitle',
    'setSheetOffset',
    'flashFrame',
    'setSkipTaskbar',
    'setKiosk',
    'isKiosk',
    'isTabletMode',
    'getMediaSourceId',
    'getNativeWindowHandle',
    'setRepresentedFilename',
    'getRepresentedFilename',
    'setDocumentEdited',
    'isDocumentEdited',
    'focusOnWebView',
    'blurWebView',
    'setProgressBar',
    'setHasShadow',
    'hasShadow',
    'setOpacity',
    'getOpacity',
    'setShape',
    'showDefinitionForSelection',
    'setIcon',
    'setWindowButtonVisibility',
    'setVisibleOnAllWorkspaces',
    'isVisibleOnAllWorkspaces',
    'setIgnoreMouseEvents',
    'setContentProtection',
    'setFocusable',
    'setAutoHideCursor',
    'setVibrancy',
    'setTrafficLightPosition',
    'getTrafficLightPosition'
  ],
  windowInvokes: ['capturePage'],
  webContentsMethods: [
    'isDestroyed',
    'focus',
    'isFocused',
    'isLoading',
    'isLoadingMainFrame',
    'isWaitingForResponse',
    'isCrashed',
    'setUserAgent',
    'getUserAgent',
    'setIgnoreMenuShortcuts',
    'setAudioMuted',
    'isAudioMuted',
    'isCurrentlyAudible',
    'setZoomFactor',
    'getZoomFactor',
    'setZoomLevel',
    'getZoomLevel',
    'undo',
    'redo',
    'cut',
    'copy',
    'copyImageAt',
    'paste',
    'pasteAndMatchStyle',
    'delete',
    'selectAll',
    'unselect',
    'replace',
    'replaceMisspelling',
    'findInPage',
    'stopFindInPage',
    'isBeingCaptured',
    'incrementCapturerCount',
    'decrementCapturerCount',
    'getPrinters',
    'openDevTools',
    'closeDevTools',
    'isDevToolsOpened',
    'isDevToolsFocused',
    'toggleDevTools',
    'send',
    'sendToFrame',
    'enableDeviceEmulation',
    'disableDeviceEmulation',
    'sendInputEvent',
    'showDefinitionForSelection',
    'isOffscreen',
    'startPainting',
    'stopPainting',
    'isPainting',
    'setFrameRate',
    'getFrameRate',
    'invalidate',
    'getWebRTCIPHandlingPolicy',
    'setWebRTCIPHandlingPolicy',
    'getOSProcessId',
    'getProcessId',
    'getBackgroundThrottling',
    'setBackgroundThrottling'
  ],
  webContentsInvokes: [
    'insertCSS',
    'removeInsertedCSS',
    'executeJavaScript',
    'executeJavaScriptInIsolatedWorld',
    'setVisualZoomLevelLimits',
    'insertText',
    'capturePage',
    'print',
    'printToPDF',
    'savePage',
    'takeHeapSnapshot'
  ]
};
const ipcRenderer  = window.ipcRenderer;


export interface IpcRendererUtilsOptions {
  initCallback?: (initData: any) => void
}

const defaultIpcRendererUtilsOptions: IpcRendererUtilsOptions = {};

export class WinHelper {
  private __mainId?: number;
  private winId?: number;
  // private receiptMessageMap = new Map<string, any>();
  private currentWindow?:  BrowserWindow;

  private macTransparencyTimer?: NodeJS.Timer;

  private __data: Record<string, any> = {};

  private macForward: boolean = true;


  /**
   * 构造方法
   * @param initCallback 初始化回调函数
   * @param windowCloseBefore 窗口关闭前不提供不绑定
   */
  constructor({initCallback} = {...defaultIpcRendererUtilsOptions}) {
    ipcRenderer.on('init', (event, data) => {
      this.__mainId = event.senderId;
      this.winId = data.winId;
      this.initCurrentWindow(data.winId);
      this.macTransparencyWindow();
      window.addEventListener('beforeunload', this.handleDestroy)
      if (initCallback) {
        initCallback(data);
      }
    });

    // ipcRenderer.on('receipt', (event, data) => {
    //   const receiptId =  data['_receiptId'];
    //   delete data['_receiptId'];
    //   this.receiptMessageMap.set(receiptId, data);
    // })

    // 清理回执消息
    // setInterval(() => this.clearReceiptMessageMap(), 30 * 1000)
  }

  /**
   * 获取当前窗口所在显示屏
   */
  getDisplayByWinPosition() {
    const bounds = this.getCurrentWindow().getBounds();
    return utools.getDisplayNearestPoint(bounds);
  }


  /**
   * 根据鼠标位置获取位置
   */
  static getDisplayByCursor() {
    const screenPoint = utools.getCursorScreenPoint();
    return utools.getDisplayNearestPoint(screenPoint);
  }
  /**
   * 发送信息到主窗口
   * @param channel
   * @param data
   */
  sendMainMessage(data: Record<string, any> | string = {}) {
    if (!this.__mainId) {
      throw new Error('未获取通信窗口的 id');
    }
    ipcRenderer.sendTo(this.__mainId, 'main.api', data);
  }

  /**
   * mac 透明窗口专用
   */
  refreshWindow() {
    const currentWindow = window.winHelper.getCurrentWindow();
    const bounds = currentWindow.getBounds();
    const display = utools.getDisplayNearestPoint(bounds);
    if (!currentWindow.isSimpleFullScreen()) {
      currentWindow.setSize(display.bounds.width - 10, display.bounds.height - 10);
      setTimeout(() => {
        currentWindow.setSize(bounds.width, bounds.height);
      });
    }
  }

  // /**
  //  * 发送信息到主窗口并获取主窗口到返回值
  //  * @param channel 通道
  //  * @param data 数据
  //  * @param timeout 获取超时时间, 默认 2000ms
  //  * @returns {Promise<any>}
  //  */
  // sendSyncMainMessage(channel: string, data: Record<string, any> = {}, timeout = 2000): Promise<any> {
  //   const receiptId = this.getReceiptId(channel, timeout);
  //   data['_receiptId'] = receiptId;
  //   this.sendMainMessage(data);
  //   let tryCount = timeout / 100;
  //   return new Promise((resolve, reject) => {
  //     const timer = setInterval(() => {
  //       tryCount--;
  //       const res = this.receiptMessageMap.get(receiptId);
  //       if (res) {
  //         this.receiptMessageMap.delete(receiptId);
  //         resolve(res);
  //         clearInterval(timer);
  //       }
  //       if (tryCount <= 0) {
  //         reject(`超时未获取到返回值---channel:${channel}`)
  //         clearInterval(timer);
  //       }
  //     }, 100)
  //   });
  // }

  // clearReceiptMessageMap() {
  //   const clearKey: string[] = [];
  //   const now = Date.now()
  //   for (const [key] of this.receiptMessageMap.entries()) {
  //     const receiptKeyObj = this.parseReceiptId(key)
  //     const expireTime = receiptKeyObj.createTime + receiptKeyObj.timeout + 30 * 1000
  //     if (now > expireTime) {
  //       clearKey.push(key);
  //     }
  //   }
  //
  //   clearKey.map(key => this.receiptMessageMap.delete(key));
  // }

  async invokePluginApi(api: string, args: any) {
    return await ipcRenderer.invoke("plugin.api",api, args);
  }

  sendPluginApi(api: string, args: any) {
    return ipcRenderer.sendSync("plugin.api",api, args);
  }

  // getReceiptId(channel: string, timeout: number) {
  //   return channel + ':' + Date.now() +':'+ timeout + ':' + nanoid();
  // }
  //
  // parseReceiptId(receiptId: string) {
  //   const [channel, createTime, timeout, id] = receiptId.split(":");
  //   return {
  //     channel,
  //     createTime: Number(createTime),
  //     timeout: Number(timeout),
  //     id
  //   }
  // }

  getBrowserWindow(otherWindowsId: number) {
    const winObj: Record<string, any> = {} as any;
    winObj.id = otherWindowsId;
    winIpc.windowMethods.map((key) => {
      winObj[key] = (...args: any[]) => this.sendPluginApi('pluginBrowserWindowMethod', { id: otherWindowsId, method: key, args });
    });
    winIpc.windowInvokes.map(key => {
      winObj[key] = (...args: any[]) => this.invokePluginApi('pluginBrowserWindowInvoke', {id: otherWindowsId, method: key, args});
    });
    winObj.webContents = {};
    winIpc.webContentsMethods.map(key => {
      winObj.webContents[key] = (...args: any[]) =>
        this.sendPluginApi('pluginBrowserWindowMethod',
          { id: otherWindowsId, target:"webContents", method: key, args })
    })
    winIpc.webContentsInvokes.map(key => {
      winObj.webContents[key] = (...args: any[]) =>
        this.invokePluginApi('pluginBrowserWindowInvoke',
          { id: otherWindowsId, target:"webContents", method: key, args })
    });

    return winObj as BrowserWindow;
  }


  initCurrentWindow(winId: number) {
    this.currentWindow = this.getBrowserWindow(winId);
  }

  /**
   * 获取当前窗口对象只有部分的属性
   */
  getCurrentWindow(): BrowserWindow {
    if (!this.currentWindow) {
      throw new Error('请在初始化之后调用本方法');
    }
    return this.currentWindow;
  }

  get ipcRenderer() {
    return ipcRenderer;
  }

  private macTransparencyWindow() {
    const background =  this.currentWindow?.getBackgroundColor();
    console.log('macTransparencyWindow', utools.isMacOS(),  background , background === '#000000')
    if (utools.isMacOS() && background && background === '#000000') {
      this.macTransparencyTimer = this.macStrike(this.currentWindow!)
    }
  }

  private handleDestroy() {
    // 释放资源
    if (this.macTransparencyTimer) {
      // @ts-ignore
      clearInterval(this.macTransparencyTimer)
    }
    this.currentWindow = undefined;
  }

  setData<T>(key: string, data: T) {
    this.__data[key] = data;
  }

  getData<T>(key: string): T {
    return this.__data[key];
  }


  async  updateIgnoreMouseEvents (win: BrowserWindow, x: number, y: number) {

    // capture 1x1 image of mouse position.
    const image = await win.webContents.capturePage({
      x,
      y,
      width: 1,
      height: 1,
    });

    const buffer = image.getBitmap();

    // console.log('!buffer[3]', !buffer[3])
    // set ignore mouse events by alpha.
    win.setIgnoreMouseEvents(!buffer[3], { forward: this.macForward });
    // console.log("setIgnoreMouseEvents", !buffer[3]);
  }

  macStrike(win: BrowserWindow) {
    const timer = setInterval(() => {
      const point = utools.getCursorScreenPoint();
      const [x, y] = win.getPosition();
      const [w, h] = win.getSize();

      if (point.x > x && point.x < x + w && point.y > y && point.y < y + h) {
        this.updateIgnoreMouseEvents(win, point.x - x, point.y - y).then(() => {})
      }
    }, 350);
    return timer;
  }

  get mainId() {
    return this.__mainId;
  }
}
export default WinHelper;
