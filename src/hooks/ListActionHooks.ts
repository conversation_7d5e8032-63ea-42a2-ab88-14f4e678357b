// 一些列表元素操作的 hooks
import { ref } from "vue";
import type { Ref } from "vue";

type ActionIdType = string | number;

/**
 * 切换列表元素操作状态[允许同时编辑多个]
 * @return [当前在操作中的元素集合, 切换集合]
 */
export function useListElementEditActions(): [Ref<ActionIdType[]>, (id: ActionIdType) => void] {
  const actionElementList = ref<ActionIdType[]>([]);

  // 切换元素激活状态
  function switchover(id: ActionIdType) {
    const index = actionElementList.value.indexOf(id);
    if (index === -1) {
      actionElementList.value.push(id);
    } else {
      actionElementList.value.splice(index, 1);
    }
  }
  return [actionElementList, switchover];
}


/**
 * 切换列表元素操作状态[允许同时编辑一个]
 * @return [当前在操作中的元素集合, 切换集合]
 */
export function useListElementEditAction(): [Ref<ActionIdType>, (id: ActionIdType) => void] {
  const actionElement = ref<ActionIdType>();

  // 切换元素激活状态
  function switchover(id: ActionIdType) {
    console.log('useListElementEditAction.switchover', id);
    actionElement.value = id;
  }

  return [actionElement, switchover];
}
