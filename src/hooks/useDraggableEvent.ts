import { ref } from 'vue'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'

export interface IUseWindowDragOptions {
}

export class WindowSimpleDrag {
  private __options: IUseWindowDragOptions;
  private __page: { x: number; y: number } | null = null;
  private __down = ref(false);
  private __el: HTMLElement | null;

  constructor() {
  }

  start() {
    // this.__el = document.querySelector(this.__options.querySelector || 'body');
    // this.__el.addEventListener('mousedown', this.onMouseDown.bind(this));
    // this.__el.addEventListener('mousemove', this.onMouseMove.bind(this));
    // this.__el.addEventListener('mouseup', this.onMouseUp.bind(this));
    document.addEventListener('keypress', this.keypress);
    document.addEventListener('keyup', this.keyup);
  }

  keypress(e: KeyboardEvent) {
    console.log('keypress....')
    const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
    const screenshotDrag = document.getElementById('screenshotDrag');
    screenshotDrag.style.display = 'block';
    // @ts-ignore
    if (screenshotDrag.style.display !== 'block') {
      screenshotDrag.style.display = 'block';
      // @ts-ignore
      screenshotDrag.style['-webkit-app-region'] = 'drag';
    }
  }

  keyup(e: KeyboardEvent) {
    console.log('keyup', e)
    const screenshotDrag = document.getElementById('screenshotDrag')!;
    screenshotDrag.style.display = 'none';
  }
  stop() {
    this.__down.value = false;
    document.removeEventListener ('keypress', this.keypress)
    document.removeEventListener('keyup', this.keyup);
  }
}


