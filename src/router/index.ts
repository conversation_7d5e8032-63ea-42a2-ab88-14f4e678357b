import { createRouter, createWebHashHistory, type RouteRecordRaw } from 'vue-router'
import type { MenuRouterItem } from '@xiaou66/u-web-ui';

// @unocss-include
export const homeRoutes: MenuRouterItem[] = [
  {
    name: 'fileManager',
    path: 'fileManager',
    component: () => import('@/views/fileManager/fileManager.vue'),
    meta: {
      menu: true,
      title: '文件盒子',
      icon: 'i-u-receive',
    }
  },
  {
    name: 'userSetting',
    path: 'userSetting',
    component: () => import('@/views/userSetting'),
    meta: {
      menu: true,
      title: '截图设置',
      icon: 'i-u-config',
    }
  },
  {
    name: 'toolSetting',
    path: 'toolSetting',
    component: () => import('@/views/toolSetting/toolSetting.vue'),
    meta: {
      menu: true,
      title: '工具设置',
      icon: 'i-u-tool',
    }
  },
  {
    name: 'sceneManager',
    path: 'sceneManager',
    component: () => import('@/views/sceneManager/sceneManager.vue'),
    meta: {
      menu: true,
      title: '场景管理',
      icon: 'i-u-application-effect',
    }
  },
  {

    name: 'themeSetting',
    path: 'themeSetting',
    component: () => import('@/views/themeSetting/themeSetting.vue'),
    meta: {
      menu: true,
      title: '样式',
      icon: 'i-u-theme',
    }
  },
  {
    name: 'keyboardShortcut',
    path: 'keyboardShortcut',
    component: () => import('@/views/keyboardShortcut/keyboardShortcut.vue'),
    meta: {
      menu: true,
      title: '快捷键管理',
      icon: 'i-u-keyboard-one',
    }
  },
  {
    name: 'dataSetting',
    path: 'dataSetting',
    component: () => import('@/views/dataSetting/dataSetting.vue'),
    meta: {
      menu: true,
      title: '数据管理',
      icon: 'i-u-data-all',
    }
  },
  {
    name: 'chargeStore',
    path: 'chargeStore',
    component: () => import('@/views/chargeStore/chargeStore.vue'),
    meta: {
      menu: true,
      title: '购买商店',
      icon: 'i-u-buy',
    }
  },
  {
    name: 'FAQ',
    path: 'FAQ',
    component: () => import('@/views/frequentlyAskedQuestion'),
    meta: {
      menu: true,
      title: '帮助中心',
      icon: 'i-u-help',
    }
  },
]
// 路由列表
export const routes: Readonly<RouteRecordRaw[]> = [
  {
    name: 'home',
    path: '/home',
    component: () => import('@/views/Home.vue'),
    children: homeRoutes,
  },
  {
    name: 'historyList',
    path: '/historyList',
    component: () => import('@/views/historyList/historyList.vue'),
  }
  // {
  //   name: 'updateRecord',
  //   path: '/updateRecord',
  //   component: () => import('@/views/updateRecord/updateRecord.vue'),
  //   meta: {
  //     title: '更新记录',
  //     icon: 'update-rotation',
  //   }
  // },
];

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes
})
router.beforeEach((to, from, next) => {
  next();
})
export default router
