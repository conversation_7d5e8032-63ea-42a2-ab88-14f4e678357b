import Cocoa
import Foundation
import CoreAudio
import AudioToolbox

// 定义媒体按键的常量
enum MediaKey: UInt32 {
    case soundUp = 0
    case soundDown = 1
    case play = 16
    case next = 17
    case previous = 18
    case fast = 19
    case rewind = 20
}

// 封装发送媒体按键事件的函数
func HIDPostAuxKey(key: <PERSON>Key) {
    let runLoop = RunLoop.current

    func doKey(down: Bool) {
        let flags = down ? 0xa00 : 0xb00
        let data1 = (Int(key.rawValue) << 16) | ((down ? 0xa : 0xb) << 8)

        if let event = NSEvent.otherEvent(
            with: .systemDefined,
            location: .zero,
            modifierFlags: NSEvent.ModifierFlags(rawValue: UInt(flags)),
            timestamp: 0,
            windowNumber: 0,
            context: nil,
            subtype: 8,
            data1: data1,
            data2: -1
        ) {
            event.cgEvent?.post(tap: .cghidEventTap)
            runLoop.run(until: Date(timeIntervalSinceNow: 0.01))
        }
    }

    doKey(down: true)
    doKey(down: false)
    runLoop.run(until: Date(timeIntervalSinceNow: 0.05))
}

// 检查系统音频输出
func isSystemAudioPlaying() -> Bool {
    var defaultOutputDeviceID: AudioDeviceID = 0
    var propertySize = UInt32(MemoryLayout<AudioDeviceID>.size)
    var propertyAddress = AudioObjectPropertyAddress(
        mSelector: AudioObjectPropertySelector(kAudioHardwarePropertyDefaultOutputDevice),
        mScope: AudioObjectPropertyScope(kAudioObjectPropertyScopeGlobal),
        mElement: AudioObjectPropertyElement(kAudioObjectPropertyElementMain)
    )

    guard AudioObjectGetPropertyData(
        AudioObjectID(kAudioObjectSystemObject),
        &propertyAddress,
        0,
        nil,
        &propertySize,
        &defaultOutputDeviceID
    ) == 0 else {
        return false
    }

    var isRunning: UInt32 = 0
    propertySize = UInt32(MemoryLayout<UInt32>.size)
    var address = AudioObjectPropertyAddress(
        mSelector: AudioObjectPropertySelector(kAudioDevicePropertyDeviceIsRunningSomewhere),
        mScope: AudioObjectPropertyScope(kAudioObjectPropertyScopeGlobal),
        mElement: AudioObjectPropertyElement(kAudioObjectPropertyElementMain)
    )

    guard AudioObjectGetPropertyData(
        defaultOutputDeviceID,
        &address,
        0,
        nil,
        &propertySize,
        &isRunning
    ) == 0 else {
        return false
    }

    return isRunning != 0
}

// 检查当前是否有媒体正在播放
func isMediaPlaying() -> Bool {
    if isSystemAudioPlaying() {
        return true
    }


    return false
}

// 定义支持的命令映射
let supportedCommands: [String: MediaKey] = [
    "playpause": .play,
    "next": .next,
    "prev": .previous,
    "volup": .soundUp,
    "voldown": .soundDown
]

// 处理命令行参数
let arguments = CommandLine.arguments

if arguments.count > 1 {
    let command = arguments[1]

    if command == "status" {
        print(isMediaPlaying() ? "playing" : "stopped")
    } else if let mediaKey = supportedCommands[command] {
        HIDPostAuxKey(key: mediaKey)
    } else {
    }
} else {
}
