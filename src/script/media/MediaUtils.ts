import DataUtils from '@/utils/DataUtils'
import { NotifyPlugin } from 'tdesign-vue-next'
import EnvironmentUtils from '@/utils/EnvironmentUtils'

function getCscPath() {
  const is64bit = window.nodeProcess.arch === "x64";
  let cscPath = window.path.join(
    window.nodeProcess.env.WINDIR,
    "Microsoft.NET",
    is64bit ? "Framework64" : "Framework",
    "v4.0.30319",
    "csc.exe"
  );
  let version = "v4.0";

  if (!window.fs.existsSync(cscPath)) {
    cscPath = window.path.join(
      window.nodeProcess.env.WINDIR,
      "Microsoft.NET",
      is64bit ? "Framework64" : "Framework",
      "v3.5",
      "csc.exe"
    );
    version = "v3.5";
  }
  if (!window.fs.existsSync(cscPath)) {
    throw new Error("未安装.NET Framework");
  }
  return { path: cscPath, version };
}

function encodeCommand(command: string) {
  return window.nodeBuffer.Buffer.from(command, 'utf16le').toString('base64');
}

function getExeFilePath() {
  if (utools.isWindows()) {
    return window.path.join(DataUtils.getDataSaveLibPath(), "u-media.exe");
  } else if (utools.isMacOS()) {
    return window.path.join(DataUtils.getDataSaveLibPath(), "u-media");
  }
}

async function windowInstall() {
  try {
    if (window.fs.existsSync(getExeFilePath())) {
      return;
    }
    if (window.nodeProcess.arch === "x64") {
      // 远程下载
      await EnvironmentUtils.downloadResource(DataUtils.getDataSaveLibPath(), 'u-media:win64')
      return;
    }
    NotifyPlugin.info({
      title: '安装提示',
      content: '当前系统类型作者提供支持库不支持, 接下来将自动进行编译运行'
    });
    // 编译脚本文件
    const { path: cscPath } = getCscPath();
    const res = await import('./media.cs?raw').then((res) => res.default)
    const scriptFilePath = window.path.join(utools.getPath('temp'), Date.now() + '.cs');
    window.fs.writeFileSync(scriptFilePath, res, 'utf8');
    try {
      window.execSync(`"${cscPath}" /out:${getExeFilePath()} ${scriptFilePath}`);
      NotifyPlugin.success({
        title: '安装提示',
        content: '编译成功',
      });
    } catch (e: any) {
      NotifyPlugin.error({
        title: '安装失败',
        content: '编译失败' + e.message,
      });
    } finally {
      window.fs.unlinkSync(scriptFilePath);
    }
  }catch (e: any) {
    NotifyPlugin.error({
      title: '安装失败',
      content: e.message,
    });
  }
}

async function macInstall() {
  try {
    if (window.fs.existsSync(getExeFilePath())) {
      return;
    }
    if (window.nodeProcess.arch === "arm64") {
      // 远程下载
      await EnvironmentUtils.downloadResource(DataUtils.getDataSaveLibPath(), 'u-media:mac-arm')
      return;
    }
    NotifyPlugin.info({
      title: '安装提示',
      content: '当前系统类型作者提供支持库不支持, 接下来将自动进行编译运行'
    });
    // 编译脚本文件
    const res = await import('./media.swift?raw').then((res) => res.default)
    const outputPath = getExeFilePath();
    try {
      window.execSync(`echo '${res}' | swiftc - -o "${outputPath}"`)
      NotifyPlugin.success({
        title: '安装提示',
        content: '编译成功',
      });
    } catch (e: any) {
      NotifyPlugin.error({
        title: '安装失败',
        content: '编译失败' + e.message,
      });
    }
  }catch (e: any) {
    NotifyPlugin.error({
      title: '安装失败',
      content: e.message,
    });
  }
}
async function install() {
  if (utools.isWindows()) {
    await windowInstall();
  } else if (utools.isMacOS()) {
    await macInstall();
  }
}


async function pausePause() {
  if (utools.isLinux()) {
    return
  }
  const exeFilePath = getExeFilePath();
  if (!window.fs.existsSync(exeFilePath)) {
    await install();
  }
  try {
    window.exec(`"${exeFilePath}" playpause`);
  } catch (e) {
    console.log('播放出现问题', e);
  }
}
function playStatus() {
  return true;
}

export default {
  install,
  pausePause,
  playStatus
}

