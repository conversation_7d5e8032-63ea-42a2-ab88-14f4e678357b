import dayjs from 'dayjs'

async function getAccessToken(config: { apiKey: string; secretKey: string }) {
  const formData = new FormData()
  formData.set('grant_type', 'client_credentials')
  formData.set('client_id', config.apiKey)
  formData.set('client_secret', config.secretKey)
  return await fetch('https://aip.baidubce.com/oauth/2.0/token', {
    method: 'POST',
    body: formData
  })
    .then((res) => res.json())
    .then((res) => {
      return {
        token: res.access_token,
        expireTime: dayjs().add(res.expires_in).valueOf()
      }
    })
}

async function getGeneralOcr(base64: string, accessToken: string) {
  const formData = new FormData()
  formData.set('image', base64)
  formData.set('recognize_granularity', 'small')
  formData.set('detect_language', 'true')
  formData.set('vertexes_location', 'false')
  formData.set('paragraph', 'false')
  formData.set('probability', 'false')
  return await fetch(
    `https://aip.baidubce.com/rest/2.0/ocr/v1/general?access_token=${accessToken}`,
    {
      method: 'POST',
      body: formData
    }
  ).then((res) => res.json())
}

async function getTranslateText(content: string, accessToken: string) {
  return await fetch(
    `https://aip.baidubce.com/rpc/2.0/mt/texttrans/v1?access_token=${accessToken}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=utf-8'
      },
      body: JSON.stringify({
        q: content,
        from: 'auto',
        to: 'zh'
      })
    }
  ).then((res) => res.json())
}

async function getTranslateImage(blob: Blob, accessToken: string) {
  const formData = new FormData()
  formData.set('image', blob)
  formData.set('from', 'auto')
  formData.set('to', 'zh')
  formData.set('v', '3')
  return await fetch(
    `https://aip.baidubce.com/file/2.0/mt/pictrans/v1?access_token=${accessToken}`,
    {
      method: 'POST',
      body: formData
    }
  ).then((res) => res.json())
}

async function fyTranslateImage(options: any) {
  const params = `from=auto&to=zh&appid=${options.appid}&salt=${options.salt}&sign=${options.sign}&cuid=APICUID&mac=mac&version=3`
  const formData = new FormData();
  formData.set('image', options.image)
  formData.set('from', 'auto')
  formData.set('to', 'zh')
  formData.set('appid', options.appid)
  formData.set('salt', options.salt)
  formData.set('sign', options.sign)
  formData.set('cuid', options.cuid)
  formData.set('mac', options.mac)
  return await fetch(
    `https://fanyi-api.baidu.com/api/trans/sdk/picture?${params}`,
    {
      method: 'POST',
      body: formData
    }
  ).then((res) => res.json())
}

export default {
  getAccessToken,
  getGeneralOcr,
  getTranslateText,
  getTranslateImage,
  fyTranslateImage
}
