const productId = 660084;
export async function getUpdateLogs() {
  return await fetch(`https://support.qq.com/api/v2/${productId}/posts/label/feat?_=${Date.now()}`)
    .then((res) => {
      return res.text();
    })
    .then((res) => JSON.parse(res))
    .then((res) => {
      if (res.data instanceof Object) {
        res.data = Object.keys(res.data).map((item) => {
          const dataItem = res.data[item];
          dataItem.content = JSON.parse(dataItem.content);
          dataItem.content.content = JSON.parse(dataItem.content.content);
          dataItem.content.thank_post_id_list = dataItem.content.thank_post_id_list.map(
            (item: any) => JSON.parse(item)
          );
          if (dataItem.replies instanceof Object) {
            dataItem.replies = Object.keys(dataItem.replies).map((i) => dataItem.replies[i]);
          }
          return dataItem;
        });
      }
      return res;
    })
    .then((res) => res);
}

export interface FaqCategoryItem {
  id: number;
  title: string;
}

/**
 * 获取 FAQ 分类
 */
export async function getFaqCategories(): Promise<FaqCategoryItem[]> {
  return await window.nodeHttp<any>({
    url: `https://support.qq.com/api/v2/${productId}/faqs/categories_list?t=${Date.now()}`,
    parse: 'json',
    timeout: 2000,
    headers: {
      referer: `https://support.qq.com/products/${productId}`
    }
  }).then(res => res.body)
    .then(res => res.data.list);
}

export interface FaqItem {
  id: number;
  faq_id: number;
  title: string;
  updated_at: string;
}
export async function getFaqList(categoryId: number): Promise<FaqItem[]> {
  return await window.nodeHttp<any>({
    url: `https://support.qq.com/api/v2/${productId}/faqs/categories_list/${categoryId}?categories_only=0&scene=&_=${Date.now()}`,
    parse: 'json',
    timeout: 2000,
    headers: {
      referer: `https://support.qq.com/products/${productId}`
    }
  }).then(res => res.body)
    .then(res => res.data.list);
}


export interface FaqContentDetail {
  id: number;
  title: string;
  content: string;
  created_at: string;
  read_total: number;
}

export async function getFaqContentDetail(faqId: number):Promise<FaqContentDetail> {
  console.log('faqId', faqId)
  return await window.nodeHttp<any>({
    url: `https://support.qq.com/api/v2/${productId}/faqs/${faqId}?_=1720229208089`,
    parse: 'json',
    timeout: 2000,
    headers: {
      referer: `https://support.qq.com/products/${productId}`
    }
  }).then(res => res.body)
    .then(res => res.data);
}
