
export interface ICloudOcrApiRequest {
  image: string;
}

export interface  ICloudOcrApiWordsResultItem {
  chars: {
    char: string;
    location: {
      top: number;
      left: number;
      width: number;
      height: number;
    };
  }[];
  words: string;
  location: {
    top: number;
    left: number;
    width: number;
    height: number;
  }
}
export interface ICloudOcrApiResponse {
  wordsResult: ICloudOcrApiWordsResultItem[]
}

async function ocr(data: ICloudOcrApiRequest): Promise<ICloudOcrApiResponse> {
  return new Promise((resolve, reject) => {
    fetch(`${import.meta.env.VITE_API_BASE_URL}/api-center/ocr`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    }).then(res => res.json())
      .then(res => {
        if (res.code === 0) {
          resolve(res.data);
        } else {
          reject(res.msg);
        }
      })
  })
}


export default {
  ocr
};
