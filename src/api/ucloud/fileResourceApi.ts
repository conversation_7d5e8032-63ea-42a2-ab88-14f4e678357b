
/**
 * 根据资源 code
 * @param resourceCode 资源 code
 */
async function getResource(resourceCode: string) {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/fileResource/getResource?resourceCode=${resourceCode}`)
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}

export default {
  getResource
}
