export interface GoodDetailRespVo {
  goodTitle: string;
  goodInventory: number;
  goodStatus: number;
}
export async function getGoodDetailApi(goodId: number): Promise<GoodDetailRespVo> {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/sys/goods/detail?goodId=${goodId}`)
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}
