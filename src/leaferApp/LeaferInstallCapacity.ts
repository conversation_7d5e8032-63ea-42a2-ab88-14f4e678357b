import { type App, Box, Group, Image, ImageEvent, Rect, ZoomEvent } from 'leafer-ui'
import { Editor, EditorEvent } from '@leafer-in/editor'
import LeaferElement from '@/leaferApp/LeaferElement'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import ScreenshotFunctions from '@/leaferApp/ScreenshotCapacity'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import KeyboardsConstants from '@/core/constants/KeyboardsConstants'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import type WatermarkTool from '@/components/toolbox/WatermarkTool/WatermarkTool'
import LeaferHelper from '@/leaferApp/LeaferHelper'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import jsUtil from '@/utils/jsUtil'
import { setCurrentTheme } from '@/hooks/useTheme'
import './custom/SupperArrow/SupperArrowEditTool'
// 状态
import '@leafer-in/state'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import DataUtils from '@/utils/DataUtils'
import { MainImageLoadedEvent } from '@/events/MainImageLoadedEvent'
import { ElementLoadEvent } from '@/events/ElementLoadEvent'

/**
 * 加载背景层
 * @param app
 */
function loadBackground(app: App) {
  const background = new Rect({
    id: LeaferConstant.ElementBackground,
    width: app.width,
    height: app.height,
    hittable: false,
    fill: undefined,
  });
  app.ground.add(background);
}


/**
 * 加载批量选择的限制
 * @param app
 */
function loadEditorSelectImposeAction(app: App) {
  app.editor.on(EditorEvent.SELECT, (e: EditorEvent) => {
    const target = LeaferHelper.formatIUIArray(e.value);
    const editorConfigList = target.filter(item => item.editConfig);
    console.log('editorConfigList', editorConfigList);
    if (!target.length
      || !editorConfigList
      || !editorConfigList.length
      || editorConfigList.length === 1) {
      LeaferHelper.cleanAppEditorImposeAction(app);
      return;
    }

    const editConfig = editorConfigList
      .map(item => item.editConfig)
      .reduce((previousValue, currentValue) => {
        return {...previousValue, ...currentValue};
      }, {});

    if (editConfig.resizeable === false) {
      app.editor.config.resizeable = editConfig.resizeable;
    }
    if (editConfig.rotateable === false) {
      app.editor.config.rotateable = editConfig.rotateable;
    }
    if (editConfig.moveable === false) {
      app.editor.config.moveable = editConfig.moveable;
    }
    if (editConfig.skewable === false) {
      app.editor.config.skewable = editConfig.skewable;
    }
    if (editConfig.flipable === false) {
      app.editor.config.flipable = editConfig.flipable;
    }


    // @ts-ignore
    console.log('editConfig.disableMultiple', editConfig.disableMultiple)
    // @ts-ignore
    if (editConfig.disableMultiple) {
      // @ts-ignore
      for (const config of editConfig.disableMultiple) {
        // @ts-ignore
        app.editor.config[config] = false;
      }
    }
  });
}

/**
 * 加载马赛克层
 * @param app
 */
function loadMosaic(app: App) {
  const mosaicGroup = new Group({
    id: LeaferConstant.ElementMosaic,
    draggable: false,
    selected: false,
  });
  app.tree.add(mosaicGroup);
}

/**
 * 加载 drawGroup 层
 * @param app
 */
function loadDrawGroup(app: App) {
  const drawGroup = new Group({
    id: LeaferConstant.ElementDrawGroup,
    draggable: false,
    selected: false,
  });
  app.tree.add(drawGroup);
}

/**
 * 在绘画元素之上
 * @param app
 */
function loadMiddleGroup(app: App) {
  const middleGroup = new Box({
    id: LeaferConstant.ElementMiddleGroup,
    draggable: false,
    selected: false,
  });
  app.tree.add(middleGroup);
}

function installAllGroup(app: App) {
  loadMosaic(app);
  loadDrawGroup(app);
  loadMiddleGroup(app);
}

/**
 * 安装删除元素
 * @param app
 */
function installDeleteElement(app: App) {
  const keyboardConfigStore = useKeyboardConfigStore();
  const keyboardConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(KeyboardsConstants.DELETE_ELEMENT);
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.DELETE_ELEMENT, keyboardConfig, {
    downHandle: (e) => {
      if (app.editor.target
        && app.editor.config.keyEvent) {
        const target = app.editor.target;
        if (target instanceof Array) {
          target.map(item => LeaferHelper.removeElement(app, item));
        } else  {
          LeaferHelper.removeElement(app, target);
        }
        LeaferHelper.exitEditor(app);
      }
    }
  });
}

/**
 * 安装缩放
 * @param app
 */
function installZoom(app: App) {
  app.tree.on(ZoomEvent.BEFORE_ZOOM, () => {
    console.log('ZoomEvent.BEFORE_ZOOM')
    const currentWindow = window.winHelper.getCurrentWindow()
    const bounds = currentWindow.getBounds();
    const display = utools.getDisplayNearestPoint(bounds);
    if (display.bounds.width !== bounds.width) {
      currentWindow.setSize(display.bounds.width - 50, display.bounds.height - 50, false)
      currentWindow.setPosition(bounds.x, bounds.y, false)
    }
    return false;
  })
  const toolConfigStore = useToolConfigStore();
  const currentToolBoxStore = useCurrentToolBoxStore()
  app.tree.on(ZoomEvent.ZOOM, (e: ZoomEvent) => {
    const scale = app.tree.zoomLayer.scale as number;
    console.log('scale', scale)
    if (scale >= 4) {
      return;
    }
    console.log('ZoomEvent.ZOOM', e);
    console.log('app', app.width);
    LeaferHelper.setCanvasSize(app, {width: app.width! * e.scale, height: app.height! * e.scale}, false);

    // draw 容器
    const drawGroup = app.tree.findId(LeaferConstant.ElementDrawGroup);
    drawGroup.set({
      x: 0,
      y: 0,
      width: app.width,
      height: app.height,
    });
    // middle 容器
    const middleGroup = app.tree.findId(LeaferConstant.ElementMiddleGroup);
    middleGroup.set({
      x: 0,
      y: 0,
      width: app.width,
      height: app.height,
    });

    const watermarkTool = currentToolBoxStore.getToolByCode<WatermarkTool>('watermark');
    if (watermarkTool) {
      watermarkTool.canvasSizeChange();
    }



    LeaferHelper.pageAlign(app, toolConfigStore.layoutConfig.align, LeaferElement.getScreenshotGroup(app));
  });
  app.tree.on(ZoomEvent.END, () => {
    LeaferHelper.autoAdjustWindowSize(app, true)
  });
}
/**
 * 安装贴图
 * @param app
 */
function installPastePicture(app: App) {
  const keyboardConfigStore = useKeyboardConfigStore();
  const config = keyboardConfigStore.getDefaultFuncKeyboardConfig(KeyboardsConstants.PASTE_PICTURE);
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.PASTE_PICTURE, config, {
    downHandle: () => {
      const clipboardImage = window.clipboard.readImage('clipboard');
      let base64 = null;
      if (!clipboardImage.isEmpty()) {
        const fileUrl = window.clipboard.read('public.file-url');
        console.log('fileUrl',  fileUrl)

        if (fileUrl) {
          base64 = window.nativeImage.createFromPath(fileUrl.replace('file://', '')).toDataURL();
        } else {
          base64 = clipboardImage.toDataURL()
        }
      } else if (utools.isWindows()) {
        const filePath = window.clipboard.readBuffer('FileNameW')
          .toString('ucs2')
          .replace(RegExp(String.fromCharCode(0), 'g'), '');
        const nativeImage = window.nativeImage.createFromPath(filePath);
        if (nativeImage.isEmpty()) {
          return;
        }
        base64 = nativeImage.toDataURL();
      }
      if (base64) {
        window.leaferHistoryList.addHistory();
        const image = new Image({
          url: base64,
          editable: true,
        });
        app.tree.findId(LeaferConstant.ElementMiddleGroup)
          .add(image);
      }
    }
  })
}

/**
 * 安装复制截图
 * @param app
 */
function installCopyPicture(app: App) {
  const keyboardConfigStore = useKeyboardConfigStore();
  const config = keyboardConfigStore.getDefaultFuncKeyboardConfig(KeyboardsConstants.COPY_PICTURE);
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.COPY_PICTURE, config, {
    downHandle: () => {
      ScreenshotCapacity.exportScreenshotDataUrl(app)
        .then((res) => {
          utools.copyImage(res);
        });
    }
  })
}



function capture(app: App, image: Image) {
  const group = new Group({
    id: 'captureContainerGroup',
  });
  app.tree.add(group);
  const box = new Box({
    id: LeaferConstant.ElementCaptureContainerCover,
    width: image.width,
    height: image.height,
    fill: 'rgba(0, 0, 0, 0.5)',
  });
  group.add(box);
  const chooseAreaRect = new Rect({
    id: LeaferConstant.ElementChooseAreaRect,
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    editable: true,
    fill: 'black',
    eraser: true,
    dragBounds: 'parent',
    cursor: 'pointer',
  });
  group.add(chooseAreaRect);
  const editor = new Editor({
    selector: false,
  });
  editor.hittable = false;
  app.sky.addAt(editor, 0)
  editor.target = chooseAreaRect;
}



function setCaptureCanvasSize(app: App,
                                     image: Image) {
  const width = image.width!;
  const height = image.height!;
  const view = app.view as HTMLDivElement;
  view.style.width = width + 'px';
  view.style.height = height + 'px';
  console.log(width, height);
  app.resize({width, height });
  app.tree.resize({width, height});
  const currentWindow = window.winHelper.getCurrentWindow();
  currentWindow.show();
}


function loadSceneConfig() {
  // 加载配置文件
  const sceneStore = useSceneStore();
  const sceneConfig = sceneStore.getSceneConfig(window.sceneCode)
  new ConfigReloadEvent(sceneConfig.disableAutoSaveTools, sceneConfig.saveMode !== 'auto')
    .dispatchEvent();
}
function loadScreenshot(app: App,
                        imageData: string,
                        hideToolBar = false,
                        autoSize = false) {
  const settingUserStore = useSettingUserStore();
  if (settingUserStore.generalSetting.toolTheme === 'imageColor') {
    jsUtil.isBrightImage(imageData)
      .then(res => {
        setCurrentTheme(res ? 'default' : 'dark');
      });
  }
  const image = new Image({
    id: 'mainImage',
    url: imageData,
    // draggable: true,
    // editable: true,
    hittable: false,
    x: 0,
    y: 0,
  });
  image.x = 0;
  image.y =  0;

  const {generalSetting: {globalDefaultTool, toolbarShowMode}} = useSettingUserStore();
  const currentToolBoxStore = useCurrentToolBoxStore();



  image.on(ImageEvent.LOADED, function (e: ImageEvent) {
    const scaleFactor= window.winHelper.getDisplayByWinPosition().scaleFactor || 1;
    console.log('display.scaleFactor', scaleFactor);
    image.set({
      scale: 1 / scaleFactor,
    });
    // debugger
    console.log('display.scaleFactor', image.getBounds());
    LeaferHelper.setCanvasSize(app, image.getBounds(), true, autoSize);
    if (window.environment === 'screenshot') {
      if (hideToolBar) {
        ScreenshotFunctions.setToolBarShow(false);
      } else {
        ScreenshotFunctions.setToolBarShow(toolbarShowMode === 'show');
      }
      window.leaferHistoryList.addHistory();
    } else if (window.environment === 'capture') {
      capture(app, image);
      setCaptureCanvasSize(app, image);
    }
    currentToolBoxStore.setCurrentToolBoxByCode(window.environment  === 'screenshot' ? globalDefaultTool : 'screenshotDrag');
    // loadSceneConfig();
    console.log('主图加载完成');
    LeaferElement.getScreenshotGroup(app).set({
      x: 0,
      y: 0
    })
    new MainImageLoadedEvent().dispatchEvent();
  });
  const screenshotGroup = new Group({
    id: LeaferConstant.ElementScreenShotGroup,
    x: 0,
    y: 0,
  });
  screenshotGroup.add(image);
  app.tree.addAt(screenshotGroup, 0);
}

function loadJsonScreenshot(app: App, fileId: string) {
  const fileDirPath = window.path.join(DataUtils.getDataScreenshotLibrary(), fileId);
  const groundJson = window.fs.readFileSync(window.path.join(fileDirPath, 'ground.json'), 'utf-8');
  app.ground.set(JSON.parse(groundJson));
  const treeJson = window.fs.readFileSync(window.path.join(fileDirPath, 'edit.json'), 'utf-8');
  app.tree.set(JSON.parse(treeJson));
  const nativeImage = window.nativeImage.createFromPath(window.path.join(fileDirPath, 'edit.png'));
  console.log('nativeImage', nativeImage.getSize());
  const size = nativeImage.getSize();
  setTimeout(() => {
    LeaferHelper.setCanvasSize(app, {
      width: size.width / window.devicePixelRatio,
      height: size.height / window.devicePixelRatio
    });
    window.leaferHistoryList.addHistory();
    const {generalSetting: { globalDefaultTool, toolbarShowMode }} = useSettingUserStore();
    ScreenshotFunctions.setToolBarShow(toolbarShowMode === 'show');
    const { setCurrentToolBoxByCode } = useCurrentToolBoxStore();
    setCurrentToolBoxByCode(globalDefaultTool);
    // loadSceneConfig();
    new MainImageLoadedEvent().dispatchEvent();
    new ElementLoadEvent().dispatchEvent();
  }, 300);
}

/**
 * 注册主图截图 API
 * @param app
 */
function captureScreenshotApi(app: App) {
  window.api.captureScreenshot = () => {
    utools.screenCapture((base64) => {
      if (!base64) {
        window.winHelper.getCurrentWindow().close();
        return;
      }
      loadScreenshot(app, base64);
    })
  }
}

/**
 * 更新场景 API
 */
function reloadSceneApi() {
  window.api.reloadScene = (sceneCode: string) => {
    window.sceneCode = sceneCode;
    window.loadToolList();
  }
}

function setGlobalParamsApi() {
  window.api.setGlobalParams = (key: string, value: string) => {
    window.globalParams[key] = value;
  }
}

function sendBase64Screenshot(app: App) {
  window.api.sendBase64Screenshot = (base64: string,
                                     options?: {hideToolBar?: boolean,
                                       autoSize?: boolean,
                                       screenshotLocation?: {x: number, y: number}}) => {
    const {
      hideToolBar = false,
      screenshotLocation = undefined
    }  = options || {};
    if (screenshotLocation) {
      window.tempData.screenshotLocation = options.screenshotLocation;
    }else {
      delete window.tempData.screenshotLocation
    }
    if (options && options.autoSize) {
      loadScreenshot(app, base64, hideToolBar, options.autoSize);
    } else {
      loadScreenshot(app, base64, hideToolBar);
    }

  }
}

function sendScreenshotFileId(app: App) {
  window.api.sendScreenshotFileId = (fileId: string) => {
    loadJsonScreenshot(app, fileId)
  }
}


function sendUrlScreenshot(app: App) {
  window.api.sendUrlScreenshot = (url: string) => {
    loadScreenshot(app, url);
  }
}

/**
 * 注册 API
 * @param app
 */
function registerApi(app: App) {
  setGlobalParamsApi();
  reloadSceneApi();
  sendScreenshotFileId(app);
  captureScreenshotApi(app);
  sendBase64Screenshot(app);
  sendUrlScreenshot(app);
}
export default {
  loadBackground,
  loadEditorSelectImposeAction,
  loadMosaic,
  installAllGroup,
  loadMiddleGroup,
  loadDrawGroup,
  installZoom,
  installDeleteElement,
  installPastePicture,
  registerApi,
  installCopyPicture
}
