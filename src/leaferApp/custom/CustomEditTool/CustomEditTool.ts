import { Box } from 'leafer-ui'
import { EditTool, registerEditTool } from '@leafer-in/editor'
import type { IE<PERSON>, ILeafer, IPointData, IUI } from '@leafer-ui/interface'
import type { IBoundsData } from '@leafer/interface'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'

interface IEditorEvent extends IEvent {
  readonly target?: IUI;
  readonly value?: IUI | IUI[];
  readonly oldValue?: IUI | IUI[];
  readonly list?: IUI[];
  readonly oldList?: IUI[];
  readonly worldOrigin?: IPointData;
  readonly origin?: IPointData;
}
interface IEditorMoveEvent  extends IEditorEvent{
  readonly moveX: number;
  readonly moveY: number;
}
interface IEditorScaleEvent extends IEditorEvent {
  readonly scaleX?: number;
  readonly scaleY?: number;
  readonly direction?: number;
  readonly lockRatio?: boolean | 'corner';
}

export function adjustToolBarPosition(bounds: IBoundsData, app: ILeafer) {
  const toolBarElementContainer = document.getElementById(HtmlDomConstants.ToolboxContainerWrapper);
  const toolBarElement = document.getElementById('toolboxContainer');
  if (!toolBarElement || !toolBarElementContainer) {
    return;
  }
  let top = bounds.height + bounds.y + 10;
  let left = bounds.x;
  const toolBarBound = toolBarElement.getBoundingClientRect();
  const { clientWidth, clientHeight} = document.body;
  if (top + toolBarBound.height > clientHeight) {
    top = bounds.y - toolBarBound.height - 14;
  }

  if (toolBarBound.width + left > clientWidth) {
    left = clientWidth - toolBarBound.width;
  }
  // if (left < 0 || left + toolBarBound.width > clientWidth) {
  //   left = app.width! / 2 - toolBarBound.width / 2;
  // }

  if (top < 0) {
    top = bounds.height + bounds.y - toolBarBound.height - 10;
  }

  toolBarElementContainer.style.top = `${top}px`;
  console.log('toolBarBound.width + left > app.width!', left + toolBarBound.width, toolBarBound.width, document.body.clientWidth)
  toolBarElementContainer.style.left = `${left}px`;
}
@registerEditTool()
export class CustomEditTool extends EditTool {

  public get tag() { return 'CustomEditTool' }

  public point: Box | null = null;

  public toolBarElement: HTMLElement | null = null;
  onCreate() {
    super.onCreate();
  }

  onMove(e: IEditorMoveEvent) {
    super.onMove(e);
    if (e.target) {
      const app = this.editor.leafer!;
      adjustToolBarPosition(e.target.getBounds(), app);
    }
  }

  onScale(e: IEditorScaleEvent) {
    super.onScale(e);
    if (e.target) {
      const app = this.editor.leafer!;
      adjustToolBarPosition(e.target.getBounds(), app);
    }
  }
}
