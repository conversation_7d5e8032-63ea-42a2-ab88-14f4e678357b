import { dataProcessor, dataType, Rect, RectData, registerUI } from 'leafer-ui'
import type { ILeaferCanvas, IRectData, IRectInputData, IRenderOptions } from '@leafer-ui/interface'

interface IMosaicInputData extends IRectInputData {
  mosaicSize: number;
}

interface IMosaicData extends IRectData {

}
class MosaicData extends RectData implements IMosaicData {

}


@registerUI()
class Mosaic extends Rect {
  public get __tag() { return 'Mosaic'}

  @dataProcessor(MosaicData)
  declare public __: IMosaicData;

  @dataType(10)
  declare public mosaicSize: number // 增加自定义属性， 注意必须加上 declare 关键词

  constructor(data: IMosaicInputData) {
    super(data)
  }

  __drawRenderPath(canvas: ILeaferCanvas) {
    const { context } = canvas;
    const {width = 0, height = 0, x = 0, y = 0} = this.__;
    super.__drawRenderPath(canvas)
    context.fillStyle = 'red';

    context.fillRect(0, 0, width, height)
  }

  __draw(canvas: ILeaferCanvas, _options: IRenderOptions) {
    const { context } = canvas;
    const {x, y, width, height} = this.worldRenderBounds;

    const dx = x * this.app.pixelRatio!;
    const dy = y * this.app.pixelRatio!;
    const dw = width * this.app.pixelRatio!;
    const dh = height * this.app.pixelRatio!;
    if (dw < 1 || dh < 1) {
      return;
    }

    const imgData = context.getImageData(Math.max(1, dx), Math.max(1, dy), dw, dh);
    // 获取图像宽高
    const w = imgData.width;
    const h = imgData.height;
    const degreeOfBlur = this.mosaicSize;
    // 等分图像宽高
    const stepW = w / degreeOfBlur;
    const stepH = h / degreeOfBlur;

    let beforeColor = null;
    for (let i = 0; i < stepH; i++) {
      for (let j = 0; j < stepW; j++) {
        // 随机获取一个小方格的随机颜色
        const x = j * degreeOfBlur + Math.ceil(0.1 * degreeOfBlur);
        const y = i * degreeOfBlur + Math.ceil(0.1 * degreeOfBlur);
        let color = this.getAxisColor(
          imgData,
          x,
          y,
        );
        // 当取不到样式时候直接跳过, 解决会填充底色的问题
        if (!color[0] && !beforeColor) {
          continue;
        } else if (color[0]) {
          beforeColor = color;
        } else if (!color[0]) {
          color = beforeColor;
        }
        // 循环小方格的像素点
        for (let k = 0; k < degreeOfBlur; k++) {
          for (let l = 0; l < degreeOfBlur; l++) {
            // 设置小方格的颜色
            this.setAxisColor(
              imgData,
              j * degreeOfBlur + l,
              i * degreeOfBlur + k,
              color
            );
          }
        }
      }
    }

    // 渲染打上马赛克后的图像信息
    context.putImageData(imgData, dx, dy);
  }


  getAxisColor(imgData: ImageData, x: number, y: number) {
    const w = imgData.width;
    const d = imgData.data;
    const color = [];
    const index = 4 * (y * w + x);
    color[0] = d[index];      // R
    color[1] = d[index + 1];  // G
    color[2] = d[index + 2];  // B
    color[3] = d[index + 3];  // A
    return color;
  }
  setAxisColor(imgData: ImageData,
               x: number,
               y: number,
               color: Array<number>) {
    const w = imgData.width;
    const d = imgData.data;
    d[4 * (y * w + x)] = color[0];
    d[4 * (y * w + x) + 1] = color[1];
    d[4 * (y * w + x) + 2] = color[2];
    d[4 * (y * w + x) + 3] = color[3];
  }


}
export default Mosaic;
