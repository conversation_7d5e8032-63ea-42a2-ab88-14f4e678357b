import {
  affectStrokeBoundsType,
  dataProcessor,
  Line,
  LineData,
  PathBounds,
  PathCommandDataHelper,
  registerUI
} from 'leafer-ui'
import type { ILineData, ILineInputData, IPointData, IStrokeAlign } from '@leafer-ui/interface'


// 定义数据

interface ISupperArrowInputData extends ILineInputData { }
interface ISupperArrowData extends ILineData {

}

class SupperArrowData extends LineData implements ISupperArrowData {
}


// 定义类

const { moveTo, lineTo, closePath } = PathCommandDataHelper

@registerUI()
export class SupperArrowLine extends Line {


  public get __tag() { return ' SupperArrow' }

  @dataProcessor(SupperArrowData)
  declare public __: ISupperArrowData

  // 画非闭合的线条，需要修改 strokeAlign 默认值为 ‘center’（UI默认是内描边）
  @affectStrokeBoundsType('center')
  declare public strokeAlign: IStrokeAlign

  constructor(data: ISupperArrowInputData) {
    super(data)
  }

  // 1. 绘制自定义路径
  public __updatePath() {
    const { strokeWidth = 20 , x = 0, y = 0 } = this.__
    const path: number[] = this.__.path = [] // 相当于 beginPath


    const toPoint = this.toPoint || {x: 0, y: 0};
    const from: IPointData = {x, y};
    const to: IPointData = {x: toPoint.x + from.x, y: toPoint.y + from.y};
    this.worldToInner(from, from);
    this.worldToInner(to, to);

    this.drawArrow(path, from.x, from.y,to.x ,to.y, strokeWidth);

    // console.log("__updatePath", strokeWidth, x, y, toPoint, this.__)
  }

  // 2. (可选）如果通过width、height属性无法确定图形 bounds，需要override此函数，从路径中获取 bounds
  public __updateBoxBounds(): void {
    PathBounds.toBounds(this.__.path!, this.__layout.boxBounds)
  }

  public drawArrow(path: number[], x0: number, y0: number, x1: number, y1: number, width: number) {
    if (width === void 0) { width = 3; }
    if (width < 3)
      width = 3; //最小宽度
    /**
     * 极坐标[以(x0,y0)为原点] 转换 画布坐标
     * @param x0
     * @param y0
     * @param r
     * @param radian
     */
    const polarCoordinate2canvasCoordinate = function (x0: number, y0: number, r: number, radian: number) {
      //转与之对应的直角坐标
      let x = r * Math.cos(radian);
      let y = r * Math.sin(radian);
      //直角坐标再转画布坐标
      x += x0;
      y += y0;
      return { x: x, y: y };
    };
    //起点坐标与终点坐标的距离
    const distance = Math.sqrt((y1 - y0) * (y1 - y0) + (x1 - x0) * (x1 - x0));
    //起点坐标与终点坐标连线倾斜的弧度，此时的弧度是第一象限的弧度
    let radian = Math.asin(Math.abs(y1 - y0) / distance); //第一象限的弧度
    if (x0 > x1 && y1 > y0) { //第二象限
      radian = Math.PI - radian;
    }
    else if (x0 > x1 && y0 > y1) { //第三象限
      radian += Math.PI;
    }
    else if (x1 > x0 && y0 > y1) { //第四象限
      radian = 2 * Math.PI - radian;
    }
    //在起点坐标与终点坐标的线段上，找出距离终点坐标距离为distance - width * 2的点坐标，再以此点依次计算出箭头的各个关键点的坐标
    const _a = polarCoordinate2canvasCoordinate(x0, y0, distance - width * 3.5, radian), x = _a.x, y = _a.y;
    const p1 = polarCoordinate2canvasCoordinate(x, y, width, radian - Math.PI * 0.35);
    const p2 = polarCoordinate2canvasCoordinate(x, y, width * 2.5, radian - Math.PI * 0.58);
    const p3 = polarCoordinate2canvasCoordinate(x, y, width, radian + Math.PI * 0.35);
    const p4 = polarCoordinate2canvasCoordinate(x, y, width * 2.5, radian + Math.PI * 0.58);
    //找到里箭头的6个关键点，依次连线
    moveTo(path, x0, y0);
    lineTo(path, p1.x, p1.y);
    lineTo(path, p2.x, p2.y);
    lineTo(path, x1, y1);
    lineTo(path, p4.x, p4.y);
    lineTo(path, p3.x, p3.y);
    closePath(path);
  }
}

SupperArrowLine.setEditConfig({
  resizeCursor: {
    url: `
      <svg viewBox="0 0 1024 1024" width="24" height="24" xmlns="http://www.w3.org/2000/svg">
        <g transform="rotate({{}},12,12)">
          <path d="M560.341333 421.802667v-298.666667h-85.333333v298.666667h-298.666667v85.333333h298.666667v298.666667h85.333333v-298.666667h298.666667v-85.333333z" fill="#363B3E" p-id="8848"></path>
        </g>
      </svg>
    `,
    x: 12,
    y: 12
  }
});
