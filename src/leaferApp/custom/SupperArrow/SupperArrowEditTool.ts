import { EditTool, registerEditTool } from '@leafer-in/editor'
import { Direction9, PointHelper } from 'leafer-ui'
import { SupperArrowLine } from './SupperArrow'
import type { IAround, IDragEvent, IFromToData, ILine, IPathCommandData, IUI } from '@leafer-ui/interface'
import type { IPointData } from '@leafer/interface'

const { move, copy } = PointHelper

const { left, right } = Direction9;

@registerEditTool()
export class SupperArrowEditTool extends EditTool {

  public get tag() { return 'SupperArrowEditTool' }


  onScaleWithDrag(e: IEditorScaleEvent): void {
    const { drag, direction, lockRatio, around } = e
    const line = e.target as SupperArrowLine;
    const isDragFrom = direction === left

    const { path } = line.__
    const { from, to } = this.getFromToByPath(path)
    const movePoint = drag.getInnerMove(line)
    const innerMove = this.getInnerMove(line, drag, lockRatio);
    // console.log('movePoint', movePoint);
    line.pen.clearPath();
    if (isDragFrom) {
      // 拖拽箭头开始
      line.drawArrow(line.path, from.x + movePoint.x, from.y + movePoint.y, to.x, to.y, line.strokeWidth);
    } else {
      // 拖拽箭头结束
      line.drawArrow(line.path, from.x, from.y, to.x + movePoint.x, to.y + movePoint.y, line.strokeWidth);
    }
    line.path = path

  }

  getInnerMove(ui: IUI, event: IDragEvent, lockRatio: boolean | 'corner'): IPointData {
    const movePoint = event.getInnerMove(ui)
    if (lockRatio) {
      if (Math.abs(movePoint.x) > Math.abs(movePoint.y)) {
        movePoint.y = 0
      } else {
        movePoint.x = 0
      }
    }
    return movePoint
  }
  //
  getFromToByPath(path: IPathCommandData): IFromToData {
    return {
      from: { x: path[1], y: path[2] },
      to: { x: path[10], y: path[11] }
    }
  }
  dragPoint(fromPoint: IPointData, toPoint: IPointData, isDragFrom: boolean, around: IAround, movePoint: IPointData): void {
    const { x, y } = movePoint
    if (isDragFrom) {
      move(fromPoint, x, y)
      if (around) move(toPoint, -x, -y)
    } else {
      if (around) move(fromPoint, -x, -y)
      move(toPoint, x, y)
    }
  }

  onSkew(_e: IEditorSkewEvent): void {

  }

  onUpdate() {
    const { editBox } = this,
      { rotatePoints, resizeLines, resizePoints, rect } = editBox
    const line = this.editor.element as ILine
    let leftOrRight: boolean;
    const fromTo = this.getFromToByPath(line.__.path);
    console.log(line.path)
    // rect.visible = false;
    rect.pen.clearPath();
    if (fromTo) {
      const { from, to } = fromTo
      line.innerToWorld(from, from, false, editBox);
      line.innerToWorld(to, to, false, editBox);
      // rect.pen.clearPath().moveTo(from.x, from.y).lineTo(to.x, to.y)
      copy(resizePoints[7] as IPointData, from)
      copy(rotatePoints[7] as IPointData, from)
      copy(resizePoints[3] as IPointData, to)
      copy(rotatePoints[3] as IPointData, to)
    }

    for (let i = 0; i < 8; i++) {
      if (i < 4) resizeLines[i].visible = false
      leftOrRight = i === left || i === right
      resizePoints[i].visible = leftOrRight
      rotatePoints[i].visible = fromTo ? false : leftOrRight
    }
  }
}

SupperArrowLine.setEditOuter('SupperArrowEditTool');
