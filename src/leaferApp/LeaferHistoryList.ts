import type { IUIJSONData } from '@leafer-ui/interface'
import { type App, DragEvent } from 'leafer-ui'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import { Editor } from '@leafer-in/editor'
import { ElementLoadEvent } from '@/events/ElementLoadEvent'

export default class LeaferHistoryList {
  private __historyList: IUIJSONData[] = [];
  private __app: App;


  constructor(app: App) {
    this.__app = app;
    this.installRecordHistoryCapacity();
  }


  public getHistoryJson() {
    return this.__app.tree.toJSON();
  }

  public addHistory() {
    this.__historyList.push(this.getHistoryJson());
  }

  public addHistoryByJson(data: IUIJSONData) {
    this.__historyList.push(data);
  }

  /**
   * 回滚
   */
  public rollBack(): IUIJSONData | null {
    console.log('rollBack', this.__historyList);
    if (this.__historyList.length === 1) {
      return null;
    }
    return this.__historyList.pop();
  }

  /**
   * 安装记录历史的能力
   * @private
   */
  private installRecordHistoryCapacity() {
    // 截图画板日志
    this.__app.tree.on(DragEvent.START, (e: DragEvent) => {
      console.log(!(e.target instanceof Array) && e.target.id === LeaferConstant.ElementChooseAreaRect)
      if (!(e.target instanceof Array) && e.target.id === LeaferConstant.ElementChooseAreaRect) {
        return;
      }
      this.addHistory();
    });
  }

  public recall() {
    this.__app.editor.cancel();
    const jsonData = this.rollBack();
    if (jsonData) {
      this.__app.tree.set(jsonData);
      this.__app.tree.updateLayout();
      new ElementLoadEvent()
        .dispatchEvent();
    } else {
      ScreenshotCapacity.showTipsNotification("info", {
        content: '没有更多的历史记录了'
      });
    }
    if (window.environment === 'capture') {
      const editor = this.__app.sky.children[0] as Editor;
      editor.hittable = false;
      this.__app.sky.addAt(editor, 0)
      editor.target = this.__app.tree.findId(LeaferConstant.ElementChooseAreaRect);
    }
  }
}
