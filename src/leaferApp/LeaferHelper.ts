import type { IGradientPaint, IPaint, IPointData, IUI } from '@leafer-ui/interface'
import { isArray } from '@/utils/jsUtil'
import { AlignHelper, type App, Point } from 'leafer-ui'
import type { ILeaferDataBase } from '@/leaferApp/LeaferData'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import leaferConstant from '@/leaferApp/LeaferConstant'
import type { ISizeData } from '@leafer/interface'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
import { ToolElementEvent } from '@/events/ToolElementEvent'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { toRefs } from 'vue'


export default class LeaferHelper {

  static __maxToolConfigHeight = 260;


  /**
   * 获取缩放后元素的大小
   * @param ui
   */
  static getScaleElementSize(ui: IUI): ISizeData {
    const bounds = ui.getBounds();
    return  {
      width: bounds.width * ui.scaleX,
      height: bounds.height * ui.scaleY
    };
  }
  /**
   * 向上查询父级元素
   * @param iui
   * @param findInfer
   */
  static findFather(iui: IUI, findInfer: (iui: IUI) => boolean): IUI {
    const res = findInfer(iui);
    if (res) {
      return iui;
    }
    if (!iui.parent) {
      return;
    }
    return LeaferHelper.findFather(iui.parent, findInfer);
  }

  /**
   * 从数组中查询出满足第一个元素
   * @param iuiList 元素列表
   * @param find 查询方法
   */
  static findOneInArray(iuiList: IUI[], find: (iui: IUI) => IUI[] | IUI | undefined): IUI[] | IUI {
    for (const iui of iuiList) {
      const res = find(iui);
      if (isArray(res) && res.length > 0) {
        return res;
      }  else if (res && !isArray(res)) {
        return res;
      }
    }
  }

  /**
   * 删除元素, 支持删除工具组
   * @param app Leafer APP
   * @param iui 元素
   */
  static removeElement (app: App, iui: IUI) {
    const data = iui.get('data') as ILeaferDataBase;
    if (!data || !data.code) {
      iui.remove();
      // 触发删除后事件
      dispatchEvent(new ToolElementEvent(ToolElementEvent.ELEMENT_DELETE, iui) as any);
      return;
    }
    if (app.findOne(iui.innerId)) {
      const element = LeaferHelper.findFather(iui, (iui: IUI) => iui.className === data.code);
      if (element) {
        element.remove();
        // 触发删除后事件
        dispatchEvent(new ToolElementEvent(ToolElementEvent.ELEMENT_DELETE, element) as any);
      }
    }
  }

  /**
   * 设置元素数据
   * @param iui 元素
   * @param data 数据
   * @param cover 是否覆盖
   */
  static setElementData<T = Record<string, any>>(iui: IUI, data: Partial<T>, cover = false) {
    iui.set({
      data: {
        ...cover ? {} : iui.getAttr('data'),
        ...JSON.parse(JSON.stringify(data))
      }
    });
  }
  static isSolidPaint(value: IPaint) {
    return value.type === 'solid';
  }

  static isGradientPaint(value: IPaint): value is IGradientPaint {
    return value.type === 'linear' || value.type === 'radial' || value.type === 'angular';
  }

  /**
   * 将 IUI | IUI[] 格式划为 数组
   * @param iui
   */
  static formatIUIArray(iui?: IUI | IUI[]) {
    if (!iui) {
      return [];
    }
    if (isArray(iui)) {
      return iui;
    }
    return [iui];
  }

  static getSelectTargets(app: App): IUI[] {
    return LeaferHelper.formatIUIArray(app.editor.target);
  }

  static exitEditor(app: App) {
    app.editor.cancel();
  }

  static pageAlign(app: App, align: string, container: IUI) {
    const contentBounds = {...container.getBounds('box'), x: 0, y: 0};
    const containerBounds = {width: app.width, height: app.height, x: 0, y: 0};
    const point = new Point();
    AlignHelper.toPoint(align as any, contentBounds, containerBounds, point, false);
    container.set(app.tree.getPagePoint(point));
  }

  /**
   * 设置画板大小
   * @param app
   * @param width
   * @param height
   * @param autoWin
   * @param autoSize
   */
  static setCanvasSize(app: App,
                       {width, height}: {width: number, height: number},
                       autoWin = true,
                       autoSize = false) {
    console.log('setCanvasSize', width, height, autoWin, autoSize);
    if (window.environment === 'capture') {
      return;
    }
    const view = app.view as HTMLDivElement;
    view.style.width = Math.floor(width) + 'px';
    view.style.height = Math.floor(height) + 'px';
    console.log('window.devicePixelRatio', app.tree.pixelRatio, app.pixelRatio, app);
    const background = app.findId(LeaferConstant.ElementBackground);
    if (background) {
      const scale = app.ground.zoomLayer.scale as number;
      background.width = Math.floor(width / scale);
      background.height = Math.floor(height / scale);
    }
    app.tree.resize({ width: Math.floor(width), height: Math.floor(height), pixelRatio: window.devicePixelRatio });
    app.sky.resize({ width: Math.floor(width), height: Math.floor(height), pixelRatio: window.devicePixelRatio });
    app.ground.resize({ width: Math.floor(width), height: Math.floor(height), pixelRatio: window.devicePixelRatio });
    app.resize({ width: Math.floor(width), height: Math.floor(height), pixelRatio: window.devicePixelRatio });
    const contentWrapper = document.getElementById('contentWrapper')
    // debugger
    if (contentWrapper) {
      // const currentWindow = window.winHelper.getCurrentWindow();
      // const display = utools.getDisplayNearestPoint(currentWindow.getBounds());
      // const bounds = display.bounds;
      // const maxHeight = bounds.height;
      // const maxWidth = bounds.width;
      // contentWrapper.style.maxWidth = `${maxWidth}px`
      // contentWrapper.style.maxHeight = `${maxHeight - LeaferHelper.__maxToolConfigHeight}px`;
      if (window.environment === 'screenshot') {
        const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
        contentWrapper.style.width = `${screenshotContainer.clientWidth}px`;
        contentWrapper.style.height = `${screenshotContainer.clientHeight}px`;
      }
    }
    if (window.utools && autoWin) {
      LeaferHelper.autoAdjustWindowSize(app, true, autoSize);
    }
  }

  /**
   * 自动计算和调整窗口大小
   * @param app
   * @param includeToolConfig
   * @param autoSize
   */
  static autoAdjustWindowSize(app: App,
                              includeToolConfig = true,
                              autoSize = false) {
    // debugger
    if (window.environment === 'capture') {
      return;
    }
    const toolboxContainerWrapper = document.getElementById('toolboxContainerWrapper');
    const toolboxContainer = document.getElementById('toolboxContainer');
    const contentWrapper = document.getElementById('contentWrapper');
    console.log('screenshotLocation', toolboxContainerWrapper, toolboxContainer, contentWrapper);
    if (!toolboxContainer || !contentWrapper || !toolboxContainerWrapper) {
      return;
    }


    const toolboxContainerRect = toolboxContainer.getBoundingClientRect();
    const toolboxContainerWrapperRect = toolboxContainerWrapper.getBoundingClientRect();
    let width = Math.max(toolboxContainer.clientWidth, app.width!) + 25;
    let height = toolboxContainerWrapperRect.top + toolboxContainerRect.height + (includeToolConfig ?  this.__maxToolConfigHeight : 10);

    if (toolboxContainer.style.opacity === '0') {
      const backgroundUI = app.ground.findId(LeaferConstant.ElementBackground)!;
      const bounds = backgroundUI.getBounds();
      // 工具隐藏工具
      width = bounds.width + 18;
      height = bounds.height + 18;
    }

    const currentWindow = window.winHelper.getCurrentWindow();
    const screenPoint = utools.getCursorScreenPoint();
    const display = utools.getDisplayNearestPoint(screenPoint);
    const screenBounds = display.bounds;
    const maxWidth = Number.MAX_VALUE;
    const maxHeight = Number.MAX_VALUE;
    const winWidth = Math.ceil(Math.min(maxWidth, Math.ceil(width)));
    const winHeight = Math.ceil(Math.min(maxHeight, Math.ceil(height)));

    const mainImage = app.tree.findId(LeaferConstant.ElementScreenShot)!;
    const mainImageBounds = mainImage.getBounds();
    console.log('screenshotLocation', window.tempData.screenshotLocation)
    if (!currentWindow.isVisible()) {
      console.log('autoAdjustWindowSize', width, height, autoSize);
      let x;
      let y;
      console.log('screenshotLocation', window.tempData.screenshotLocation)
      if (window.tempData
        && window.tempData.screenshotLocation) {
        // const cursorScreenPoint = utools.getCursorScreenPoint();
        const screenshotLocation = window.tempData.screenshotLocation;
        // console.log('location', screenshotLocation, cursorScreenPoint)
        // let symbol = 1
        /*if (cursorScreenPoint.x < screenshotLocation.x && cursorScreenPoint.y  < screenshotLocation.y) {
          symbol = -1;
        }*/
        x = screenshotLocation.x - (mainImageBounds.width + contentWrapper.clientLeft);
        y = screenshotLocation.y - (mainImageBounds.height + contentWrapper.clientTop);
      } else {
        x = screenBounds.x + (screenBounds.width - mainImageBounds.width) / 2;
        y = screenBounds.y + (screenBounds.height - mainImageBounds.height) / 2;
      }

      console.log('autoAdjustWindowSize-show', x, y, mainImageBounds.width, mainImageBounds.height);
      window.winHelper.getCurrentWindow().setSize(winWidth, winHeight);
      window.winHelper.getCurrentWindow().setPosition(Math.round(x), Math.round(y), false);

      if (autoSize) {
        console.log('autoSize', autoSize)
        const display = utools.getDisplayNearestPoint(window.winHelper.getCurrentWindow().getBounds());
        if (display.bounds.width < mainImageBounds.width || display.bounds.height < mainImageBounds.height) {
          // 图片超过屏幕
          // 计算屏幕的80%大小
          const maxWidth = display.bounds.width * 0.85;
          const maxHeight = display.bounds.height * 0.85;

          // 计算缩放比例
          const widthRatio = maxWidth / mainImageBounds.width;
          const heightRatio = maxHeight / mainImageBounds.height;

          // 使用较小的缩放比例，保持图片比例
          const scale = Math.min(widthRatio, heightRatio);

          app.interaction.zoom({ x: 100, y: 100, scale });
          const newMainImage = app.tree.findId(LeaferConstant.ElementScreenShot)!;
          const newMainImageBounds = newMainImage.getBounds();
          x = screenBounds.x + (screenBounds.width - newMainImageBounds.width) / 2;
          y = screenBounds.y + (screenBounds.height - newMainImageBounds.height) / 2;
          window.winHelper.getCurrentWindow().setPosition(Math.round(x), Math.round(y), false);
        }
      }
      window.winHelper.getCurrentWindow().show();
      console.log(screenBounds.height, mainImageBounds.height, screenBounds.height < mainImageBounds.height + 250)
      const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
      if (screenshotContainer) {
        setTimeout(() => {
          screenshotContainer.style.opacity = '1';
        });
      }
    } else {
      window.winHelper.getCurrentWindow().setSize(winWidth, winHeight);
    }

    this.autoToolBarPosition(app, includeToolConfig);
  }

  private static autoToolBarPosition(app: App, includeToolConfig: boolean) {
    if (window.environment === 'capture') {
      // xsc 指令不需要这个功能
      return;
    }
    const toolboxContainer = document.getElementById('toolboxContainer');
    const toolboxContainerRect = toolboxContainer.getBoundingClientRect();

    const display = window.winHelper.getDisplayByWinPosition();
    const screenBounds = display.bounds;
    const mainImage = app.tree.findId(LeaferConstant.ElementScreenShot)!;
    const mainImageBounds = mainImage.getBounds();
    const devicePixelRatio = display.scaleFactor;
    const imageWidth = mainImageBounds.width * mainImage.scaleX * devicePixelRatio;
    const imageHeight = mainImageBounds.height * mainImage.scaleY * devicePixelRatio;
    const { toolBarLocation } = toRefs(useCurrentToolBoxStore());

    let autoAdjustWindow = false;
    if (screenBounds.height < imageHeight + this.__maxToolConfigHeight - 10 && imageWidth > this.getToolBarSize(toolboxContainer)) {
      console.log('图上工具条', imageWidth, toolboxContainerRect.width);
      toolboxContainer.style.flexDirection = 'row';
      toolboxContainer.style.top = `${imageHeight - 130}px`;
      toolboxContainer.style.left = `${imageWidth / 2 - toolboxContainerRect.width / 2}px`;
      toolBarLocation.value = 'cover';
    } else if (screenBounds.height < imageHeight + this.__maxToolConfigHeight + 10) {
      // 需要调整到右侧
      console.log('右侧工具条')
      toolboxContainer.style.flexDirection = 'column';
      toolboxContainer.style.top = `${imageHeight / 2 - this.getToolBarSize(toolboxContainer) / 2}px`;
      toolboxContainer.style.left = `${imageWidth - 36}px`;
      toolBarLocation.value = 'right';
    } else {
      console.log('底部工具条')
      autoAdjustWindow = toolboxContainer.style.flexDirection === 'column';
      toolboxContainer.style.flexDirection = 'row';
      toolboxContainer.style.top = '';
      toolboxContainer.style.left = '';
      toolBarLocation.value = 'default';
    }

    if (autoAdjustWindow) {
     setTimeout(() => {
       this.autoAdjustWindowSize(app, includeToolConfig);
     }, 50);
    }
  }



  private static getToolBarSize(toolboxContainer: HTMLElement) {
    if (toolboxContainer.style.flexDirection === 'column') {
      return toolboxContainer.clientHeight;
    }
    return toolboxContainer.clientWidth;
  }

  static getCaptureRectBounds(app: App) {
    const chooseAreaRect = app.tree.findId(leaferConstant.ElementChooseAreaRect)!;
    const chooseAreaRectBounds = chooseAreaRect.getBounds();
    const dx = chooseAreaRectBounds.x + 4;
    const dy = chooseAreaRectBounds.y + 4;
    const dw = chooseAreaRectBounds.width - 6;
    const dh = chooseAreaRectBounds.height - 6;
    return {
      left: dx,
      top: dy,
      width: dw,
      height: dh,
    }
  }

  /**
   * 计算两个点距离
   * @param startPoint 开始点
   * @param endPoint 结束点
   */
  static calculateDistance(startPoint: IPointData, endPoint: IPointData) {
    const deltaX = endPoint.x - startPoint.x;
    const deltaY = endPoint.y - startPoint.y;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  /**
   * 清理编辑器编辑操作限制
   */
  static cleanAppEditorImposeAction(app: App) {
    app.editor.config.resizeable = true;
    app.editor.config.rotateable = true;
    app.editor.config.moveable = true;
    app.editor.config.skewable = true;
    app.editor.config.flipable = true;
  }
}
