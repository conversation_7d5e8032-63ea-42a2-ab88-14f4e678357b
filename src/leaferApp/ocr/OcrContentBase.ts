import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
import { App } from 'leafer-ui'
import LeaferHelper from '@/leaferApp/LeaferHelper'

export interface IOcrPlatFromConfigItem {
  key: number,
  name: string;
  /**
   * 平台 code
   * @see IOcrPlatFormInfo.code
   */
  platform: string;
  /**
   * image: 在图片上 </br>
   * window: 窗口上
   */
  type: 'image' | 'window';
  /**
   * 当前配置缩放激活
   */
  active?: boolean;
}

export interface IOcrPlatFormInfo {
  /**
   * 平台唯一码
   */
  code: string;
  /**
   * 平台名称
   */
  name: string;
  type: 'cloud' | 'local';
  /**
   * 是否隐藏
   */
  hide?: boolean;
}

export interface IOcrPlatFormItem {
  info: IOcrPlatFormInfo,
  ocrInstance: OcrContentBase;
}

export default abstract class OcrContentBase {

  protected ocrResultText: string = '';

  private __screenshotContainerRect?: {
    height: number;
    width: number;
    left:  number;
    top: number;
  };

  /**
   * OCR 平台信息
   * @private
   */
  private readonly __ocrPlatFormInfo: IOcrPlatFormInfo;
  private __app?: App

  protected constructor(ocrPlatFormInfo: IOcrPlatFormInfo) {
    this.__ocrPlatFormInfo = ocrPlatFormInfo;
  }


  public ocr(ocrPlatFromConfig?: IOcrPlatFromConfigItem) {
    if (this.isAlreadyOcr) {
      this.__show();
      return;
    }
    return this.doOcr(ocrPlatFromConfig);
  }


  public show(): boolean {
    console.log('isAlreadyOcr', this.isAlreadyOcr)
    if (this.isAlreadyOcr) {
      this.__show();
      return true;
    }
    return false;
  }


  public ocrResult(ocrPositionList: any[]) {
    if (this.show()) {
      return;
    }
    this.drawPictureInnerElement(ocrPositionList);
  }

  public abstract doOcr(ocrPlatFromConfig: IOcrPlatFromConfigItem): Promise<boolean>;

  /**
   * 返回图片位置
   * @protected
   */
  protected abstract loadPictureInnerOcr(ocrPlatFromConfig: IOcrPlatFromConfigItem): Promise<any[]>;

  /**
   * 向图片上添加文字容器
   * @param ocrPositionList
   * @param model
   * @protected
   */
  protected abstract drawPictureInnerElement(ocrPositionList: any[]): void;



  init(app: App) {
    if (window.environment === 'screenshot') {
      const screenshotContainer = document.getElementById(HtmlDomConstants.ScreenshotContainer);
      this.__screenshotContainerRect = screenshotContainer.getBoundingClientRect();
    } else {
      this.__screenshotContainerRect = LeaferHelper.getCaptureRectBounds(app);
    }

    this.__app = app;
    return this;
  }

  /**
   * 显示 OCR 内容
   */
  protected __show() {
    const ocrElement = this.getPictureInnerElement();
    ocrElement.style.opacity = '1';
    ocrElement.style.pointerEvents = 'auto';
    const resultInner = document.getElementById(HtmlDomConstants.ToolOcrResultInner);
    resultInner.innerHTML = this.ocrResultText;
  }

  /**
   * 隐藏 OCR 内容
   */
  public hide() {
    const ocrElement = this.getPictureInnerElement();
    ocrElement.style.opacity = '0';
    ocrElement.style.pointerEvents = 'none';
  }

  /**
   * 获取图片上的 OCR 容器
   */
  getPictureInnerElement() {
    return  document.getElementById('ocr');
  }

  /**
   * 初始化文字容器样式
   */
  getTextContainerDom(options: {left: number, top: number, content: string}) {
    const dom = document.createElement('div');
    dom.style.position = 'fixed';
    dom.style.fontFamily = 'Times New Roman';
    // dom.style.color = 'transparent';
    dom.style.fontSize = '12px';
    dom.style.zIndex = '999';
    dom.style.userSelect = 'text';
    dom.setAttribute("data-ocr-text", options.content);
    const pixelRatio = window.devicePixelRatio;

    dom.addEventListener('copy', (e) => {
      const clipboardData = e.clipboardData;
      if(!clipboardData) {
        return;
      }
      let text = window.getSelection().toString();
      if (text) {
        e.preventDefault();
        text = text.replace(/✔/g, '\n')
          .replace(/\s/g, '');
        utools.copyText(text)
      }
    });
    const paddingLeft = this.__screenshotContainerRect.left;
    const paddingTop = this.__screenshotContainerRect.top;
    dom.style.left = `${Math.ceil((options.left  / pixelRatio + paddingLeft))}px`;
    dom.style.top = `${Math.ceil((options.top / pixelRatio + paddingTop) )}px`;
    dom.innerHTML = `${options.content}`;
    this.getPictureInnerElement().append(dom);
    return dom;
  }

  /**
   * 填充空格
   * @param fillWidth 填充宽度
   * @protected
   */
  protected getFillSpace(fillWidth: number) {
    const spaceCount = Math.ceil(fillWidth / 4)
    if (spaceCount < 0) {
      return '';
    }
    let str = '';
    for (let i = 0; i < spaceCount; i++) {
      str = str + '&nbsp;';
    }
    return str;
  }


  get ocrPlatFormInfo() {
    return this.__ocrPlatFormInfo;
  }

  /**
   * 获取平台唯一的 code
   */
  public getPlatFormCode() {
    return this.__ocrPlatFormInfo.code;
  }

  get app() {
    return this.__app!;
  }

  get isAlreadyOcr(): boolean {
    return this.getPictureInnerElement().hasChildNodes();
  }
}

export class OcrError extends Error {
  private readonly __platForm: string;
  private readonly __accountKey: number;
  private readonly __errorCode: number;
  constructor(errorCode: number, platForm: string, accountKey: number, msg: string) {
    super(msg);
    this.__platForm = platForm;
    this.__accountKey = accountKey;
    this.__errorCode = errorCode;
  }

  get platForm() {
    return this.__platForm;
  }

  get accountKey() {
    return this.__accountKey;
  }
  get errorCode() {
    return this.__errorCode;
  }
}


export function ocrHide() {
  const ocrElement= document.getElementById('ocr');
  ocrElement.style.opacity = '0';
  ocrElement.style.pointerEvents = 'none';
}
