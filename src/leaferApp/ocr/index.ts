import type { IOcrPlatFormItem } from '@/leaferApp/ocr/OcrContentBase'

const orcPlatForms: Record<string, IOcrPlatFormItem> = import.meta.glob('./impl/*OcrContent.ts', {
  eager: true,
  import: 'default',
});


export function getOcrPlatformInfoList() {
  return Object.keys(orcPlatForms)
    .filter(key => !orcPlatForms[key].info.hide)
    .map((key) => orcPlatForms[key].info);
}

export function getOcrPlatformInstanceByCode(code: string) {
  const orcPlatFormList = Object.keys(orcPlatForms)
    .map(key => orcPlatForms[key])
    .filter((item) =>item.info.code === code);
  if (orcPlatFormList.length) {
    return orcPlatFormList[0].ocrInstance;
  }
  return null;
}
