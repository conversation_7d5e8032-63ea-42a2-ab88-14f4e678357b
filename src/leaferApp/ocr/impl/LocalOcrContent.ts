import OcrContentBase, {
  type IOcrPlatFormInfo,
  type IOcrPlatFormItem,
  type IOcrPlatFromConfigItem
} from '@/leaferApp/ocr/OcrContentBase'
import DataUtils from '@/utils/DataUtils'
import ExeUtils from '@/core/utils/ExeUtils'
import { getInstalledLocalOcrModelList } from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'


export interface ILocalOcrContentConfig extends IOcrPlatFromConfigItem {
  saveFilePath: string;
}

const ocrPlatFormInfo: IOcrPlatFormInfo = {
  code: 'local',
  name: '本地 OCR',
  type: 'local',
  hide: true,
}

export class LocalOcrContent extends OcrContentBase {
  constructor() {
    super(ocrPlatFormInfo)
  }

  async doOcr(ocrPlatFromConfig: ILocalOcrContentConfig): Promise<boolean> {
    const ocrResult = await this.loadPictureInnerOcr(ocrPlatFromConfig);
    this.distributeDrawPictureInnerElement(ocrResult);
    return Promise.resolve(true);
  }

  async loadPictureInnerOcr(ocrPlatFromConfig: ILocalOcrContentConfig): Promise<any[]> {
    const activeModel = DataUtils.getDataOcrActiveModel();
    if (activeModel.includes('paddleOcr')) {
      return [];
    }
    const modelItem = getInstalledLocalOcrModelList()
      .find(item => item.installDir === activeModel);
    const modelPath = window.path.join(DataUtils.getDataOcrPath(),
      modelItem.installDir,
      modelItem.executeFile);
     const res = await ExeUtils.executeJson(`"${modelPath}" "${ocrPlatFromConfig.saveFilePath}"`);
     if (window.fs.existsSync(ocrPlatFromConfig.saveFilePath)) {
       window.fs.rmSync(ocrPlatFromConfig.saveFilePath);
     }
     return res;
  }

  protected distributeDrawPictureInnerElement(ocrResult: any[]): void {
    const activeModel = DataUtils.getDataOcrActiveModel();
    if (activeModel.includes('paddleOcr')) {
      this.drawPictureInnerElement(ocrResult);
    } else if (activeModel.includes('apple') || activeModel.includes('windows')) {
      this.generalOcrResult(ocrResult);
    }
  }

  generalOcrResult(ocrPositionList: any[]) {
    this.ocrResultText = ocrPositionList.map(item =>  item.words).join("<br/>");
    for (const ocrPosition of ocrPositionList) {
      const textContainer = this.getTextContainerDom( {
        ...ocrPosition.location,
        content: '',
      });
      textContainer.style.color = 'transparent'
      textContainer.className = 'hide'
      const lineHeight = ocrPosition.location.height;
      textContainer.style.lineHeight = `${lineHeight / devicePixelRatio}px`;
      const location = ocrPosition.location;
      for (const char of ocrPosition.chars) {
        const fillSpace = this.getFillMiddleSpace(location.left, textContainer.clientWidth, char.location.left);
        textContainer.innerHTML = textContainer.innerHTML + fillSpace + char.char;
      }

      textContainer.innerHTML = textContainer.innerHTML +  '✔'
      const fillEndSpace = this.getFillEndSpace(textContainer.clientWidth, location.width);
      textContainer.innerHTML = textContainer.innerHTML + fillEndSpace;
    }
    this.__show();
  }

  private getFillMiddleSpace(textContainerLeft: number, textContainerWidth: number, left: number): string {
    const distance  = (left / devicePixelRatio) - (textContainerLeft / devicePixelRatio) - textContainerWidth;
    return this.getFillSpace(distance);
  }

  private getFillEndSpace(textContainerWidth: number, width: number) {
    console.log(width / devicePixelRatio - textContainerWidth)
    return this.getFillSpace(width / devicePixelRatio - textContainerWidth);
  }


  protected drawPictureInnerElement(ocrPositionList: any[]) {
    this.ocrResultText = ocrPositionList.map(item =>  item.text).join("<br/>");
    // console.log(ocrPositionList)
    for (const ocrPosition of ocrPositionList) {
      const boxPoints = ocrPosition.boxPoint;

      const height = Math.sqrt(
        Math.pow(boxPoints[2].x - boxPoints[1].x, 2) +
        Math.pow(boxPoints[2].y - boxPoints[1].y, 2)
      ) / devicePixelRatio;
      // 计算文本框的宽度
      const width = Math.sqrt(
        Math.pow(boxPoints[1].x - boxPoints[0].x, 2) +
        Math.pow(boxPoints[1].y - boxPoints[0].y, 2)
      ) / devicePixelRatio;

      console.log('height', height)
      const fontSize = Math.round(height * 0.7);

      const textContainer = this.getTextContainerDom( {
        left: Math.min(boxPoints[0].x, boxPoints[3].x) + 4,
        top: boxPoints[1].y,
        content: ocrPosition.text + '<span style="color: transparent !important;" class="hide">✔</span>'
      });
      textContainer.className = 'show';
      textContainer.style.width = `${width}px`;
      textContainer.style.backgroundColor = '#dfe4ea';
      textContainer.style.color = '#000000';
      textContainer.style.lineHeight = `${height * 1.2}px`;
      textContainer.style.fontSize = `${fontSize}px`;
    }
    this.__show()
  }
}


export default {
  info: ocrPlatFormInfo,
  ocrInstance: new LocalOcrContent(),
} as IOcrPlatFormItem;
