import OcrContentBase, {
  type IOcrPlatFormInfo,
  type IOcrPlatFormItem,
  type IOcrPlatFromConfigItem
} from '@/leaferApp/ocr/OcrContentBase'
import OcrCloudApi, { type ICloudOcrApiWordsResultItem } from '@/api/ucloud/OcrCloudApi'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { NotifyPlugin } from 'tdesign-vue-next'

// @ts-ignore
export interface UCloudOcrContentConfig extends IOcrPlatFromConfigItem {
}

const ocrPlatFormInfo: IOcrPlatFormInfo = {
  code: 'uCloud',
  name: 'uCloud',
  type: 'cloud',
  hide: true,
}

export class UCloudOcrContent extends OcrContentBase {
  constructor() {
    super(ocrPlatFormInfo)
  }

  async doOcr(ocrPlatFromConfig: IOcrPlatFromConfigItem): Promise<boolean> {
    const ocrPositions = await this.loadPictureInnerOcr(ocrPlatFromConfig);
    this.drawPictureInnerElement(ocrPositions);
    return Promise.resolve(false)
  }

  protected drawPictureInnerElement(ocrPositionList: ICloudOcrApiWordsResultItem[]): void {
    for (const ocrPosition of ocrPositionList) {
      const textContainer = this.getTextContainerDom( {
        ...ocrPosition.location,
        content: '',
      });
      textContainer.className='hide'
      textContainer.style.color = 'transparent'
      const lineHeight = ocrPosition.location.height;
      textContainer.style.lineHeight = `${lineHeight / devicePixelRatio}px`;
      const location = ocrPosition.location;
      for (const char of ocrPosition.chars) {
        const fillSpace = this.getFillMiddleSpace(location.left, textContainer.clientWidth, char.location.left);
        textContainer.innerHTML = textContainer.innerHTML + fillSpace + char.char;
      }
      textContainer.innerHTML = textContainer.innerHTML + '✔';
      const fillEndSpace = this.getFillEndSpace(textContainer.clientWidth, location.width);
      textContainer.innerHTML = textContainer.innerHTML + fillEndSpace;
    }
    this.__show();
  }


  private getFillMiddleSpace(textContainerLeft: number, textContainerWidth: number, left: number): string {
    const distance  = (left / devicePixelRatio) - (textContainerLeft / devicePixelRatio) - textContainerWidth;
    return this.getFillSpace(distance);

  }

  private getFillEndSpace(textContainerWidth: number, width: number) {
    console.log(width / devicePixelRatio - textContainerWidth)
    return this.getFillSpace(width / devicePixelRatio - textContainerWidth);
  }

  protected async loadPictureInnerOcr(ocrPlatFromConfig: UCloudOcrContentConfig): Promise<ICloudOcrApiWordsResultItem[]> {
    const base64 = await ScreenshotCapacity.exportScreenshotDataUrl(this.app);
    try {
      const response = await OcrCloudApi.ocr({
        image: base64
      });
      return response.wordsResult;
    }catch (e: any) {
      NotifyPlugin.warning({
        title: '提示',
        position: 'topLeft',
        content: e,
        duration: 60 * 1000,
        closeBtn: true,
      });
      throw Error('无法获取 ocr 数据');
    }
  }

}


export default {
  info: ocrPlatFormInfo,
  ocrInstance: new UCloudOcrContent(),
} as IOcrPlatFormItem;
