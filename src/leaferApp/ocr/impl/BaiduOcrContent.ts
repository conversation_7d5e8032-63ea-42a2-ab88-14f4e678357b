import OcrContentBase, {
  type IOcrPlatFormInfo,
  type IOcrPlatFormItem,
  type IOcrPlatFromConfigItem,
  OcrError
} from '@/leaferApp/ocr/OcrContentBase'
import BaiduApi from '@/api/baiduApi'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'

interface IBaiduOcrPosition {
  /**
   * 单字符
   */
  chars: {
    /**
     * 单字符的文字
     */
    char: string;
    /**
     * 单字符的位置
     */
    location: {
      top: number;
      left: number;
      width: number;
      height: number;
    };
  }[];
  /**
   * 文字块的文字
   */
  words: string;
  /**
   * 文字块的位置
   */
  location: {
    top: number;
    left: number;
    width: number;
    height: number;
  }
}

export interface BaiduOcrContentConfig extends IOcrPlatFromConfigItem {
  apiKey: string;
  secretKey: string;
}

const ocrPlatFormInfo: IOcrPlatFormInfo = {
  code: 'baidu',
  name: '百度 OCR',
  type: 'cloud'
}


/**
 * 百度 OCR 使用
 */
export class BaiduOcrContent extends OcrContentBase {
  constructor() {
    super(ocrPlatFormInfo);
  }

  async doOcr(ocrPlatFromConfig: BaiduOcrContentConfig): Promise<boolean> {
    const ocrPositions = await this.loadPictureInnerOcr(ocrPlatFromConfig);
    this.drawPictureInnerElement(ocrPositions);
    console.log('ocrPlatFromConfig.type', ocrPlatFromConfig.type)
    // if (ocrPlatFromConfig.type === 'image') {
    //   this.drawPictureInnerElement(ocrPositions);
    // }
    return Promise.resolve(true);
  }

  async loadPictureInnerOcr(ocrPlatFromConfig: BaiduOcrContentConfig): Promise<IBaiduOcrPosition[]> {
    if (ocrPlatFromConfig.key === -1) {
      // 通用
      return;
    }
    const base64 = await ScreenshotCapacity.exportScreenshotDataUrl(this.app);
    const accessToken = await this.getAccessToken(ocrPlatFromConfig);
     const data = await BaiduApi.getGeneralOcr(base64, accessToken);
     if (data.error_code) {
       throw new OcrError(data.error_code, this.ocrPlatFormInfo.code,
         ocrPlatFromConfig.key, data.error_msg);
     }
     return data.words_result;
  }

  drawPictureInnerElement(ocrPositionList: IBaiduOcrPosition[]) {
    this.ocrResultText = ocrPositionList.map(item =>  item.words).join("<br/>");
    for (const ocrPosition of ocrPositionList) {
      const textContainer = this.getTextContainerDom( {
        ...ocrPosition.location,
        content: ocrPosition.words,
      });
      textContainer.innerHTML = '';
      textContainer.style.color = 'transparent'
      textContainer.className = 'hide'
      const lineHeight = ocrPosition.location.height;
      textContainer.style.lineHeight = `${lineHeight / devicePixelRatio}px`;
      const location = ocrPosition.location;
      for (const char of ocrPosition.chars) {
        const fillSpace = this.getFillMiddleSpace(location.left, textContainer.clientWidth, char.location.left);
        textContainer.innerHTML = textContainer.innerHTML + fillSpace + char.char;
      }

      textContainer.innerHTML = textContainer.innerHTML +  '✔'
      const fillEndSpace = this.getFillEndSpace(textContainer.clientWidth, location.width);
      textContainer.innerHTML = textContainer.innerHTML + fillEndSpace;
    }
    this.__show();
  }

  private getFillMiddleSpace(textContainerLeft: number, textContainerWidth: number, left: number): string {
    const distance  = (left / devicePixelRatio) - (textContainerLeft / devicePixelRatio) - textContainerWidth;
    return this.getFillSpace(distance);

  }

  private getFillEndSpace(textContainerWidth: number, width: number) {
    console.log(width / devicePixelRatio - textContainerWidth)
    return this.getFillSpace(width / devicePixelRatio - textContainerWidth);
  }

  private async getAccessToken(config: BaiduOcrContentConfig) {
    const res = await BaiduApi.getAccessToken(config);
    return res.token;
  }
}



export default {
  info: ocrPlatFormInfo,
  ocrInstance: new BaiduOcrContent(),
} as IOcrPlatFormItem;
