import DataUtils from '@/utils/DataUtils'
import type { ChildProcessWithoutNullStreams } from 'child_process'
import { nanoid } from 'nanoid'
import ScreenshotApi from '@/core/sdk/screenshotApi'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'

export interface IEventDataBase {
  /**
   * 事件唯一 id
   */
  eventId: string;

  /**
   * 事件 code
   */
  eventCode: string;
}


export interface IEventDataReq<T = any> extends IEventDataBase {
  /**
   * 参数
   */
  params: T;
}

export interface IEventDataResp<T = any> extends IEventDataBase {
  success: boolean;
  errorMessage: string;
  data: T;
}




/**
 * 本地 OCR 服务类
 */
export default class LocalOcrService {

  /**
   * 当前
   * @private
   */
  private __javaProcess: ChildProcessWithoutNullStreams | null;

  private __invokeMethodResult: Map<string, any> = new Map<string, any>();

  private __maxTryReturnCount = 30;

  private __listenerManager = new Map<string, (data: IEventDataResp) => void>();

  private __heartbeatTimer: NodeJS.Timer;

  constructor() {

  }

  /**
   * 启动服务
   */
  start() {
    if (this.__javaProcess) {
      console.log('服务已经启动')
      return;
    }

    const javaPath = window.path.join(DataUtils.getDataOcrPath(), 'paddleOcr', 'u-ocr.jar');
    let javaExe
    if (utools.isWindows()) {
      javaExe = window.path.join(DataUtils.getDataSaveLibPath(), 'jre', 'bin', 'java.exe');
    } else {
      javaExe = window.path.join(DataUtils.getDataSaveLibPath(), 'jre', 'bin', 'java');
    }

    if (!window.fs.existsSync(javaPath)) {
      return;
    }

    const toolConfigStore = useToolConfigStore();
    if (toolConfigStore.ocrConfig.globalType !== 'local') {
      return;
    }

    this.__javaProcess = window.spawn(javaExe, [
      '-Dfile.encoding=UTF-8',
      '-jar',
      javaPath]);


    const rl = window.readLine.createInterface({
      input: this.__javaProcess.stdout,
      crlfDelay: Infinity // 确保可以正确处理换行符
    });
    this.__javaProcess.stdout.on('data', (data) => {
      console.log(data)
    });
    // 逐行处理数据
    rl.on('line', (line) => {
      // const dataStr = window.nodeBuffer.Buffer.from(line, 'binary').toString('utf8');
      if (!line || !line.toString().startsWith('data:')) {
        return;
      }

      const dataJson = line.replace('data:', '');
      this.handleData(dataJson);
      console.log(dataJson);
    });


    // 错误处理
    this.__javaProcess.stderr.on('data', (data) => {
      if (data.toString().startsWith("[main]")) {
        return;
      }
      console.error(`Java process error: ${data}`);
    });

    // 进程关闭处理
    this.__javaProcess.on('close', (code) => {
      console.log(`Java process exited with code ${code}`);
      this.clearData();
    });
    this.startHeartbeat()
  }

  /**
   * 禁止服务
   */
  stop() {
    this.__javaProcess.kill('SIGKILL');
    this.clearData();
  }

  /**
   * 添加 Listener 监听器
   * @param eventCode 事件 code
   * @param listener 监听器
   */
  addListener(eventCode: string, listener: (data: IEventDataResp) => void) {
    this.__listenerManager.set(eventCode, listener);
  }

  /**
   * 移除 Listener 监听器
   * @param eventCode 事件 code
   */
  removeListener(eventCode: string) {
    this.__listenerManager.delete(eventCode);
  }

  handleError() {

  }


  /**
   * 执行方法 (无返回值)
   * @param eventCode 事件 code
   * @param params 事件 code
   */
  invokeMethod(eventCode: string, params: any) {
    const eventId = nanoid();
    this.sendData({
      eventId,
      eventCode,
      params,
    })
  }


  /**
   * 执行方法 (存在返回值)
   * @param eventCode 事件 code
   * @param params 事件参数
   */
  async invokeMethodSync(eventCode: string, params: any): Promise<any> {
    const eventId = nanoid();
    let timer: NodeJS.Timer;
    let tryReturnCount = 0;
    return new Promise((resolve, reject) => {
      this.__invokeMethodResult.set(eventId, null);
      timer = setInterval(() => {
        const result = this.__invokeMethodResult.get(eventId);
        if (result) {
          clearInterval(timer as any);
          resolve(result);
        }
        tryReturnCount++;
        if (tryReturnCount > this.__maxTryReturnCount) {
          this.__invokeMethodResult.delete(eventId);
          clearInterval(timer as any);
          reject('未获取到返回值');
        }
      }, 100);
      this.sendData({
        eventId,
        eventCode,
        params
      });
    });
  }

  private sendData(data: IEventDataReq) {
    console.log('sendData', data)
    this.__javaProcess.stdin.write(JSON.stringify(data) + '\n');
  }


  /**
   * 处理数据
   * @param data 返回的数据
   * @private
   */
  private handleData(data: string) {
    const dataJson = data.toString().replace('data:', '');
    console.log(dataJson);
     const resp = JSON.parse(dataJson) as IEventDataResp;
     if (this.__invokeMethodResult.has(resp.eventId)) {
       this.__invokeMethodResult.set(resp.eventId, resp);
     } else {
       // 异步处理
       const res = this.__listenerManager.get(resp.eventId);
       res && res(resp);
     }
  }

  /**
   * 开始心跳包
   */
  private startHeartbeat() {
    this.__heartbeatTimer = setInterval(() => {
      window.localOcrService.invokeMethod('heartbeat', null);
    }, 10 * 1000);
  }

  private clearData() {
    this.__javaProcess = null;
    this.__heartbeatTimer && clearInterval(this.__heartbeatTimer as any);
  }

  async ocr(webContentsId: number, params: Record<string, any>) {
    const result = await this.invokeMethodSync('ocr', params);
    ScreenshotApi.sendOcrResult(webContentsId, result);
  }
}
