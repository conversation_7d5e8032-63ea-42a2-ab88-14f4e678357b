/**
 * 背景
 */
const ElementBackground = 'background';
/**
 * 马赛克
 */
const ElementMosaic = "mosaic";
/**
 * 画板
 */
const ElementDrawGroup = "draw";

const ElementMiddleGroup = 'middle';
/**
 * 截图容器 group
 */
const ElementScreenShotGroup = 'screenshotGroup';
const ElementScreenShot = 'mainImage';
/**
 * 平铺水印
 */
const ElementWatermarkTiled = "watermarkTiled";
/**
 * 矩形选择区域
 */
const ElementChooseAreaRect =  "chooseAreaRect";
const ElementCaptureContainerCover = "captureContainerCover";

//region 序号工具
const ID_SERIAL_NUMBER_EDITOR = "serialNumberEditor";
const CODE_SERIAL_NUMBER = "serialNumber";
const CODE_SERIAL_NUMBER_LINE = "serialNumberLine";
const ClASS_SERIAL_NUMBER = "serialNumber";
const ClASS_SERIAL_INNER_NUMBER = "serialInnerNumber";
const ClASS_SERIAL_INNER_TEXT_REGION = "serialInnerTextRegion";
//end region


//region 尺子工具
// const ID_SERIAL_NUMBER_EDITOR = "serialNumberEditor";
const CODE_RULER = "ruler";
const CLASS_RULER_LINE = "rulerLine";
const CLASS_RULER_TEXT = "rulerText";
//end region
const ID_HIGHLIGHT_GROUP=  "highlightGroup";
const ID_HIGHLIGHT_BOX=  "highlightBox";
const ID_HIGHLIGHT_ERASER_GROUP = "highlightEraserGroup";

export default {
  ElementBackground,
  ElementMosaic,
  ElementDrawGroup,
  ElementMiddleGroup,
  ElementScreenShot,
  ElementScreenShotGroup,
  ElementWatermarkTiled,
  ElementChooseAreaRect,
  ElementCaptureContainerCover,
  ClASS_SERIAL_NUMBER,
  ClASS_SERIAL_INNER_NUMBER,
  CODE_SERIAL_NUMBER_LINE,
  CODE_SERIAL_NUMBER,
  ClASS_SERIAL_INNER_TEXT_REGION,
  ID_SERIAL_NUMBER_EDITOR,
  CODE_RULER,
  CLASS_RULER_LINE,
  CLASS_RULER_TEXT,
  ID_HIGHLIGHT_GROUP,
  ID_HIGHLIGHT_BOX,
  ID_HIGHLIGHT_ERASER_GROUP
}
