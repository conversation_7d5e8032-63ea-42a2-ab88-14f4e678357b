import type { App, Text } from 'leafer-ui'
import { Box, defineKey, Group, PointerEvent } from 'leafer-ui'
import { InnerEditorEvent } from '@leafer-in/editor'
import type { IBoxInputData, ITextInputData, IUI } from '@leafer-ui/interface'
import LeaferConstant from '@/leaferApp/LeaferConstant'

export interface IOcrPosition {
  pos: {
    x: number;
    y: number;
  }[];
  content: string;
  score: number;
}

class OcrContent {
  constructor() {
    this.hide();
  }

  async loadOcr(): Promise<IOcrPosition[]> {
    return window.localOcr.testOcr();
  }

  init() {
    const ocrElement = this.getElement();
    const pixelRatio = window.devicePixelRatio;
    this.loadOcr().then(ocrPositionList => {
      ocrPositionList = ocrPositionList.sort((a, b) => {
        if (a.pos[0].x === b.pos[0].x) {
          return a.pos[0].y - b.pos[0].y;
        }
        return a.pos[0].x - b.pos[0].x;
      });
      for (let i = 0; i < ocrPositionList.length; i++) {
        const ocrPosition = ocrPositionList[i];
        const ocrText = document.createElement('div');
        // 字高
        const textHeight = Math.ceil(((ocrPosition.pos[3].y - ocrPosition.pos[0].y + ocrPosition.pos[2].y - ocrPosition.pos[1].y) + 9) / pixelRatio);
        ocrText.style.position = 'fixed';
        ocrText.style.color = 'transparent';
        ocrText.style.zIndex = '999';
        ocrText.style.userSelect = 'text';
        ocrText.style.fontSize = `${textHeight / 2}px`;
        ocrText.style.padding = '2px';
        ocrText.style.left = `${Math.floor((ocrPosition.pos[0].x + 9) / pixelRatio)}px`;
        ocrText.style.top = `${Math.floor((ocrPosition.pos[0].y + 9) / pixelRatio)}px`;
        ocrText.innerText = ocrPosition.content;
        ocrElement.append(ocrText);
        const width = (ocrPosition.pos[1].x - ocrPosition.pos[0].x) / pixelRatio;
        const ocrTextRect = ocrText.getBoundingClientRect();
        ocrText.style.lineHeight = `${textHeight - (ocrTextRect.height / 2)}px`;
        console.log(width, ocrTextRect.width)
        console.log(ocrText.getBoundingClientRect())

      }
      this.show();
    })
  }

  hide() {
    const ocrElement = this.getElement();
    ocrElement.style.opacity = '0';
    ocrElement.style.pointerEvents = 'none';
  }

  show() {
    const ocrElement = this.getElement();
    ocrElement.style.opacity = '1';
    ocrElement.style.pointerEvents = 'auto';
  }

  getElement() {
    return document.getElementById('ocr');
  }
}


function reloadTextBox(app: App, text: Text) {
  text.set({
    editable: true,
    draggable: true,
  });
  const box = text.parent;
  const editor = app.editor;
  defineKey(box, 'editConfig', {
    get() { return {
      rotateable: false,
      flipable: false
    }}
  });

  text.on(InnerEditorEvent.OPEN, (e) => {
    window.keyboardManager.disableKeyboard();
  });
  text.on(InnerEditorEvent.CLOSE, (e) => {
    console.log(InnerEditorEvent.CLOSE, e);
    text.editable = false;
    box.hitChildren = true;
    editor.config.keyEvent = true;
    window.keyboardManager.enableKeyboard();
    editor.select(box);
  });
  text.parent.on(PointerEvent.DOUBLE_TAP, (e: PointerEvent) => {
    e.stop();
    e.stopNow();
    e.stopDefault();
    window.tempData.DOUBLE_TAP = true;
    editor.select(text);
    box.hitChildren = false;
    text.editable = true;
    editor.openInnerEditor();
    editor.config.keyEvent = false;
    setTimeout(() => {
      delete window.tempData.DOUBLE_TAP;
    }, 666);
  });
}
/**
 * 创建文本框
 * @param app Leafer app 对象
 * @param container 文本存放容器
 * @param boxData 文件 box 属性
 * @param textData 文本 text 属性
 */
function createTextBox(app: App,
                       container: IUI,
                       boxData: IBoxInputData = {},
                       textData: ITextInputData = {}): Box {
  const box = new Box({
    // width,
    // height,
    cornerRadius: 20,
    editable: true,
    draggable: true,
    ...boxData,
    children: [{
      tag: 'Text',
      text: '请输入文字',
      textOverflow: 'hide',
      padding: [10, 10],
      ...textData,
    }]
  });
  defineKey(box, 'editConfig', {
    get() { return {
      rotateable: false,
      flipable: false
    } }
  });
  container.add(box);
  const iui = box.children[0];
  box.hitChildren = false;
  iui.editable = true;
  app.editor.select(iui);
  app.editor.config.rotateable = false;
  app.editor.config.flipable = false;
  app.editor.config.keyEvent = false;
  app.editor.openInnerEditor();
  iui.on(InnerEditorEvent.OPEN, (e) => {
    window.keyboardManager.disableKeyboard();
  });
  iui.on(InnerEditorEvent.CLOSE, (e) => {
    console.log(InnerEditorEvent.CLOSE, e);
    iui.editable = false;
    box.hitChildren = true;
    app.editor.config.keyEvent = true;
    window.keyboardManager.enableKeyboard();
    app.editor.select(box);
  });
  box.on(PointerEvent.DOUBLE_TAP, (e: PointerEvent) => {
    e.stop();
    e.stopNow();
    e.stopDefault();
    window.tempData.DOUBLE_TAP = true;
    app.editor.select(iui);
    box.hitChildren = false;
    iui.editable = true;
    app.editor.openInnerEditor();
    app.editor.config.keyEvent = false;
    setTimeout(() => {
      delete window.tempData.DOUBLE_TAP;
    }, 666);
  });
  return box;
}

/**
 * 获取截图 Group
 * @param app
 */
function getScreenshotGroup(app: App) {
  return app.tree.findId(LeaferConstant.ElementScreenShotGroup) as Group;
}


export default {
  createTextBox,
  reloadTextBox,
  getScreenshotGroup,
  OcrContent
}
