import hotkeys, { type HotkeysEvent, type KeyHandler } from 'hotkeys-js'
import { KEYBOARD_MAP } from '@/core/constants/StrConstants'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'


export interface HandleOptions {
  downHandle: KeyHandler;
  upHandle?: KeyHandler;
}

export interface KeyboardConfig {
  ctrl?: boolean;
  alt?: boolean;
  mode?: 'hold' | 'double' | 'switchover' | '',
  key?: string,
}

export default class KeyboardManager {
  private __map: Map<string, string>;
  private __holdPressMap: Map<string, number>;
  private __switchoverMap: Map<string, NodeJS.Timer>;
  private __holdPressDuration: number = 1500;

  constructor() {
    console.log('init..KeyboardManager')
    this.__map = new Map();
    this.__holdPressMap = new Map();
    this.__switchoverMap = new Map();
  }

  /**
   * 添加按键
   * @param code
   * @param keyboard
   * @param handle
   * @param scope
   */
  public addKeyboard(code: string, keyboard: string, handle: KeyHandler, scope = 'all') {
    this.removeKeyboard(code);
    if (keyboard) {
      this.__map.set(code, keyboard);
      hotkeys(keyboard, scope, handle);
    }
  }

  public addKeyboardConfig(code: string,
                           keyboardConfig: KeyboardConfig,
                           handle: HandleOptions,
                           scope: 'default' | 'all' | string = 'all') {
    this.removeKeyboard(code);
    const keys: string[] = [];

    if (keyboardConfig.ctrl) {
      keys.push(utools.isMacOS() ? 'command' : 'control');
    }

    if (keyboardConfig.alt) {
      keys.push(utools.isMacOS() ? 'option' : 'alt');
    }

    if (!keyboardConfig.key) {
      // 不存在按键
      return;
    }

    keys.push(KEYBOARD_MAP[keyboardConfig.key] ? KEYBOARD_MAP[keyboardConfig.key] : keyboardConfig.key);

    const key = keys.join('+');

    console.log('addKeyboardConfig', key, code);
    this.__map.set(code,  key);

    if (keyboardConfig.mode == 'hold') {
      this.handleHoldEvent(code, key, handle);
    } else if (keyboardConfig.mode === 'switchover') {
      this.handleSwitchoverEvent(code, key, handle);
    }else {
      hotkeys(key, scope, handle.downHandle);
    }
  }

  public addFuncKeyboard(code: string, handle: HandleOptions) {
    const keyboardConfigStore = useKeyboardConfigStore();
    const keyboardConfig = keyboardConfigStore.getDefaultFuncKeyboardConfig(code);
    this.addKeyboardConfig(code, keyboardConfig, handle);
  }



  /**
   * 移除按键
   * @param code
   */
  public removeKeyboard(code: string) {
    if (this.__map.has(code)) {
      hotkeys.unbind(this.__map.get(code));
    }
  }

  private handleHoldEvent(code: string, key: string, options: HandleOptions) {
    hotkeys(key, (keyboardEvent: KeyboardEvent, hotkeysEvent: HotkeysEvent) => {
      if (keyboardEvent.type === 'keydown') {
        if (!this.__holdPressMap.has(code)) {
          this.__holdPressMap.set(code, Date.now());
          return;
        }
        const timer = this.__holdPressMap.get(code)!;
        if (Date.now() - timer > this.__holdPressDuration) {
          options.downHandle(keyboardEvent, hotkeysEvent);
        }

      } else if (keyboardEvent.type === 'key.up') {
        this.__holdPressMap.delete(code);
      }
    });
  }

  private handleSwitchoverEvent(code: string, key: string, handle: HandleOptions) {
    hotkeys(key, (keyboardEvent: KeyboardEvent, hotkeysEvent: HotkeysEvent) => {
      let timer = this.__switchoverMap.get(code);
      timer && clearTimeout(timer as any);
      const exist = this.__switchoverMap.has(code);
      timer = setTimeout(() => {
        if (handle.upHandle) {
          handle.upHandle(keyboardEvent, hotkeysEvent);
          this.__switchoverMap.delete(code);
        }
      }, 450);
      this.__switchoverMap.set(code, timer);

      if (!exist) {
        handle.downHandle(keyboardEvent, hotkeysEvent);
      }
    });
  }

  /**
   * 禁用快捷键
   */
  disableKeyboard() {
    this.switchoverScope('NONE');
  }

  /**
   * 启用快捷键
   */
  enableKeyboard() {
    this.switchoverScope('default');
  }

  switchoverScope(scope: 'default' | 'NONE' | string)  {
    hotkeys.setScope(scope);
  }
}
