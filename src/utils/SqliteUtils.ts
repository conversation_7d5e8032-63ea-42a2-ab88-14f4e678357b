import ExeUtils from '@/core/utils/ExeUtils'
import DataUtils from '@/utils/DataUtils'
import jsUtil from '@/utils/jsUtil'
import JsUtil from '@/utils/jsUtil'

class SqliteError extends Error {
  constructor(message: string) {
    super(message)
  }
}
interface ExecuteSQLOptions {
  resultFormat?: 'json' | 'array' | 'value';
  empty?: any;
}

interface ExecutePageSQLOptions extends ExecuteSQLOptions {
  total: boolean;
}

interface PageResult<T> {
  total?: number;
  data: T[]
}

export default class SqliteUtils {

  /**
   * 数据库路径
   * @private
   */
  private readonly database;

  private  executeSqlite = '';

  constructor(database: string, checkSqliteEnvironmentCallback?: (res: boolean) => void) {
    this.database = database;
    this.checkSqliteEnvironment().then((res) => {
      checkSqliteEnvironmentCallback && checkSqliteEnvironmentCallback(res &&  this.checkDatabase());
    })

  }

  /**
   * 检查 Sqlite 环境
   */
  async checkSqliteEnvironment() {
    try {
      if (window.utools.isWindows()) {
        const windowsPath = window.path.join(DataUtils.getSqlitePath(), 'sqlite3.exe');
        try {
          await ExeUtils.execute(`${windowsPath} -version`);
          this.executeSqlite = windowsPath;
        }catch (e) {
          await ExeUtils.execute('sqlite3 -version');
          this.executeSqlite = 'sqlite3';
        }
        return true;
      }
      console.log('1--3123213123123')
      await ExeUtils.execute('sqlite3 -version');
      this.executeSqlite = "sqlite3";
      return true;
    }catch (e) {
      console.log(e)
      return false;
    }
  }

  public async initDatabase(initSql: string) {
    try {
      if (!this.checkDatabase()) {
        window.fs.writeFileSync(this.database, '', 'binary');
      }
      const tableNames = await this.executeSQL<string[]>('select name from sqlite_master', {}, {
        resultFormat: 'array'
      });
      console.log(tableNames)
      if (tableNames.length <= 1) {
        await this.executeSQL(initSql);
      }
    } catch (error) {
      console.log(error);
    }
  }


  public async saveOrUpdate(tableName: string, idKeys: string[], obj: Record<string, any>, excludeField: string[] = []) {
    const fieldList = Object.keys(obj)
      .filter(item => !excludeField.includes(item));

    const fieldNameList = fieldList.map(key => jsUtil.camelToSnake(key)).join(',');
    const fieldKeys = fieldList.map(field => `#{${field}}`).join(',');
    fieldList.filter(item => !idKeys.includes(item))
      .map(field => `${jsUtil.camelToSnake(field)}=#{${field}}`);
    console.log(obj)
    await this.executeSQL(`INSERT INTO file_library (${fieldNameList})
                                VALUES (${fieldKeys})
                                ON CONFLICT(${idKeys.map(key => jsUtil.camelToSnake(key)).join(',')}) 
                                DO UPDATE set update_time = #{updateTime}, create_time = #{createTime}`, {
      ...obj
    }, { resultFormat: 'array' });
  }


  public async page<T=any>(searchSQL: string,
                     params: Record<string, any> & {pageSize: number, page: number} = {pageSize: 20, page: 1},
                     options: ExecutePageSQLOptions = { total: true }): Promise<PageResult<T>> {
    const result: PageResult<T> = {
      data: []
    };

    if (options.total) {
      const noOrderSql = searchSQL.replace(/order by.+/, '')
      const countSQL = `select count(*) from (${noOrderSql}) TOTAL`;
      result.total = Number(await window.db.executeSQL<number>(countSQL, {}, { resultFormat: 'value' }));
    }
    const offset = (params.page - 1) * params.pageSize;
    result.data = await window.db.executeSQL<T[]>(`${searchSQL} limit #{offset}, #{pageSize}`, {
      ...params,
      offset,
    }, {
      empty: []
    });
    return result;
  }

  /**
   * SQL 执行
   * @param sql SQL 字符串
   * @param params SQL 参数
   * @param options 执行的可选参数
   * @private
   */
  public executeSQL<T=any>(sql: string,
                           params?: Record<string, any>,
                           options: ExecuteSQLOptions = {}): Promise<T> {
    const { resultFormat = 'json', empty = null } = options;

    return new Promise((resolve, reject) => {
      this.checkDatabase();
      const executeSQL = this.getExecuteSQL(sql, params);
      console.log(executeSQL);
      const selectSQL = sql.trim().toLowerCase().startsWith('select')
      const executePrefix = `${this.executeSqlite} -csv -header ${selectSQL ? '-readonly' : ''} "${this.database}"`;
      const command = `${executePrefix} "${selectSQL ? '' : 'PRAGMA busy_timeout = 50000;'}${executeSQL.replace(/\n/g, ' ').replace(/\s+/g, ' ')}"`;
      window.exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error.message)
          return;
        }
        if (stderr) {
          reject(stderr)
          return;
        }

        // 处理输出
        if (!stdout) {
          if (empty) {
            resolve(empty);
          } else {
            resolve(resultFormat === 'json' ? {} as T : [] as T);
          }
          return;
        }
        if (resultFormat === 'json') {
          resolve(this.csvToJson(stdout as string) as T);
        } else if (resultFormat === 'array' || resultFormat === 'value') {
          const csvToArray = this.csvToArray(stdout as string);
          if (resultFormat === 'array') {
            resolve(csvToArray as T);
          } else {
            resolve(csvToArray[0] as T);
          }
        }
      });
    })
  }


  /**
   * 获取执行 SQL, 支持占位符 #{} 和 ${} 的使用 <br/>
   * #{}: 需要防注入拼接数据 </br>
   * ${}: 直接拼接
   * @param sql SQL 字符串
   * @param params SQL 参数
   * @private
   */
  private  getExecuteSQL(sql: string, params?: Record<string, any>) {
    if (!params) {
      return sql;
    }

    sql = sql.replace(/#\{(\w+)\}/g, (match, key) => {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        // 返回转义后的参数值
        return this.escapeSQLValue(params[key]);
      }
      throw new Error(`Parameter ${key} is not provided.`);
    });

    // 替换 ${key} 占位符，直接拼接
    sql = sql.replace(/\$\{(\w+)\}/g, (match, key) => {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        // 直接返回参数值，注意可能导致 SQL 注入
        return params[key]; // 注意：这里需要确保 params[key] 是安全的
      }
      throw new Error(`Parameter ${key} is not provided.`);
    });

    return sql;
  }


  /**
   * sql 安全转译
   * @param value 转译参数
   * @private
   */
  private  escapeSQLValue(value: any): string {
    // 处理不同类型的值，确保安全
    if (value === null || value === undefined) {
      return 'NULL';
    } else if (typeof value === 'string') {
      // 对字符串值进行转义
      return `'${value.replace(/'/g, "''")}'`;
    } else if (typeof value === 'number') {
      // 数字直接返回
      return value.toString();
    } else {
      // 其他类型（如布尔值、对象等）转换为字符串
      return value.toString();
    }
  }


  /**
   * 将 csv 格式的数据转换为 json 字符串
   * @param csvData
   * @private
   */
  private csvToJson(csvData: string) {
    // 将 CSV 数据拆分为行
    const lines = csvData.trim().split('\n');
    // 获取标题
    const headers = lines[0].split(',')
      .map(item => JsUtil.snakeToCamel(item));
    // 将每一行转换为 JSON 对象
    return lines.slice(1).map(line => {
      const values = line.split(',');
      return headers.reduce((obj, header, index) => {
        // @ts-ignore
        obj[header] = values[index];
        return obj;
      }, {});
    });
  }

  private csvToArray(csvData: string): string[] | string[][] {
    const lines = csvData.trim()
      ?.split('\n');
    lines.splice(0, 1);

    if (!lines || lines.length === 0) {
      return [];
    }

    if (lines[0].split(',').length === 1) {
      return lines.map(item => item.split(',')[0]);
    } else {
      return lines.map(item => item.split(','));
    }
  }

  private checkDatabase() {
    if (!this.database) {
      return false;
    }
    return window.fs.existsSync(this.database);
  }
}
