import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import fileUtils from '@/utils/FileUtils'
import { nanoid } from 'nanoid'
import dayjs from 'dayjs'
import DataUtils from '@/utils/DataUtils'
import sqls from '@/sql/sqls'

export interface IScreenshotData {
  base64: string;
  tags?: number[];
}

export interface ScreenshotInfoData {
  fileId: string;
  updateTime: string;
  createTime: string;
  tags: number[];
  deviceCode: string;
}
export interface fileLibraryDO {
  fileId: string;
  updateTime: string;
  createTime: string;
}

export interface FileLibraryTagDO {
  fileId: string;
  tagId: number;
  updateKey: string;
}

export default class ScreenshotDB {

  static isSave(): boolean {
    const { localFileManagerEnable } = useFileManagerStore();
    return localFileManagerEnable;
  }

  static createScreenshot(screenshotData: IScreenshotData) {
    console.log('createScreenshot', screenshotData);
    if (!ScreenshotDB.isSave()) {
      return;
    }
    console.log('createScreenshot-ok', screenshotData);

    const { base64 } = screenshotData;
    const ext = base64.match(/^data:image\/(\w+)\+?\w*;base64,/);
    const suffix = ext && ext.length > 1 ? ext[1] : 'png';

    const fileId = nanoid();

    const fileDir = window.path.join(DataUtils.getDataScreenshotLibrary(), fileId);
    fileUtils.createDirIfAbsent(fileDir);
    window.fs.writeFileSync(window.path.join(fileDir, `original.${suffix}`),
      base64.replace(/^data:image\/\w+;base64,/, ""),
      'base64');

    const deviceCode = window.utools.getNativeId();
    const createTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    const updateTime=  dayjs().format('YYYY-MM-DD HH:mm:ss');
    const fileInfoData: ScreenshotInfoData = {
      fileId,
      createTime,
      updateTime,
      tags: screenshotData.tags || [],
      deviceCode,
    };

    window.fs.writeFileSync(window.path.join(fileDir, `info.json`), JSON.stringify(fileInfoData), 'utf8');
    this.syncLocalFileIdToDB(fileId).then(() => {});
    return fileId;
  }

  static async localSyncDB() {
    const dirList: string[] = window.fs.readdirSync(DataUtils.getDataScreenshotLibrary())
      .filter(item => !item.toString().includes('.'));
    let fileId = 0;
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const fileIdsInDb = await window.db
        .executeSQL(`select file_id from file_library where file_id > #{fileId} order by file_id limit 50`, {
          fileId
      }, {resultFormat: 'array'});

      if (!fileIdsInDb.length) {
        break;
      }
      fileId = fileIdsInDb[fileIdsInDb.length - 1];
      for (const fileIdInDb of fileIdsInDb) {
        if (!dirList.includes(fileIdInDb)) {
          await ScreenshotDB.deleteLocalDBFile(fileIdInDb);
          continue;
        }
        // 存在 fileId 信息同步
        await ScreenshotDB.syncLocalFileIdToDB(fileIdInDb);
      }
    }

    // 文件直接同步到数据库, 数据库中不存在
    for (const fileId of dirList) {
      const fileIds = await window.db
        .executeSQL(`select file_id from file_library where file_id = #{fileId} order by file_id limit 1`, {
          fileId
        }, {resultFormat: 'array'});
      if (fileIds.length > 0) {
        continue;
      }
      // 存在 fileId 信息同步
      await ScreenshotDB.syncLocalFileIdToDB(fileId);
    }
  }

  private static async deleteLocalDBFile(fileId: string) {
    if (fileId) {
      await window.db.executeSQL('delete from file_library where file_id = #{fileId}', {
        fileId
      });
      await window.db.executeSQL('delete from file_library_tag where file_id = #{fileId}', {
        fileId
      });
    }
  }



  private static async syncLocalFileIdToDB(fileId: string) {
    // 存在 fileId 信息同步
    const fileDir = window.path.join(DataUtils.getDataScreenshotLibrary(), fileId);
    const dataStr = window.fs.readFileSync(window.path.join(fileDir, 'info.json'), 'utf-8');
    if (dataStr) {
      const data = JSON.parse(dataStr);
      if (!data) {
        return;
      }
      await this.syncLocalDataToDB(data);
    }
  }

  private static async syncLocalDataToDB(fileInfo: ScreenshotInfoData) {
    await window.db.saveOrUpdate('file_library', ['fileId'], fileInfo, ['tags']);
    await ScreenshotDB.syncLocalTagsDB(fileInfo);
  }

  public static async syncLocalTagsDB(data: ScreenshotInfoData) {
    const updateKey = nanoid(32);
    if (data.tags && data.tags.length) {
      const join = data.tags.map(tagId => `('${data.fileId}', '${tagId}', '${updateKey}')`)
        .join(",");
      await window.db.executeSQL(`INSERT INTO file_library_tag (file_id, tag_id, update_key )
                                VALUES ${join} 
                                ON CONFLICT(file_id, tag_id) DO UPDATE set update_key = #{updateKey}`,
        { updateKey },
          {resultFormat: 'array'});
    }
    await window.db.executeSQL(`
                    delete from file_library_tag where file_id = #{fileId} and update_key != #{updateKey}`,
      {updateKey, fileId: data.fileId});
  }
  public static async deleteFile(data: ScreenshotInfoData) {
    await window.db.executeSQL(``,
      {fileId: data.fileId});
    await window.db.executeSQL(`
                    delete from file_library where file_id = #{fileId}`,
      {fileId: data.fileId});
    window.fs.rmSync(window.path.join(DataUtils.getDataScreenshotLibrary(), data.fileId),
      { recursive: true, force: true });
  }

  public static async clearAllData() {
    await window.db.executeSQL(`DROP TABLE IF EXISTS file_library;`);
    await window.db.executeSQL(`DROP TABLE IF EXISTS file_library_tag;`);
    await window.db.executeSQL(`DROP TABLE IF EXISTS db_version;`);
    await window.db.initDatabase(sqls.initTable);
  }
}
