import {
  createScreenCapture,
  createScreenShot,
  type CreateScreenShotOptions,
  getCaptureDisplays
} from '@/utils/screenshot'
import ScreenshotApi from '@/core/sdk/screenshotApi'
import type { Router } from 'vue-router'
import { filePathToFileUrlData } from '@/utils/jsUtil'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import { DEFAULT_GLOBAL_KEYBOARD_CONFIG } from '@/core/constants/KeyboardsConstants'
import DataUtils from '@/utils/DataUtils'
import { UtoolsCodeEvent } from '@/events/UtoolsCodeEvent'
import ScreenshotDB from '@/utils/ScreenshotDB'
import { type ISceneConfig, useSceneStore } from '@/stores/SceneStore/SceneStore'
import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import MediaUtils from '@/script/media/MediaUtils'

// import MediaUtils from "@/utils/MediaUtils";


export interface PluginEnterEvent {
  code: string;
  type: string;
  payload: any;
  option: any
}

export function dispatchUtoolsCodeEvent(data: PluginEnterEvent, router: Router) {
  const event = new UtoolsCodeEvent(data, router)
  console.log('dispatchUtoolsCodeEvent', event)
  window.dispatchEvent(event)
}


function isUtoolsCodeEventDetail(obj: Event): obj is UtoolsCodeEvent {
  return obj instanceof UtoolsCodeEvent
}

function addEventListener(type: string, event: (e: UtoolsCodeEvent) => void) {
  window.addEventListener(type, (e) => {
    if (isUtoolsCodeEventDetail(e)) {
      event(e)
    }
  })
}

function screenCapture(executePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const settingUserStore = useSettingUserStore()
      console.log('utools---截图开始')
      utools.screenCapture((imgBase64) => {
        console.log('utools---截图结束回掉')
        if (!imgBase64) {
          reject()
          return
        }
        if (settingUserStore.generalSetting.screenshotLocation === 'mouse') {
          const cursorScreenPoint = utools.getCursorScreenPoint()
          window.tempData.screenshotLocation = {
            x: cursorScreenPoint.x,
            y: cursorScreenPoint.y
          }
        }
        resolve(imgBase64)
      })
    } catch (e) {
      console.log('utools---出现异常', e)
      reject(e)
    }
  })

}

async function screenshotCapture(e: UtoolsCodeEvent, capture: any, sceneConfig?: ISceneConfig) {
  const playStatus = utools.isPurchasedUser() && sceneConfig && sceneConfig.suspendMedia && MediaUtils.playStatus();
  if(playStatus) {
    MediaUtils.pausePause().then(() => {});
  }
  const createScreenShotOptions: CreateScreenShotOptions = {}
  Promise.all([capture(DataUtils.getDataSaveLibPath()), createScreenShot(createScreenShotOptions)])
    .then(([base64, { webContentsId, win }]) => {
      loadSceneConfig(webContentsId, base64, sceneConfig);
      ScreenshotApi.sendBase64Screenshot(webContentsId, base64,
        { hideToolBar: e.hasParamKey('suspend'), screenshotLocation: window.tempData.screenshotLocation })
      delete window.tempData.screenshotLocation
    }).catch(e => {
    console.error('screenshot.capture, 出现异常', e)
    createScreenShotOptions.win && createScreenShotOptions.win.close()
  }).finally(() => {
    utools.outPlugin();
    if(playStatus) {
      MediaUtils.pausePause();
    }
  })
}


function userCustomScreenshotCapture(executePath?: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const beforePngs = window.clipboard.readImage().toDataURL();
    const { scScreenshotCustomExecute } = useSettingUserStore();
    window.exec(scScreenshotCustomExecute).on('close', (code) => {
      const pngs = window.clipboard.readImage().toDataURL();
      if (!pngs || pngs === beforePngs) {
        reject();
      }
      resolve(pngs);
    });
  })
}
async function handleScreenshotCapture(e: UtoolsCodeEvent) {
  utools.hideMainWindow()
  utools.setExpendHeight(0)
  const settingUserStore = useSettingUserStore()
  const scScreenshotWay = settingUserStore.scScreenshotWay
  let capture = screenCapture
  if (scScreenshotWay == 'local') {
    capture = window.localCapture!
  } else if (scScreenshotWay === 'custom') {
    capture = userCustomScreenshotCapture
  }
  if (!capture) {
    utools.showNotification('当前系统还未支持当前的截图方式')
    utools.outPlugin()
    return
  }

  const sceneStore = useSceneStore()
  let sceneConfig = null
  if (e.hasParamKey('sceneCode')) {
    const sceneCode = e.getParamsKey('sceneCode')
    sceneConfig = sceneStore.getSceneConfig(sceneCode)
  }

  if (sceneConfig && sceneConfig.delayScreenshot) {
    // 延迟截图
    setTimeout(() => screenshotCapture(e, capture, sceneConfig), sceneConfig.delayScreenshot)
  } else {
    screenshotCapture(e, capture, sceneConfig)
  }
}

function initCodeGlobalKeyboardManager(router: Router) {
  const keyboardConfigStore = useKeyboardConfigStore()
  const globalKeyboardConfig = keyboardConfigStore.globalKeyboardConfig
  const codeList = Object.keys(globalKeyboardConfig)
  for (const code of codeList) {
    const config = DEFAULT_GLOBAL_KEYBOARD_CONFIG[code]
    if (!config || !config.pluginCode) {
      continue
    }

    window.globalKeyboardManager.addKeyboard(code, {
      ...globalKeyboardConfig[code]
    } as any, () => {
      dispatchUtoolsCodeEvent({
        code: config.pluginCode!,
        payload: '',
        type: 'text',
        option: null
      }, router)
    })
  }
}

export function utoolsCodeInit(router: Router) {
  addEventListener('screenshot.capture', handleScreenshotCapture)
  initCodeGlobalKeyboardManager(router)
}

addEventListener('screenshot.delay.capture', async (e) => {
  const { generalSetting } = useSettingUserStore()
  utools.hideMainWindow()
  utools.outPlugin()
  setTimeout(() => {
    handleScreenshotCapture(e)
  }, generalSetting.delayScreenshot)
})

addEventListener('screenshot.capture.only', () => {
  utools.hideMainWindow()
  utools.setExpendHeight(0)
  screenCapture(DataUtils.getDataSaveLibPath()).then(base64 => {
    utools.copyImage(base64)
  }).finally(() => {
    utools.outPlugin()
  })
})

addEventListener('screenshot.capture.local', () => {
  utools.hideMainWindow()
  utools.setExpendHeight(0)
  try {
    if (!window.localCapture) {
      utools.showNotification('当前系统未支持')
      return
    }
    window.localCapture(DataUtils.getDataSaveLibPath()).then(base64 => {
      console.log('base64', base64)
      if (base64) {
        createScreenShot().then(({ webContentsId }) => {
          ScreenshotApi.sendBase64Screenshot(webContentsId, base64)
        })
      }
    })
  } finally {
    utools.outPlugin();
  }
})


addEventListener('screenshot.base64', (e) => {
  console.log('screenshot.base64', e.detail)
  utools.hideMainWindow()
  utools.setExpendHeight(0);
  const sceneStore = useSceneStore()
  let sceneConfig = null;
  if (e.hasParamKey('sceneCode')) {
    const sceneCode = e.getParamsKey('sceneCode')
    sceneConfig = sceneStore.getSceneConfig(sceneCode)
  }

  createScreenShot().then(({ webContentsId }) => {
    loadSceneConfig(webContentsId, e.pluginEnterParams.payload, sceneConfig);
    ScreenshotApi.sendBase64Screenshot(webContentsId, e.pluginEnterParams.payload)
    utools.outPlugin()
  })
})

addEventListener('screenshot.clipboardReadImage', (e) => {
  utools.hideMainWindow()
  utools.setExpendHeight(0);
  const sceneStore = useSceneStore()
  let sceneConfig = null;
  if (e.hasParamKey('sceneCode')) {
    const sceneCode = e.getParamsKey('sceneCode')
    sceneConfig = sceneStore.getSceneConfig(sceneCode)
  }

  const clipboardImage = window.clipboard.readImage('clipboard');
  let base64 = null;
  if (!clipboardImage.isEmpty()) {
    const fileUrl = window.clipboard.read('public.file-url');
    console.log('fileUrl',  fileUrl)

    if (fileUrl) {
      base64 = window.nativeImage.createFromPath(fileUrl.replace('file://', '')).toDataURL();
    } else {
      base64 = clipboardImage.toDataURL()
    }
  } else if (utools.isWindows()) {
    const filePath = window.clipboard.readBuffer('FileNameW')
      .toString('ucs2')
      .replace(RegExp(String.fromCharCode(0), 'g'), '');
    const nativeImage = window.nativeImage.createFromPath(filePath);
    if (!nativeImage.isEmpty()) {
      base64 = nativeImage.toDataURL();
    }
  }
  if (!base64) {
    window.utools.showNotification("未获取到剪贴板图片");
    utools.outPlugin()
    return;
  }
  createScreenShot().then(({ webContentsId }) => {
    loadSceneConfig(webContentsId, e.pluginEnterParams.payload, sceneConfig);
    ScreenshotApi.sendBase64Screenshot(webContentsId, base64)
    utools.outPlugin()
  })
})

export async function sendScreenshotFileId(id: string) {
  const createScreenShotOptions: CreateScreenShotOptions = {}
  createScreenShot(createScreenShotOptions)
    .then(({ webContentsId }) => {
      ScreenshotApi.setGlobalParams(webContentsId, 'id', id)
      ScreenshotApi.sendScreenshotFileId(webContentsId, id)
    }).catch(e => {
    console.error('screenshotByJson, 出现异常', e)
    createScreenShotOptions.win && createScreenShotOptions.win.close()
  })
}
// 加载场景并将文件加入文件盒子
function loadSceneConfig(webContentsId: number, base64: string, sceneConfig?: ISceneConfig) {
  const fileManagerStore = useFileManagerStore();
  if (sceneConfig) {
    const sceneCode = sceneConfig.sceneCode
    if (fileManagerStore.localFileManagerEnable && sceneConfig.fileBoxEnable) {
      // 将截图保存至文件盒子
      const fileId = ScreenshotDB.createScreenshot({
        base64,
        tags: sceneConfig.tags
      });
      ScreenshotApi.setGlobalParams(webContentsId, 'id', fileId)
    }
    ScreenshotApi.reloadScene(webContentsId, sceneCode);
  }
}
export async function screenshotByFile(filePath: string, sceneConfig?: ISceneConfig, autoSize = false) {
  const createScreenShotOptions: CreateScreenShotOptions = {}
  return Promise.all([filePathToFileUrlData(filePath), createScreenShot(createScreenShotOptions)])
    .then(([base64, { webContentsId }]) => {
      loadSceneConfig(webContentsId, base64, sceneConfig);
      ScreenshotApi.sendBase64Screenshot(webContentsId, base64, { autoSize });
      return webContentsId
    }).catch(e => {
      console.error('screenshot.file, 出现异常', e)
      createScreenShotOptions.win && createScreenShotOptions.win.close()
    })
}

addEventListener('screenshot.file', async (e) => {
  utools.hideMainWindow()
  utools.setExpendHeight(0)
  if (e.pluginEnterParams.payload.length) {
    let sceneConfig = null;
    const sceneStore = useSceneStore()
    if (e.hasParamKey('sceneCode')) {
      const sceneCode = e.getParamsKey('sceneCode')
      sceneConfig = sceneStore.getSceneConfig(sceneCode)
    }
    screenshotByFile(e.pluginEnterParams.payload[0].path, sceneConfig)
      .finally(() => {
        utools.outPlugin()
      });
  }
});

addEventListener('screenshot.autoSizeFile', async (e) => {
  utools.hideMainWindow();
  utools.setExpendHeight(0);
  if (e.pluginEnterParams.payload.length) {
    const sceneStore = useSceneStore()
    let sceneConfig = null;
    if (e.hasParamKey('sceneCode')) {
      const sceneCode = e.getParamsKey('sceneCode')
      sceneConfig = sceneStore.getSceneConfig(sceneCode)
    }
    screenshotByFile(e.pluginEnterParams.payload[0].path, sceneConfig, true)
      .finally(() => {
        utools.outPlugin();
      })
  }
})

addEventListener('ui.router', (e) => {
  console.log('ui.router', e)
  e.router.replace({ name: e.getParamsKey('router'), query: { ...e.params } })
    .then(() => {});
  utools.setExpendHeight(600);
})
addEventListener('screenshot.capture.oneself', async () => {
  utools.setExpendHeight(0)
  const captureDisPlays = await getCaptureDisplays()
  console.log('screenshot.capture.oneself', captureDisPlays)
  utools.hideMainWindow()
  const ans = []
  const captureList = await Promise.all(captureDisPlays.map(item => item.capture()
    .then(buffer => window.nativeImage.createFromBuffer(buffer).toDataURL())))
  for (let i = 0; i < captureDisPlays.length; i++) {
    const captureDisPlay = captureDisPlays[i]
    const res = await createScreenCapture({
      display: captureDisPlay.display,
      captureDisplayId: captureDisPlay.captureDisplayId
    })
      .then(({ webContentsId, win }) => {
        ScreenshotApi.sendBase64Screenshot(webContentsId, captureList[i])
        return { win, webContentsId }
      })
    ans.push(res)
  }
  // const ans = captureDisPlays.map(({capture, display, captureDisplayId}) => {
  //   // console.log('111111111', display.id)
  //     return Promise.all([capture().then(buffer => {
  //       return  "data:image/png;base64," + buffer.toString('base64');
  //     }), createScreenCapture({ display, captureDisplayId })])
  //       .then(([base64, {webContentsId, win}]) => {
  //       screenshotApi.sendBase64Screenshot(webContentsId, base64);
  //       return {
  //         win,
  //         webContentsId
  //       }
  //     });
  // });
  await Promise.all(ans).then((data) => {
    const winIds = data.map((({ win }) => win.id))
    for (const item of data) {
      ScreenshotApi.sendOtherWindowIds(item.webContentsId, item.win.id, winIds)
    }
  }).catch(e => {
    console.log('error', e)
  }).finally(() => {
    utools.outPlugin()
  })
})


// addEventListener('screen.record', async (e) => {
//   createRecordCapture('')
// });
