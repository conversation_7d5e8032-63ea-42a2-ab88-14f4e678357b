import DataUtils from '@/utils/DataUtils'
import fileResourceApi from '@/api/ucloud/fileResourceApi'
import HttpUtils from '@/utils/HttpUtils'
import FileUtils from '@/utils/FileUtils'
import {
  getInstalledLocalOcrModelList,
  type ILocalOcrModelItem
} from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'
import { NotifyPlugin } from 'tdesign-vue-next'

function haveJavaEnvironment() {
  return window.fs.existsSync(window.path.join(DataUtils.getDataSaveLibPath(), 'jre'));
}

async function installJavaEnvironment(platform: 'macArm' | 'windowsX64') {
  const resource = await fileResourceApi.getResource(`jre:${platform}`);
  const filePreview = resource.filePreviewUrl;
  const previewUrl = filePreview.previewUrl;
  const fileName = filePreview.fileName;
  const jreFilePath = window.path.join(DataUtils.getDataSaveLibPath(), fileName);
  await HttpUtils.downloadFile(previewUrl, jreFilePath);
  const jreDir = window.path.join(DataUtils.getDataSaveLibPath(), 'jre');
  await FileUtils.unTar(jreFilePath, jreDir, true);
}

function haveLocalOcrEnvironment() {
  const modelItem = getInstalledLocalOcrModelList()
    .find(item => item.installDir === DataUtils.getOrDefaultDataOcrActiveModel());
  if (!modelItem) {
    return false;
  }
  const modelPath = window.path.join(DataUtils.getDataOcrPath(),
    modelItem.installDir,
    modelItem.executeFile);
  return window.fs.existsSync(modelPath);
}


async function installOcr(model: ILocalOcrModelItem) {
  const resource = await fileResourceApi.getResource(model.resourceCode);
  const filePreview = resource.filePreviewUrl;
  const previewUrl = filePreview.previewUrl;
  const fileName = filePreview.fileName;
  const ocrFilePath = window.path.join(DataUtils.getDataSavePath(), fileName);
  try {
    await HttpUtils.downloadFile(previewUrl, ocrFilePath);
  }catch (e) {
    throw Error("code: 10001, 下载 组件OCR 组件错误")
  }
  const ocrDir = window.path.join(DataUtils.getDataOcrPath(), model.installDir);
  try {
    await FileUtils.unTar(ocrFilePath, ocrDir, true);
  }catch (e) {
    throw Error("code: 10002, 解压 组件OCR 组件错误")
  }
}

/**
 * 安装 OCR 模型
 * @param model
 */
async function installOcrModel(model: ILocalOcrModelItem) {
  // 安装 java 环境
  if (model.runEnv) {
    if (model.runEnv === 'java'
      && !haveJavaEnvironment()) {
      await NotifyPlugin.info({
        title: '下载必要组件',
        content: '下载必要组件环境....',
        duration: 60 * 60 * 1000
      });
    }
    try {
      await installJavaEnvironment(model.runPlatform);
    }catch (e) {
      await NotifyPlugin.warning({
        title: '下载必要组件出现问题',
        content: '下载必要组件 请重试或请联系开发者',
        duration: 3 * 1000,
        closeBtn: true,
      });
    }
  }
  await NotifyPlugin.info({
    title: '下载离线OCR模型',
    content: '正在下载压缩包中...',
    duration: 60 * 60 * 1000
  });
  try {
    await installOcr(model);
  }catch (e: any) {
    await NotifyPlugin.warning({
      content: e.message.startsWith("code") ? e.message : '获取离线OCR模型下载出现问题, 请联系开发者',
      duration: 3 * 1000,
      closeBtn: true,
    });
  }
  await NotifyPlugin.success({
    title: '提示',
    content: '下载离线OCR模型安装完成',
    duration: 3 * 1000,
    closeBtn: true,
  });
}
/**
 * 卸载 Ocr 模型
 */
function uninstallOcrModel(model: ILocalOcrModelItem) {
  const installPath = window.path.join(DataUtils.getDataOcrPath(), model.installDir);
  if (window.fs.existsSync(installPath)) {
    NotifyPlugin.info({
      title: '卸载离线OCR组件',
      content: '卸载离线OCR组件中...',
      duration: 60 * 60 * 1000
    }).then(() => {});
    try {
      window.fs.rmSync(installPath, {
        recursive: true,
        force: true
      });
      NotifyPlugin.success({
        title: '提示',
        content: `卸载离线OCR组件完成`,
        duration: 3 * 1000,
        closeBtn: true,
      }).then(() => {});
    }catch (e) {
      NotifyPlugin.error({
        title: '提示',
        content: `卸载离线OCR组件出现问题`,
        duration: 3 * 1000,
        closeBtn: true,
      }).then(() => {});
    }
  }
}

function haveOpenCvEnvironment() {
  return window.fs.existsSync(DataUtils.getOpenCVPath());
}

async function installOpenCv() {
  await downloadResource( DataUtils.getOpenCVPath(), 'opencv');
}

/**
 * 下载文件
 * @param installPath 安装的全路径
 * @param resourceCode 下载资源 code
 */
async function downloadResource(installPath: string, resourceCode: string) {
  const resource = await fileResourceApi.getResource(resourceCode);
  const filePreview = resource.filePreviewUrl;
  const previewUrl = filePreview.previewUrl;
  const fileName = filePreview.fileName;
  const filePath = window.path.join(DataUtils.getDataSavePath(), fileName);
  try {
    await HttpUtils.downloadFile(previewUrl, filePath);
    await FileUtils.unTar(filePath, installPath, true);
  }catch (e) {
    console.error(`下载文件失败: resourceCode: ${resource}`, e);
  }
}



function isWindowsCaptureLibrary() {
  return window.fs.existsSync(window.path.join(DataUtils.getDataSaveLibPath(), 'capture.exe'));
}

async function installWindowsCaptureLibrary() {
  const resource = await fileResourceApi.getResource('capture:windows');
  const filePreview = resource.filePreviewUrl;
  const previewUrl = filePreview.previewUrl;
  const fileName = filePreview.fileName;
  const filePath = window.path.join(DataUtils.getDataSaveLibPath(), fileName);
  await HttpUtils.downloadFile(previewUrl, filePath);
}

function isQrCode() {
  const onlyWechatWasmFile = window.fs.existsSync(window.path.join(DataUtils.getQrCodePath(), 'onlyWasmFile.data'));
  const wechatQrCodeFile =  window.fs.existsSync(window.path.join(DataUtils.getQrCodePath(), 'QRcodeFile.data'));
  return onlyWechatWasmFile && wechatQrCodeFile;
}

export default {
  haveJavaEnvironment,
  installJavaEnvironment,
  haveLocalOcrEnvironment,
  uninstallOcrModel,
  installOcrModel,
  haveOpenCvEnvironment,
  installOpenCv,
  isWindowsCaptureLibrary,
  installWindowsCaptureLibrary,
  downloadResource,
  isQrCode
}
