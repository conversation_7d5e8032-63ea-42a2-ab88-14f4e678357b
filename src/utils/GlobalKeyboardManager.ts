import { KEYBOARD_UIOHook_MAP } from '@/core/constants/StrConstants'
import type { UiohookKeyboardEvent } from 'uiohook-napi'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'


export interface KeyboardConfig {
  ctrl?: boolean;
  alt?: boolean;
  mode?: 'hold' | 'double' | 'switchover',
  key?: string,
}

export interface GlobalKeyboardHandle {
  code: string;
  handle: () => void;
  keyboardConfig: KeyboardConfig;
}



export default class GlobalKeyboardManager {
  private __map: Map<string, GlobalKeyboardHandle>;
  private __keyMap: Map<string, string>;
  private __keydownEvent?: any;

  constructor() {
    this.__map = new Map();
    this.__keyMap = new Map();
  }

  handleKeydown(e: UiohookKeyboardEvent) {
    const keys: string[] = [];
    const ctrl = utools.isMacOS() ? e.metaKey : e.ctrlKey;
    const alt = e.altKey;
    if (ctrl) {
      const keyword = ctrl ? 'ctrl' : '';
      keys.push(keyword);
    }

    if (alt) {
      const keyword = alt ? 'alt' : '';
      keys.push(keyword);
    }

    keys.push(e.keycode.toString());

    const keyCode = keys.join('|');
    const code = this.__keyMap.get(keyCode);
    if (!code) {
      return;
    }
    const handle = this.__map.get(code);
    if (!handle) {
      return;
    }
    handle.handle();
  }


  /**
   * 添加按键
   * @param code
   * @param keyboardConfig
   * @param handle
   */
  public addKeyboard(code: string, keyboardConfig: KeyboardConfig, handle: () => void) {
    this.removeKeyboard(code);
    const keyCode = this.buildKeyboard(keyboardConfig);
    this.__map.set(code, {
      code,
      keyboardConfig,
      handle,
    });
    this.__keyMap.set(keyCode, code);
  }

  /**
   * 刷新按键
   * @param code 功能 code
   * @param keyboardConfig 快捷键配置
   */
  public refreshKeyboard(code: string, keyboardConfig: KeyboardConfig) {
    const globalKeyboardHandle = this.__map.get(code);
    const keyCode = this.buildKeyboard(globalKeyboardHandle.keyboardConfig);
    this.__keyMap.delete(keyCode);
    this.addKeyboard(code, keyboardConfig, globalKeyboardHandle.handle);
  }


  /**
   * 移除按键
   * @param code
   */
  public removeKeyboard(code: string) {
    if (this.__map.has(code)) {
      const global = this.__map.get(code);
      this.__map.delete(code);
      if (global) {
        const keyCode = this.buildKeyboard(global.keyboardConfig);
        this.__keyMap.delete(keyCode);
      }
    }
  }

  private enable() {
    if (window.uiohook) {
      window.uiohook.uIOhook.start();
      this.__keydownEvent = this.handleKeydown.bind(this);
      window.uiohook.uIOhook.on('keydown', this.__keydownEvent);
    }
  }

  public disable() {
    if (window.uiohook) {
      window.uiohook.uIOhook.stop();
      if (this.__keydownEvent) {
        window.uiohook.uIOhook.off('keydown', this.__keydownEvent);
        this.__keydownEvent = undefined;
      }
    }

  }

  public autoGlobalKeyboard(val?: boolean) {
    if (val === undefined) {
      const keyboardConfigStore = useKeyboardConfigStore()
      val = keyboardConfigStore.globalKeyboardEnable;
    }
    if (val) {
      this.enable();
    } else {
      this.disable();
    }
  }

  private buildKeyboard(keyboardConfig: KeyboardConfig) {
    if (keyboardConfig.key) {
      const keys: string[] = [];
      const keyword = keyboardConfig.ctrl ? 'ctrl' : '';
      if (keyword) {
        keys.push(keyword)
      }

      if (keyboardConfig.alt) {
        keys.push('alt');
      }

      keys.push(KEYBOARD_UIOHook_MAP[keyboardConfig.key]);
      return keys.join('|');
    }
    return  '';
  }

}
