import type { BrowserWindow, Display } from 'electron'
import { useSettingUserStore } from '@/stores/SettingUserStore'

export interface CreateScreenShotOptions {
  win?: any,
  display?: Display;
  captureDisplayId?: number;
}

type CaptureDisplay = { display: Display, capture: () => Promise<Buffer>, captureDisplayId: number};
export async function getCaptureDisplays(): Promise<CaptureDisplay[]> {
  const allDisplays = utools.getAllDisplays() as Display[];
  const screenshots = await window.screenshot.listDisplays();
  const displays: CaptureDisplay[] = [];
  for (let i = 0; i < allDisplays.length; i++) {
    displays.push({
      display: allDisplays[i],
      captureDisplayId: screenshots[i].id,
      capture: async () => {
        return window.screenshot({
          screen: screenshots[i].id,
          format: 'png'
        });
      }
    })
  }
  return displays;
}


export function createScreenShot(options?: CreateScreenShotOptions): Promise<{webContentsId: number, win: BrowserWindow}> {
  const {generalSetting: { systemHasShadow }} = useSettingUserStore()
  return new Promise((resolve, reject) => {
    try {
      const win: any = utools.createBrowserWindow('./dist/screenshotContainer.html', {
        title: '截图底座',
        width: 0,
        height: 0,
        hasShadow: systemHasShadow,
        minimizable: false,
        // 背景透明，防止放大缩小时出现白框
        transparent: true,
        backgroundColor: '#00000000',
        frame: false,
        alwaysOnTop: false,
        // 是否在任务栏中显示窗口
        skipTaskbar: true,
        // 当用户切换到任务控制时是否应隐藏窗口
        hiddenInMissionControl: false,
        show: false,
        enableLargerThanScreen: true,
        type: 'panel',
        // thickFrame: false,
        webPreferences: {
          preload: './src/screenshot/preload.js',
          devTools: true,
          backgroundThrottling: false
        }
      } as any, () => {
        try {
          if (options) {
            options.win = win
          }
          // fix: 移动窗口到指定位置, 解决显示器缩放比例问题
          const display = utools.getDisplayNearestPoint(utools.getCursorScreenPoint())
          const displayBounds = display.bounds
          win.setPosition(displayBounds.x, displayBounds.y);

          win.setResizable(false);
          win.setAlwaysOnTop(true, 'modal-panel');
          win.webContents.openDevTools({ mode: 'detach' });
          // win.executeJavaScript("window.location.href='http://localhost:5173/screenshotContainer'");
          const webContentsId = win.webContents.id;
          window.ipcRenderer.sendTo(webContentsId, 'init', { winId: win.id });
          resolve({webContentsId, win: win as any });
        }catch (e) {
          reject(e)
        }
      });
    }catch (e) {
      reject(e)
    }
  })
}

export function createScreenCapture(options?: CreateScreenShotOptions): Promise<{webContentsId: number, win: BrowserWindow}> {
  return new Promise((resolve, reject) => {
    try {
      const win: any = utools.createBrowserWindow('./dist/captureContainer.html', {
        title: '截图',
        width: 100,
        height: 100,
        minimizable: false,
        // 背景透明，防止放大缩小时出现白框
        transparent: true,
        backgroundColor: '#00000000',
        frame: false,
        alwaysOnTop: false,
        // 是否在任务栏中显示窗口
        skipTaskbar: true,
        // 当用户切换到任务控制时是否应隐藏窗口
        hiddenInMissionControl: false,
        show: false,
        webPreferences: {
          preload: './src/capture/preload.js',
          devTools: true,
          backgroundThrottling: false
        }
      } as any, () => {
        if (options) {
          options.win = win
        }

        win.setResizable(false);
        win.setAlwaysOnTop(true, 'screen-saver');
        win.webContents.openDevTools({ mode: 'detach' });
        if (options && options.display) {
          const bounds = options?.display.bounds!;
          win.setPosition(bounds.x, bounds.y)
        }
        const webContentsId = win.webContents.id;
        const initParams: Record<string, any> = {};
        if (options?.captureDisplayId) {
          initParams.captureDisplayId = options.captureDisplayId;
        }
        window.ipcRenderer.sendTo(webContentsId, 'init', { winId: win.id, ...initParams });
        resolve({ webContentsId, win: win as any});
      });
    }catch (e) {
      reject(e)
    }
  })
}


export function createRecordCapture(options?: CreateScreenShotOptions): Promise<{webContentsId: number, win: BrowserWindow}> {
  const  display = window.utools.getDisplayNearestPoint(window.utools.getCursorScreenPoint());
  return new Promise((resolve, reject) => {
    try {
      const win: any = utools.createBrowserWindow('./dist/recordContainer.html', {
        ...display.bounds,
        title: '截图',
        minimizable: false,
        // 背景透明，防止放大缩小时出现白框
        transparent: true,
        backgroundColor: '#00000000',
        frame: false,
        alwaysOnTop: false,
        // 是否在任务栏中显示窗口
        skipTaskbar: true,
        // 当用户切换到任务控制时是否应隐藏窗口
        hiddenInMissionControl: false,
        show: false,
        webPreferences: {
          preload: './src/capture/preload.js',
          devTools: true,
          backgroundThrottling: false
        }
      } as any, () => {
        if (options) {
          options.win = win
        }

        win.setResizable(false);
        win.setAlwaysOnTop(true, 'screen-saver');
        win.webContents.openDevTools();
        if (options && options.display) {
          const bounds = options?.display.bounds!;
          win.setPosition(bounds.x, bounds.y)
        }
        const webContentsId = win.webContents.id;
        const initParams: Record<string, any> = {};
        if (options?.captureDisplayId) {
          initParams.captureDisplayId = options.captureDisplayId;
        }
        window.ipcRenderer.sendTo(webContentsId, 'init', { winId: win.id, ...initParams });
        resolve({ webContentsId, win: win as any});
        win.show();
      });
    }catch (e) {
      reject(e)
    }
  })
}

export function createSuspendContainer(options?: CreateScreenShotOptions): Promise<{webContentsId: number, win: BrowserWindow}> {
  const  display = window.utools.getDisplayNearestPoint(window.utools.getCursorScreenPoint());
  return new Promise((resolve, reject) => {
    try {
      const win: any = utools.createBrowserWindow('./dist/suspendContainer.html', {
        ...display.bounds,
        title: '截图',
        minimizable: false,
        // 背景透明，防止放大缩小时出现白框
        transparent: true,
        backgroundColor: '#00000000',
        frame: false,
        alwaysOnTop: false,
        // 是否在任务栏中显示窗口
        skipTaskbar: true,
        // 当用户切换到任务控制时是否应隐藏窗口
        hiddenInMissionControl: false,
        show: false,
        webPreferences: {
          preload: './src/suspend/preload.js',
          devTools: true,
          backgroundThrottling: false
        }
      } as any, () => {
        if (options) {
          options.win = win
        }

        win.setResizable(false);
        win.setAlwaysOnTop(true, 'screen-saver');
        win.webContents.openDevTools();
        if (options && options.display) {
          const bounds = options?.display.bounds!;
          win.setPosition(bounds.x, bounds.y)
        }
        const webContentsId = win.webContents.id;
        const initParams: Record<string, any> = {};
        if (options?.captureDisplayId) {
          initParams.captureDisplayId = options.captureDisplayId;
        }
        window.ipcRenderer.sendTo(webContentsId, 'init', { winId: win.id, ...initParams });
        resolve({ webContentsId, win: win as any});
        win.show();
      });
    }catch (e) {
      reject(e)
    }
  })
}
