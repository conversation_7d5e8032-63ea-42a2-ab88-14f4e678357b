
const typeMap: Record<string, string> = {
  '[object Number]': 'number',
  '[object Boolean]': 'boolean',
  '[object String]': 'string',
  '[object Array]': 'array',
  '[object Object]': 'object',
  '[object Function]': 'function',
  '[object Undefined]': 'undefined',
  '[object Null]': 'null',
}

function isType(value: any, type: string) {
  const toString = Object.prototype.toString;
  return typeMap[toString.call(value)] === type;
}

function findFatherElementId(dom: Element, id: string): Element | null {
  if (!dom) {
    return null;
  }
  if (dom.id === id) {
    return dom;
  }
  return findFatherElementId(dom.parentElement, id)
}


export function isObject(value: any): value is object {
  return isType(value, 'object');
}

export function isArray <T>(value: any[T]): value is Array<T> {
  return isType(value, 'array');
}

export function isString(value: any): value is string {
  return isType(value, 'string');
}

export function filePathToFileUrlData(path: string): Promise<string> {
  return new Promise((resolve, reject) => {
    window.fs.readFile(path, function (err, data) {
      if (err) {
        reject(err);
      } else {
        const nativeImage = window.nativeImage.createFromBuffer(data);
        if (!nativeImage.isEmpty()) {
          resolve(nativeImage.toDataURL());
        } else {
          reject('图片格式不支持');
        }
      }
    });
  })
}

/**
 * 动态加载脚本
 * @param url 脚本路径
 * @param callback onload 回调
 */
function loadScript(url: string, callback?: () => void) {
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.src = url;
  script.async = true;

  script.onload = function () {
    if (callback) {
      callback();
    }
  };

  document.head.appendChild(script);
}

/**
 * 是否是明亮图
 * @param base64Image
 */
function isBrightImage(base64Image: string) {
  return new Promise((resolve, reject) => {
    // 创建一个新的图像对象
    let img = new Image();
    img.src = base64Image;

    img.onload = () => {
      // 创建一个 Canvas 元素
      let canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // 设置 Canvas 尺寸
      canvas.width = img.width;
      canvas.height = img.height;

      // 将图像绘制到 Canvas 上
      ctx.drawImage(img, 0, 0);

      // 获取图像的像素数据
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      let r, g, b, avg;
      let totalBrightness = 0;
      const pixelCount = data.length / 4; // 每个像素有 RGBA 四个值

      // 计算平均亮度
      for (let i = 0; i < data.length; i += 4) {
        r = data[i];     // 红色通道
        g = data[i + 1]; // 绿色通道
        b = data[i + 2]; // 蓝色通道

        // 使用加权平均计算亮度
        avg = (r * 0.299 + g * 0.587 + b * 0.114);
        totalBrightness += avg;
      }

      const averageBrightness = totalBrightness / pixelCount;

      // 判断明亮或暗黑
      canvas.remove();
      img.remove();
      canvas = null;
      img = null;
      resolve(averageBrightness > 128); // 128 是亮度的阈值
    };

    img.onerror = (error) => {
      reject(error);
    };
  });
}

function camelToSnake(camelCase: string) {
  return camelCase
    .replace(/([A-Z])/g, '_$1') // 在大写字母前加上下划线
    .toLowerCase(); // 转换为小写字母
}

function snakeToCamel(snakeCase: string) {
  return snakeCase
    .toLowerCase() // 将字符串转为小写
    .replace(/_./g, match => match.charAt(1).toUpperCase()); // 替换下划线及其后面的字符
}

/**
 * 随机数
 * @param min 最小值
 * @param max 最大值
 */
function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
export default {
  isArray,
  isObject,
  filePathToFileUrlData,
  loadScript,
  isBrightImage,
  camelToSnake,
  snakeToCamel,
  getRandomInt,
  findFatherElementId
}
