/**
 * 下载网络文件
 * @param url url
 * @param saveAsPath 保存地址
 * @param options
 * @returns {Promise<string>}
 */
function downloadFile(url: string, saveAsPath: string, options: Record<string, any> = {}): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (!window.fs.existsSync(window.path.dirname(saveAsPath))) {
        window.fs.mkdirSync(window.path.dirname(saveAsPath), { recursive: true });
      }
    } catch (e) {
      reject(e);
    }
    const fileWrite = window.fs.createWriteStream(saveAsPath);

    const request = url.startsWith('https') ? window.https : window.http;
    request
      .get(url, options, (resp) => {
        resp.pipe(fileWrite);
        fileWrite.on('finish', () => {
          fileWrite.close();
          resolve(url);
        });
      })
      .on('error', (err) => {
        reject(err);
      });
  });
}

/**
 * 判断端口是否被占用
 * @param port 端口号
 * @returns 该端口是否被占用
 */
function isUsePort(port: number): Promise<boolean> {
  let command: string;
  let args: string[];

  if (utools.isWindows()) {
    command = 'netstat';
    args = ['-ano', '|', 'findstr', `:${port}`];
  } else {
    command = 'lsof';
    args = ['-i', `:${port}`];
  }

  return new Promise((resolve, reject) => {
    const child = window.spawn(command, args);
    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data: Buffer) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data: Buffer) => {
      stderr += data.toString();
    });

    child.on('close', (code: number) => {
      if (code !== 0) {
        resolve(false);
        return;
      }

      if (stdout === "") {
        resolve(false); // 端口未被占用
      } else {
        resolve(true); // 端口被占用
      }
    });

    child.on('error', (err: Error) => {
      resolve(false); // 端口被占用
    });
  });
}


export default {
  downloadFile,
  isUsePort
}
