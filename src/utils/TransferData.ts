import DataUtils from '@/utils/DataUtils'

// 迁移数据
/**
 * 1.1.11 -> 1.1.12
 */
function transferOcrData() {
  if (window.fs.existsSync(window.path.join(DataUtils.getDataOcrPath(), 'version'))) {
    const paddleOcrDir = window.path.join(DataUtils.getDataSavePath(), 'paddleOcr');
    window.fs.renameSync(DataUtils.getDataOcrPath(), paddleOcrDir);
    window.fs.renameSync(paddleOcrDir, window.path.join(DataUtils.getDataOcrPath(), 'paddleOcr'));
  }
}

export default {
  transferOcrData
}
