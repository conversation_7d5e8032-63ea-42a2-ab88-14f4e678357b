/**
 * 创建不存在目录
 * @param dir
 */
function createDirIfAbsent(dir: string) {
  if (!window.fs.existsSync(dir)) {
    window.fs.mkdirSync(dir, { recursive: true });
  }
}

function unTar(filePath: string, outputDir: string, deleteOriginalFile = false) {
  return new Promise((resolve, reject) => {
    console.log('unTar', filePath, outputDir)
    try {
      createDirIfAbsent(outputDir);
    } catch (e) {
      console.error('创建目录失败--', e);
      reject('创建目录失败');
    }
    window.zip.unzipSync(filePath, outputDir);
    if (deleteOriginalFile) {
      window.fs.unlinkSync(filePath);
    }
    resolve(true);
  });
}

export default {
  unTar,
  createDirIfAbsent
}
