import FileUtils from '@/utils/FileUtils'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { getInstalledLocalOcrModelList } from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'

function getDataSavePath() {
  return  utools.dbStorage.getItem(`settingUserStore/localAppData/${utools.getNativeId()}`)
    || window.path.join(utools.getPath('userData'), 'screenshot');
}

/**
 * 截图库
 */
function getDataScreenshotLibrary() {
  return window.path.join(getDataSavePath(), 'library')
}

/**
 * ocr 路径
 */
function getDataOcrPath() {
  const ocrPath = window.path.join(getDataSavePath(), 'ocr');
  FileUtils.createDirIfAbsent(ocrPath);
  return ocrPath;
}

/**
 * 获取当前激活模型
 */
function getDataOcrActiveModel(): string | undefined {
  const enableFile = window.path.join(getDataOcrPath(), 'enable');
  if (!window.fs.existsSync(enableFile)) {
    return undefined;
  }
  const enableValue  = window.fs.readFileSync(enableFile, 'utf-8');
  const activeItem =  getInstalledLocalOcrModelList()
    .find(item => item.installDir === enableValue);
  console.log('activeItem', activeItem)
  if (activeItem) {
    return activeItem.installDir;
  } else {
    window.fs.writeFileSync(enableFile, '', 'utf-8');
    return undefined;
  }
}

/**
 * 获取当前激活模型, 如果没有会将第一个模型自动设置为激活模型
 */
function getOrDefaultDataOcrActiveModel() {
  const res  = getDataOcrActiveModel();
  if (res) {
    return res;
  }
  const installedList = getInstalledLocalOcrModelList();
  if (installedList.length) {
    const enableFile = window.path.join(getDataOcrPath(), 'enable');
    window.fs.writeFileSync(enableFile, installedList[0].installDir , 'utf-8');
    return installedList[0].installDir;
  }
  return undefined;
}

function getDataAttachmentPath() {
  const diagramPath = window.path.join(getDataSavePath(), 'attachment');
  FileUtils.createDirIfAbsent(diagramPath);
  return diagramPath;
}

function getDataExpressionPackagePath() {
  const expressionPackagePath = window.path.join(getDataAttachmentPath(),  'expression', 'package');
  FileUtils.createDirIfAbsent(expressionPackagePath);
  return expressionPackagePath;
}


/**
 * 获取插件临时文件保存目录
 */
function getDataSaveTempPath() {
  const tempPath = window.path.join(getDataSavePath(), 'temps');
  FileUtils.createDirIfAbsent(tempPath);
  return tempPath;
}

function getOpenCVPath() {
  return window.path.join(getDataSaveLibPath(), 'opencv');
}

function getSqlitePath() {
  return window.path.join(getDataSaveLibPath(), 'sqlite');
}


function autoInstallLib() {
  const savePath = window.path.join(getDataSavePath(), 'lib', 'opencv.js');
  const existsSync = window.fs.existsSync(savePath);
  if (existsSync) {
    window.fs.unlinkSync(savePath);
  }
  const settingUserStore = useSettingUserStore();
  const enhanceScreenshot = settingUserStore.generalSetting.enhanceScreenshot;
  if (enhanceScreenshot && !EnvironmentUtils.haveOpenCvEnvironment()) {
    EnvironmentUtils.installOpenCv().then(() => {});
  }
  if (utools.isWindows() && settingUserStore.scScreenshotWay === 'local'
    && !EnvironmentUtils.isWindowsCaptureLibrary()) {
    EnvironmentUtils.installWindowsCaptureLibrary().then(() => {});
  }
}

function getDataSaveLibPath() {
  const tempPath = window.path.join(getDataSavePath(), 'lib');
  FileUtils.createDirIfAbsent(tempPath);
  return tempPath;
}

function getQrCodePath() {
  return window.path.join(getDataSaveLibPath(), 'qrcode');
}

export default {
  getDataSavePath,
  getDataSaveTempPath,
  getDataSaveLibPath,
  getOpenCVPath,
  getSqlitePath,
  autoInstallLib,
  getDataAttachmentPath,
  getDataExpressionPackagePath,
  getDataOcrPath,
  getDataOcrActiveModel,
  getOrDefaultDataOcrActiveModel,
  getDataScreenshotLibrary,
  getQrCodePath
}
