// @ts-ignore
import { getImgQRCodeInfo } from 'wechat-qrcode-ocr-wasm'
import DataUtils from "@/utils/DataUtils";

function getQrCode(url: string) { // Promise<response>
  const onlyWasmFile = window.path.join(DataUtils.getQrCodePath(), 'onlyWasmFile.data');
  const qrcodeFile = window.path.join(DataUtils.getQrCodePath(), 'QRcodeFile.data')
  return getImgQRCodeInfo({
    wasmBinaryFile: "file://" + onlyWasmFile,
    wechatQRcodeFile: "file://" + qrcodeFile,
    url, // image url or base64
    loadStatus: ({ loaded, total }: any) => {
      console.log(`Downloading data...[${loaded}/${total}]`);
    },
  });
}
export default {
  getQrCode
};
