import { type App as VueApp, createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useTheme } from '@/hooks/useTheme'
import { dispatchUtoolsCodeEvent } from '@/utils/utoolsCodeProcessor'
import { initMainApiProvider } from '@/core/sdk/mainApiProvider'
import './utils/utoolsCodeProcessor'
import { utoolsCodeInit } from './utils/utoolsCodeProcessor'
import GlobalKeyboardManager from '@/utils/GlobalKeyboardManager'
import ColorPickers from 'vue3-colorpicker'
import 'vue3-colorpicker/style.css'
// // @ts-ignore
import LocalOcrService from '@/leaferApp/ocr/LocalOcrService'
import DataUtils from '@/utils/DataUtils'
import PlugInUtils from '@/core/utils/PlugInUtils'
import TransferData from '@/utils/TransferData'
import { useSceneStore } from '@/stores/SceneStore/SceneStore'
import SqliteUtils from '@/utils/SqliteUtils'
import { useFileManagerStore } from '@/stores/FileManager/FileManagerStore'
import './assets/main.less'
(() => {
  import('virtual:uno.css')
})()

TransferData.transferOcrData();
window.globalKeyboardManager = new GlobalKeyboardManager();
window.localOcrService = new LocalOcrService();

window.plugInUtils = new PlugInUtils({
  plugName: '截图工具 Plus'
});

window.api = {};
window.tempData = {};

function initApp() {
  const app = createApp(App)
  app.use(createPinia())
  app.use(router)
  app.use(ColorPickers)
  return app;
}

useTheme({
  setDarkTheme: () => {
    document.body.setAttribute('arco-theme', 'dark')
  },
  setDefaultTheme: () => {
    document.body.removeAttribute('arco-theme');
  }
});

window.db = new SqliteUtils(window.path.join(DataUtils.getDataSavePath(), 'data.db'), (res) => {
  if (!res) {
    const fileManagerStore = useFileManagerStore();
    fileManagerStore.localFileManagerEnable = false;
  }
});
const app: VueApp<Element> = initApp();
let isRender = false;


if (window.utools) {
  // 进入页面注册或登录
  utoolsCodeInit(router);
}
// initApp().mount('#app');
// router.replace({name: 'userSetting'})
// console.log(utools.onPluginEnter)
// utools.onPluginEnter((data) => {
//   console.log('data', data)
// })
window.globalKeyboardManager.autoGlobalKeyboard();
if (window.utools) {
  initMainApiProvider();
  utools.onPluginEnter((data) => {
    console.log('onPluginEnter', data)
    // initApp().mount('#app');
    if (utools.dbStorage.getItem('plug-in-unique')) {
      if (data.code.startsWith('ui') && !isRender) {
        app.mount('#app');
        isRender = true;
      }
      dispatchUtoolsCodeEvent(data, router);
    } else {
      app.mount('#app');
      isRender = true;
      // @ts-ignore
      utools.dbStorage.setItem('plug-in-unique', true);
      router.replace({name: 'FAQ', query: { new: true }}).then(() => {
      });
    }
    DataUtils.autoInstallLib();
  });
}
const sceneStore = useSceneStore();
const codeList = window.utools.getFeatures().map(item => item.code);
const commandPrefix = 'screenshot.capture';
for (const scene of sceneStore.sceneConfigList) {
  const code = `${commandPrefix}?sceneCode=${scene.sceneCode}`
  if (scene.system) {
    continue;
  }
  if (scene.enable) {
    // 只有用户定义的关键字才会进入同步, 系统关键字不进行同步
    if (!codeList.includes(code)) {
      window.utools.setFeature({
        code,
        explain: `${scene.title}-${scene.desc}`,
        cmds: [scene.plugInCode]
      });
    }
  } else {
    if (codeList.includes(code)) {
      window.utools.removeFeature(code);
    }
  }
}
// window.uiohook.uIOhook.on('keydown', (e) => {
//   console.log(e);
// });
// window.uiohook.uIOhook.start();
