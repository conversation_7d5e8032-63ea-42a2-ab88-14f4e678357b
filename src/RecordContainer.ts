import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ColorPickers from 'vue3-colorpicker'
import KeyboardManager from '@/utils/KeyboardManager'
import 'vue3-colorpicker/style.css'
import './assets/screenshotContainer.less'
import utoolsImage from '@/directives/utoolsImage'
import RecordContainer from '@/RecordContainer.vue'

// 初始化 API 容器
window.api = {};
window.tempData = {};
window.globalParams = {};
window.keyboardManager = new KeyboardManager();


const app = createApp(RecordContainer)

app.use(createPinia())
app.use(ColorPickers)
  .use(utoolsImage);
app.mount('#app');

