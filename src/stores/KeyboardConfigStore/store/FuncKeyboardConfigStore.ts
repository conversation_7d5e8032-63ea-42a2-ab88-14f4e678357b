import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { computed } from 'vue'
import type { ToolBoxKeyboardConfig } from '@/stores/SettingUserStore'
import { DEFAULT_FUNC_KEYBOARD_CONFIG } from '@/core/constants/KeyboardsConstants'


const defaultFuncKeyboardConfig: Record<string, ToolBoxKeyboardConfig> = {};


export default () => {
  const funcKeyboardConfig = useUtoolsDbStorage('keyboardConfigStore/FuncKeyboardConfig', {
    ...defaultFuncKeyboardConfig
  });
  const getFuncKeyboardConfig = computed(() => {
    return (code: string) => funcKeyboardConfig.value[code];
  });

  function addFuncKeyboardConfig(config: ToolBoxKeyboardConfig) {
    if (funcKeyboardConfig.value[config.code]) {
      const oldValue = funcKeyboardConfig.value[config.code];
      funcKeyboardConfig.value[config.code] = {...oldValue, ...config};
    } else {
      funcKeyboardConfig.value[config.code] = config;
    }
    console.log('addFuncKeyboardConfig', funcKeyboardConfig.value[config.code]);
    return funcKeyboardConfig.value[config.code];
  }

  function getDefaultFuncKeyboardConfig(code: string) {
    const config = getFuncKeyboardConfig.value(code);
    if (config) {
      return config
    }
    return DEFAULT_FUNC_KEYBOARD_CONFIG[code].keyboard;
  }
  return {
    funcKeyboardConfig,
    getFuncKeyboardConfig,
    addFuncKeyboardConfig,
    getDefaultFuncKeyboardConfig,
  }
}
