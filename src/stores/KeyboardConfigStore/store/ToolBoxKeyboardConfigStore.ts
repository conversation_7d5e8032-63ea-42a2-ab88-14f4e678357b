import type { ToolBoxKeyboardConfig } from '@/stores/SettingUserStore'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'

export const defaultToolBoxKeyboardConfig: Record<string, ToolBoxKeyboardConfig> = {};

export default () => {
  const toolBoxKeyboardConfig = useUtoolsDbStorage('keyboardConfigStore/ToolBoxKeyboardConfig',
    {...defaultToolBoxKeyboardConfig});

  function addToolBoxKeyboardConfig(config: ToolBoxKeyboardConfig) {
    if (toolBoxKeyboardConfig.value[config.code]) {
      const oldValue = toolBoxKeyboardConfig.value[config.code];
      toolBoxKeyboardConfig.value[config.code] = {...oldValue, ...config};
    } else {
      toolBoxKeyboardConfig.value[config.code] = config;
    }
    return toolBoxKeyboardConfig.value[config.code];
  }

  return {
    toolBoxKeyboardConfig,
    addToolBoxKeyboardConfig
  }
}
