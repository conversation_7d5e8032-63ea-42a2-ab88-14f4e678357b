import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { computed, ref } from 'vue'


export interface IFunMouseConfigItem {
  funCode: string,
  desc: string;
  enable: boolean,
  tipsEnable: boolean,
}

export interface IFunMouseSaveConfigItem {
  funCode: string,
  enable: boolean,
  tipsEnable: boolean,
}


export enum FunMouseFunCode {
  /**
   * 双击关闭
   */
  COPY_LOSE= 'doubleClick:copyAndClose'
}

const defaultFunMouseConfigItems: IFunMouseConfigItem[] = [
  {
    funCode: 'doubleClick:copyAndClose',
    desc: '双击关闭并复制截图',
    enable: false,
    tipsEnable: false,
  }
]
export default () => {
  const funMouseConfigItems = ref(defaultFunMouseConfigItems);
  const funMouseSaveConfigItems = useUtoolsDbStorage<IFunMouseSaveConfigItem[]>(
    'keyboardConfigStore/funMouseConfigStore', []);

  const  getFunMouseConfig = computed(() => (funCode: string): IFunMouseConfigItem =>  {
    const configSaveItem = funMouseSaveConfigItems.value.find(item => item.funCode == funCode) || {};
    const configItem = funMouseConfigItems.value.find(item => item.funCode === funCode);
    return {
      ...configItem,
      ...configSaveItem
    }
  });

  function setFunMouseConfig(funCode: string, field: 'enable' | 'tipsEnable', value: any) {
    const index = funMouseSaveConfigItems.value.findIndex(item => item.funCode == funCode);
    if (index !== -1) {
      funMouseSaveConfigItems.value.splice(index, 1, { ...funMouseSaveConfigItems.value[index], [field]: value})
    } else {
      funMouseSaveConfigItems.value.push({ funCode, [field]: value } as any);
    }
  }

  return {
    funMouseConfigItems,
    getFunMouseConfig,
    setFunMouseConfig
  }
}
