import type { ToolBoxKeyboardConfig } from '@/stores/SettingUserStore'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { computed, type Ref, watch } from 'vue'
import type { FuncKeyBoardConfig } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'


export interface GlobalKeyBoardConfig extends FuncKeyBoardConfig {
  /**
   * 插件 code
   */
  pluginCode?: string;
}

const defaultGlobalKeyboardConfig: Record<string, ToolBoxKeyboardConfig> = {};


export default () => {
  const globalKeyboardConfig = useUtoolsDbStorage('keyboardConfigStore/GlobalKeyboardConfig', {
    ...defaultGlobalKeyboardConfig
  });
  const globalKeyboardEnable: Ref<boolean> = useUtoolsDbStorage('keyboardConfigStore/GlobalKeyboardEnable', false);

  const getGlobalKeyboardConfig = computed(() => {
    return (code: string) => globalKeyboardConfig.value[code];
  });

  function addGlobalKeyboardConfig(config: ToolBoxKeyboardConfig) {
    if (globalKeyboardConfig.value[config.code]) {
      const oldValue = globalKeyboardConfig.value[config.code];
      globalKeyboardConfig.value[config.code] = {...oldValue, ...config};
    } else {
      globalKeyboardConfig.value[config.code] = config;
    }
    return globalKeyboardConfig.value[config.code];
  }

  watch(() => globalKeyboardEnable.value, (value) => {
    console.log('autoGlobalKeyboard', value);
    window.globalKeyboardManager.autoGlobalKeyboard(value);
  })

  return {
    globalKeyboardConfig,
    getGlobalKeyboardConfig,
    addGlobalKeyboardConfig,
    globalKeyboardEnable,
  }
}
