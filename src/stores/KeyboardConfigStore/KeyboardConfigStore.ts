import { defineStore } from 'pinia'
import type { KeyboardConfig } from '@/utils/KeyboardManager'
import ToolBoxKeyboardConfigStore from './store/ToolBoxKeyboardConfigStore'
import FuncKeyboardConfigStore from './store/FuncKeyboardConfigStore'
import GlobalKeyboardConfigStore from '@/stores/KeyboardConfigStore/store/GlobalKeyboardConfigStore'
import FunMouseConfigStore from '@/stores/KeyboardConfigStore/store/FunMouseConfigStore'


export interface FuncKeyBoardConfig {
  code: string;
  title: string;
  keyboard: KeyboardConfig;
}


export const useKeyboardConfigStore = defineStore('keyboardConfigStore', () => {
  const toolBoxKeyboardConfig = ToolBoxKeyboardConfigStore();
  return {
    ...FunMouseConfigStore(),
    ...GlobalKeyboardConfigStore(),
    ...FuncKeyboardConfigStore(),
    ...toolBoxKeyboardConfig,
  };
});
