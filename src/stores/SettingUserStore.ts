import { defineStore } from 'pinia'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import type { KeyboardConfig } from '@/utils/KeyboardManager'

export interface GeneralUserSetting {
  performance: boolean;
  globalDefaultTool: string;
  toolbarShowMode: 'show' | 'hide';
  enhanceScreenshot: boolean;
  screenshotLocation: 'center' | 'mouse';
  systemHasShadow: boolean;
  toolTheme: 'utools' | 'imageColor';
  /**
   * 截图边框
   */
  screenshotBorder: 'color' | 'shadow' | 'none',
  /**
   * 自定义截图边框颜色
   */
  screenshotBorderColor: string;

  /**
   * 截图mini模式边框
   */
  screenshotMiniBorder: 'color' | 'none';

  /**
   * 截图mini模式边框颜色
   */
  screenshotMiniBorderColor: string;

  /**
   * 工具条边框
   */
  toolBorder: 'color' | 'none';

  /**
   * 工具条边框颜色
   */
  toolBorderColor: string;

  /**
   * 工具条大小
   */
  toolbarSize: 'default' | 'medium' | 'large';

  /**
   * 工具选中边框
   */
  toolSelectedBorderColor: string;
  /**
   * 工具边框大小
   */
  toolSelectedBorderSize: number;

  /**
   * 工具气泡延迟时间
   */
  toolTooltipDelayTime: number;
  /**
   * dsc 延迟
   */
  delayScreenshot: number;
}

export const defaultGeneralUserSetting: GeneralUserSetting = {
  performance: true,
  globalDefaultTool: 'screenshotDrag',
  toolbarShowMode: 'show',
  enhanceScreenshot: false,
  screenshotLocation: 'center',
  toolTheme: 'utools',
  screenshotBorder: 'color',
  screenshotBorderColor: 'rgb(52, 152, 219)',
  screenshotMiniBorder: 'color',
  screenshotMiniBorderColor: 'rgb(52, 152, 219)',
  toolBorder: 'none',
  toolBorderColor: 'rgb(52, 152, 219)',
  toolSelectedBorderColor: 'rgb(131, 109, 255)',
  toolTooltipDelayTime: 1000,
  toolbarSize: 'default',
  toolSelectedBorderSize: 2,
  delayScreenshot: 3000,
  systemHasShadow: true,
}

export interface ToolBoxKeyboardConfig extends KeyboardConfig {
  /**
   * 按键对应的功能 code
   */
  code: string;
}

export type IDragMode = 'full' | 'compatibility' | 'NONE';


export const useSettingUserStore = defineStore('settingUserStore', () => {

  const generalSetting = useUtoolsDbStorage('settingUserStore/generalSetting',
    {...defaultGeneralUserSetting});

  const mouseWheelZoom = useUtoolsDbStorage<boolean>(`mouseWheelZoom/${utools.getNativeId()}`, false);

  const oldDragSettingValue = window.utools.dbStorage.getItem(`enhanceDrag/${utools.getNativeId()}`);
  let initDragMode: IDragMode = 'full';
  if (oldDragSettingValue !== undefined) {
    console.log('oldDragSettingValue', oldDragSettingValue)
    initDragMode = oldDragSettingValue ? oldDragSettingValue : 'full';
    window.utools.dbStorage.removeItem(`enhanceDrag/${utools.getNativeId()}`);
    if (window.utools.isLinux()) {
      initDragMode = initDragMode === 'full' ? 'compatibility' : initDragMode;
    }
  }
  const dragMode = useUtoolsDbStorage<IDragMode>(`dragMode/${utools.getNativeId()}`, initDragMode);

  const scScreenshotWay = useUtoolsDbStorage<'utools' | 'local' | 'custom'>('settingUserStore/scScreenshotWay',
    'utools', {
    platform: true
  });

  const scScreenshotCustomExecute = useUtoolsDbStorage<string>('settingUserStore/scScreenshotWay/custom',
    '', {
      platform: true
    });
  const localAppDataPath = useUtoolsDbStorage(`settingUserStore/localAppData/${utools.getNativeId()}`,
    window.path.join(utools.getPath('userData'), 'screenshot'));

  const doubleClickCloseCopy = useUtoolsDbStorage('settingUserStore/doubleClickCloseCopy', true);

  const imageToolsDisabledList = useUtoolsDbStorage<string[]>(`settingUserStore/imageToolsDisabledList`, []);
  return {
    doubleClickCloseCopy,
    generalSetting,
    localAppDataPath,
    scScreenshotWay,
    scScreenshotCustomExecute,
    mouseWheelZoom,
    dragMode,
    imageToolsDisabledList
  }
});
