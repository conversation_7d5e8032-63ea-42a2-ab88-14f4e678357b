import { defineStore } from 'pinia'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { nanoid } from 'nanoid'
import { cloneDeep } from 'es-toolkit'

export interface ISceneConfig {
  /**
   * 名称
   */
  title: string;
  /**
   * 描述
   */
  desc: string;
  /**
   * 场景 code
   */
  sceneCode: string;
  /**
   * 插件 code
   */
  plugInCode: string;
  /**
   * 保存模式
   */
  saveMode: 'auto' | 'manual';
  /**
   * 系统
   */
  system?: boolean;
  /**
   * 启用
   */
  enable: boolean;
  /**
   * 禁用工具列表
   */
  disableCodeTools: string[];

  disableAutoSaveTools: string[];
  /**
   * 文件盒子功能是否启动
   */
  fileBoxEnable: boolean;
  /**
   * 文件收集标签
   */
  tags: number[];
  /**
   * 延迟截图
   */
  delayScreenshot: number;
  /**
   * 是否暂停音频
   */
  suspendMedia?: boolean;
}

const systemSceneConfig: ISceneConfig = {
  title: '默认场景',
  desc: '系统默认的场景',
  sceneCode: 'default',
  plugInCode: 'sc',
  saveMode: 'auto',
  disableCodeTools: [],
  disableAutoSaveTools: [],
  system: true,
  enable: true,
  fileBoxEnable: false,
  tags: [],
  delayScreenshot: 0,
  suspendMedia: false,
}

const defaultSceneConfig: ISceneConfig = {
  title: '用户场景',
  desc: '这里可以输入场景描述',
  sceneCode: '',
  plugInCode: '',
  saveMode: 'auto',
  enable: false,
  disableCodeTools: [],
  disableAutoSaveTools: [],
  fileBoxEnable: false,
  tags: [],
  delayScreenshot: 0,
}

export const useSceneStore = defineStore('sceneStore', () => {
  const sceneConfigList = useUtoolsDbStorage<ISceneConfig[], ISceneConfig>('sceneConfigList', [
    { ...systemSceneConfig }
  ], {
    templateValue: { ...systemSceneConfig },
    initData: (data => {
      data[0].disableAutoSaveTools.push('watermark');
    })
  });

  function getSceneConfig(sceneCode: string) {
    console.log('getSceneConfig', sceneCode)
    return sceneConfigList.value.find(item => item.sceneCode === sceneCode);
  }

  function addScene({ sceneTemplateCode, title, desc, sceneCode }: { sceneTemplateCode?: string, title?: string, desc?: string, sceneCode?: string }) {
    sceneCode = sceneCode || nanoid(32);
    if (sceneTemplateCode) {
      // copy 模版场景工具配置
      const sceneTemplatePrefix = `sceneToolConfig/${sceneTemplateCode}`;
      const allDocs = window.utools.db.allDocs(sceneTemplatePrefix);

      const newScenePrefix = `sceneToolConfig/${sceneCode}`;
      allDocs.forEach((doc) => {
        const newDocId = doc._id.toString().replace(sceneTemplatePrefix, newScenePrefix);
        if (doc._attachments) {
          // 附件
          window.utools.db.postAttachment(newDocId,
            utools.db.getAttachment(doc._id),
            utools.db.getAttachmentType(doc._id));
        } else {
          // 数据
          window.utools.dbStorage.setItem(newDocId, doc.value);
        }
      });
    }
    const oldIndex = sceneConfigList.value.findIndex(item => item.sceneCode === sceneCode);
    const oldData =  oldIndex !== -1 ? sceneConfigList.value[oldIndex] : {};
    const config: ISceneConfig = cloneDeep(sceneTemplateCode ? getSceneConfig(sceneTemplateCode) : defaultSceneConfig);
    sceneConfigList.value.splice(oldIndex === -1 ? 1 : oldIndex, oldIndex === -1 ? 0 : 1, {
      ...config,
      sceneCode,
      plugInCode: '',
      enable: false,
      system: false,
      ...oldData,
      title: title || '用户场景' + sceneConfigList.value.length.toString().padStart(2, '0'),
      desc: desc || '',
    } as ISceneConfig);
    return sceneCode;
  }

  /**
   * 删除场景
   * @param sceneCode
   */
  function deleteScene(sceneCode: string) {
    const index = sceneConfigList.value.findIndex(item => item.sceneCode === sceneCode);
    if (index >= 0) {
      sceneConfigList.value.splice(index, 1);
      window.utools.db.allDocs(`sceneToolConfig/${sceneCode}`).map(({_id}) => {
        window.utools.dbStorage.removeItem(_id);
      });
    }
  }
  return {
    sceneConfigList,
    getSceneConfig,
    addScene,
    deleteScene
  }
});
