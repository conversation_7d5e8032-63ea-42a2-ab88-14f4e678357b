import { defineStore } from 'pinia'
import { computed, type Ref, ref, toRaw } from 'vue'
import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { type App } from 'leafer-ui'
import { BaseUseTool } from '@/components/toolbox/BaseUseTool'

export type ToolBoxBarLocation = 'default' | 'right' | 'cover';
let currentTool: BaseUseTool | undefined =  undefined;
let leaferApp: App | null = null;
const allToolBox: ToolboxItem[] = [];
export const useCurrentToolBoxStore = defineStore('currentToolBoxStore', () => {

  function registerApp(app: App) {
    leaferApp = app;
  }
  function registerAllToolBox(app: App, items: ToolboxItem[]) {
    leaferApp = app;
    if (allToolBox.length > 0) {
      return;
    }
    allToolBox.length = 0;
    allToolBox.push(...items);
    for (const toolBox of allToolBox) {
      toolBox.tool?.install(app, toolBox.info);
    }
  }

  function getLeaferApp() {
    return leaferApp;
  }

  const currentToolBoxInfo =  ref<ToolboxInfoItemConfig>();
  const getCurrentToolBoxCode = computed(() =>  {
    if (!currentToolBoxInfo.value) {
      return '';
    }
    return currentToolBoxInfo.value.code;
  });

  function setCurrentToolBox(toolBox: ToolboxItem) {
    const beforeToolCode = currentToolBoxInfo.value ? currentToolBoxInfo.value.code : null;
    if (currentTool) {
      currentTool.destroy();
    }
    currentToolBoxInfo.value = toolBox.info;
    currentTool = toRaw(toolBox.tool);
    currentTool!.use();
    if (utools.isMacOS()) {
      window.winHelper.refreshWindow();
    }
    if (beforeToolCode && toolBox.info.instant) {
      const toolBoxItem = allToolBox.find(item => item.info.code === beforeToolCode)
      toolBoxItem && setCurrentToolBox(toolBoxItem as ToolboxItem)
    }
  }

  function setCurrentToolBoxByCode(code: string, savePrefixTool = false) {
    if (currentToolBoxInfo.value
        && currentToolBoxInfo.value.code === code) {
      return;
    }
    console.log('setCurrentToolBoxByCode', code)
    const toolBoxItem = allToolBox
      .find(item => item.info.code === code) as ToolboxItem;
    if (savePrefixTool && currentToolBoxInfo.value) {
      prefixToolBoxCode.value = currentToolBoxInfo.value.code;
    }
    if (toolBoxItem) {
      setCurrentToolBox(toolBoxItem)
    }
  }

  function isCurrentToolByCode(code: string): boolean {
    if (!currentToolBoxInfo.value) {
      return false;
    }
    return currentToolBoxInfo.value.code === code;
  }

  const prefixToolBoxCode = ref<string>();
  function rollbackToolBox() {
    if (prefixToolBoxCode.value) {
      setCurrentToolBoxByCode(prefixToolBoxCode.value);
    }
  }

  function getToolInfoByCode(code: string) {
    const item = allToolBox.find(item => item.info.code === code)
    if (item) {
      return toRaw(item.tool)!;
    }
  }

  function getToolByCode<T extends BaseUseTool>(code: string): T | null {
    const item = allToolBox.find(item => item.info.code === code)
    if (item) {
      return toRaw(item.tool) as T;
    }
    return null;
  }

  const toolBarVisible = ref(true);

  function disableToolBar() {
    toolBarVisible.value = false;
  }

  function enableToolBar() {
    toolBarVisible.value = true;
  }

  const toolBarLocation: Ref<ToolBoxBarLocation> = ref('default');
  return {
    getLeaferApp,
    currentToolBox: currentToolBoxInfo,
    setCurrentToolBox,
    getCurrentToolBoxCode,
    setCurrentToolBoxByCode,
    rollbackToolBox,
    isCurrentToolByCode,

    registerApp,
    registerAllToolBox,
    getToolByCode,

    toolBarVisible,
    disableToolBar,
    enableToolBar,

    toolBarLocation,
  }
})
