import { defineStore } from 'pinia'
import ImageUploadConfigStore from '@/stores/ToolConfigStore/store/ImageUploadConfigStore'
import RectangleToolConfigStore from '@/stores/ToolConfigStore/store/RectangleToolConfigStore'
import RoundToolConfigStore from '@/stores/ToolConfigStore/store/RoundToolConfigStore'
import ArrowToolConfigStore from '@/stores/ToolConfigStore/store/ArrowToolConfigStore'
import PencilToolConfigStore from '@/stores/ToolConfigStore/store/PencilToolConfigStore'
import MosaicToolConfigStore from '@/stores/ToolConfigStore/store/MosaicToolConfigStore'
import LayoutToolConfigStore from '@/stores/ToolConfigStore/store/LayoutToolConfigStore'
import TextToolConfigStore from '@/stores/ToolConfigStore/store/TextToolConfigStore'
import WatermarkToolConfigStore from '@/stores/ToolConfigStore/store/WatermarkToolConfigStore'
import SerialNumberToolStore from '@/stores/ToolConfigStore/store/SerialNumberToolStore'
import OcrToolConfigStore from '@/stores/ToolConfigStore/store/OcrToolConfigStore'
import QrCodeToolConfigStore from '@/stores/ToolConfigStore/store/QrCodeToolConfigStore'
import SaveToolSceneSetting from '@/stores/ToolConfigStore/store/SaveToolConfigStore'
import TranslateToolConfigStore from '@/stores/ToolConfigStore/store/TranslateToolConfigStore'

export const useToolConfigStore = defineStore('toolConfigStore', () => {
  return {
    ...SaveToolSceneSetting(),
    ...ImageUploadConfigStore(),
    ...RectangleToolConfigStore(),
    ...RoundToolConfigStore(),
    ...ArrowToolConfigStore(),
    ...PencilToolConfigStore(),
    ...MosaicToolConfigStore(),
    ...LayoutToolConfigStore(),
    ...TextToolConfigStore(),
    ...WatermarkToolConfigStore(),
    ...SerialNumberToolStore(),
    ...OcrToolConfigStore(),
    ...QrCodeToolConfigStore(),
    ...TranslateToolConfigStore(),
  }
})
