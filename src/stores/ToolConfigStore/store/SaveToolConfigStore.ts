import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { cloneDeep } from 'es-toolkit'
import { ref } from 'vue'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'

export interface ISaveConfig {
  /**
   * 文件格式
   */
  filenameFormat: string;
  /**
   * 默认文件路径
   */
  filePath: string;
  /**
   * 文件后缀
   */
  fileSuffix: 'png' | 'jpg' | 'auto';

  saveAfterClose?: boolean;
}

const defaultSaveConfig: ISaveConfig = {
  filePath: utools.getPath('downloads'),
  filenameFormat: ' {YY}_{M}_{D}_{h}_{m}_{s}',
  saveAfterClose: false,
  fileSuffix: 'auto',
}

export default () => {
  const saveConfig = ref<ISaveConfig>({
    ...defaultSaveConfig
  });


  function getSceneSaveConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/saveConfig`, defaultSaveConfig);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneSaveConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('save')) {
        value = cloneDeep(value);
      }
      saveConfig.value = value;
    }
  });

  return {
    saveConfig,
    getSceneSaveConfig,
  }
}
