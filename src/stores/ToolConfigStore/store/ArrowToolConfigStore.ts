import { computed, ref } from 'vue'
import type { IStrokeConfig, IStrokeWidth } from '../index'
import { defaultStroke, defaultStrokeSelectOptions, defaultStrokeWidth } from '../ToolConfigConstants'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'


export interface IArrowConfig extends IStrokeConfig, IStrokeWidth {
  shape: 'arrow' | 'doubleArrow' | 'line';
  subShape: 'fat' | 'general';
}


const defaultArrowConfig: IArrowConfig = {
  ...defaultStroke,
  ...defaultStrokeWidth,
  strokeSelectOptions: [...defaultStrokeSelectOptions],
  shape: 'arrow',
  subShape: 'fat'
}

export default () => {
  const arrowConfig = ref<IArrowConfig>({
    ...defaultArrowConfig
  });

  const getArrowConfig = computed(() => arrowConfig.value);

  function getSceneArrowConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/arrowConfig`, defaultArrowConfig);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneArrowConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('arrow')) {
        value = cloneDeep(value);
      }
      arrowConfig.value = value;
    }
  });
  return {
    arrowConfig,
    getArrowConfig,
    getSceneArrowConfig,
  }
}

