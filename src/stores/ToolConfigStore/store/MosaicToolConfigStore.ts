import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface IMosaicConfig {
  mosaicSize: number;
}

const defaultMosaicConfig: IMosaicConfig = {
  mosaicSize: 18,
}

export default () => {
  const mosaicConfig = ref<IMosaicConfig>({
    ...defaultMosaicConfig
  });
  const getMosaicConfig = computed(() => mosaicConfig.value);

  function getSceneMosaicConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/mosaicConfig`, defaultMosaicConfig);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneMosaicConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('mosaic')) {
        value = cloneDeep(value);
      }
      mosaicConfig.value = value;
    }
  });
  return {
    mosaicConfig,
    getMosaicConfig,
    getSceneMosaicConfig
  }
}
