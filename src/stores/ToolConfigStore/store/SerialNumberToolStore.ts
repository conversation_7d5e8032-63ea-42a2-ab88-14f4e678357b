import { computed, ref } from 'vue'
import type { ITextConfig } from '@/stores/ToolConfigStore/store/TextToolConfigStore'
import {
  defaultTextBackgroundSelectOptions,
  defaultTextSelectOptions
} from '@/stores/ToolConfigStore/store/TextToolConfigStore'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface ISerialNumber {
  textConfig: ITextConfig
}


const defaultSerialNumberConfig: ISerialNumber = {
  textConfig: {
    width: 0,
    height: 0,
    fontSize: 14,
    fillId: '1',
    fillSelectOptions: [...defaultTextSelectOptions],
    fillBackgroundId: '1',
    fillBackgroundSelectOptions: [...defaultTextBackgroundSelectOptions]
  }
}

export default () => {
  const serialNumberConfig = ref<ISerialNumber>({
    ...defaultSerialNumberConfig
  });
  const getSerialNumberConfig = computed(() => serialNumberConfig.value);

  function getSceneSerialNumberConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/serialNumber`, defaultSerialNumberConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneSerialNumberConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('serialNumber')) {
        value = cloneDeep(value);
      }
      serialNumberConfig.value = value;
    }
  });
  return {
    serialNumberConfig,
    getSerialNumberConfig,
    getSceneSerialNumberConfig,
  }
}
