import type { FillColor } from '@/stores/ToolConfigStore'
import { ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'

export type WatermarkConfigType = ISingleWatermarkConfig & ITiledWatermarkConfig;

export interface ISingleWatermarkConfig {
  position: string;
  opacity: number;
  direct?: boolean;
  padding?: number;
  contentScript?: {
    scriptId: string;
    args?: {
      params: string;
      value: string;
    }[];
  };
}

export interface ITiledWatermarkConfig {
  text: string;
  rotation: number;
  opacity: number;
  fillId: string;
  fillSelectOptions: FillColor[];
  gap: number;
}


const defaultWatermarkFillSelectOptions: FillColor[] = [
  {
    id: '1',
    type: 'linear',
    stops: [{ offset: 0, color: '#8e9eab' }, { offset: 1, color: '#eef2f3' }]
  },
  {
    id: '2',
    type: 'linear',
    stops: [{ offset: 0, color: '#a1ffce' }, { offset: 1, color: '#faffd1' }]
  },
  {
    id: '3',
    type: 'solid',
    color: '#b2b6b6'
  },
  {
    id: '4',
    type: 'solid',
    color: '#686a67'
  },
  {
    id: '5',
    type: 'solid',
    color: '#698e6a'
  },
  {
    id: '6',
    type: 'solid',
    color: '#dfe0d9',
    edit: true
  }
];

const defaultWatermarkConfig: ISingleWatermarkConfig  =  {
  opacity: 100,
  position: '',
}
const defaultTiledWatermarkConfig: ITiledWatermarkConfig  =  {
  opacity: 100,
  text: '',
  rotation: 45,
  fillSelectOptions: [...defaultWatermarkFillSelectOptions],
  fillId: '1',
  gap: 10,
}
export default () => {

  const singleWatermarkConfig = ref({
    ...defaultWatermarkConfig
  });

  const tiledWatermarkConfig = ref<ITiledWatermarkConfig>({
    ...defaultTiledWatermarkConfig,
  })

  function getSceneWatermarkConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/watermark/tiledWatermarkConfig`, defaultTiledWatermarkConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    console.log('watermark..' + window.sceneCode)
    if (window.sceneCode) {
      let tiledWatermarkConfigValue = getSceneWatermarkConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('watermark')) {
        tiledWatermarkConfigValue = cloneDeep(tiledWatermarkConfigValue);
      }
      tiledWatermarkConfig.value = tiledWatermarkConfigValue;
      const currentToolBoxStore = useCurrentToolBoxStore();
      const watermarkTool = currentToolBoxStore.getToolByCode('watermark');
      watermarkTool.reloadConfig().then(() => {});
    }
  });
  return {
    singleWatermarkConfig,
    tiledWatermarkConfig,
    getSceneWatermarkConfig,
  }
}
