import type { IStrokeConfig, IStrokeWidth } from '../index'
import { defaultStroke, defaultStrokeWidth } from '@/stores/ToolConfigStore/ToolConfigConstants'
import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface IRectangleConfig extends IStrokeConfig, IStrokeWidth {
  shape: 'normal' | 'round';
  fill: boolean;
}

const defaultRectangleConfig: IRectangleConfig = {
  ...defaultStroke,
  ...defaultStrokeWidth,
  fill: false,
  shape: 'normal',
};

export default () => {
  const rectangleConfig = ref<IRectangleConfig>({
    ...defaultRectangleConfig
  });

  function getSceneRectangleConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/rectangleConfig`, defaultRectangleConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneRectangleConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('rectangle')) {
        value = cloneDeep(value);
      }
      rectangleConfig.value = value;
    }
  });

  const getRectangleConfig = computed(() => rectangleConfig.value);
  return {
    rectangleConfig,
    getRectangleConfig,
    getSceneRectangleConfig
  }
}
