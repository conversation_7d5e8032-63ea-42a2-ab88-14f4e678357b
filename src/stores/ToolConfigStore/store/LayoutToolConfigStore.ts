import { defaultBackground } from '../ToolConfigConstants'
import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { cloneDeep } from 'es-toolkit'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'

export interface ILayoutConfig {
  mode: 'auto' | 'manual',
  padding: {
    width: number;
    height: number;
  },
  align: string,
  canvas: {
    width: number,
    height: number,
  },
}

export interface IBeautifyConfig {
  fillId: string;
  /**
   * 背景圆角
   */
  backgroundCorner: number;
  /**
   * 截图圆角
   */
  imageCorner: number;
  shadowMode: 'NONE' | 'SHADOW';
  shadowList: {
    x: number;
    y: number;
    blur: number;
    color: string;
    spread: number;
    visible: boolean;
  }[];
}

const defaultBeautifyConfig: IBeautifyConfig = {
  fillId: '',
  backgroundCorner: 0,
  imageCorner: 0,
  shadowMode: 'NONE',
  shadowList: [
    {
      x: 0,
      y: 5,
      blur: 15,
      color: '#0000004C',
      spread: 5,
      visible: true,
    }
  ]
}

const defaultLayoutConfig: ILayoutConfig = {
  mode: 'auto',
  align: 'center',
  padding: { width: 0, height: 0},
  canvas: {
    width: 0,
    height: 0,
  },
}

export default () => {
  const layoutConfig = ref<ILayoutConfig>({
    ...defaultLayoutConfig
  });
  const beautifyConfig = ref<IBeautifyConfig>({
    ...defaultBeautifyConfig,
  });

  const getLayoutConfig = computed(() => layoutConfig.value);

  function getSceneLayoutConfig(sceneCode: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${sceneCode}/layoutConfig`, defaultLayoutConfig);
  }
  function getSceneBeautifyConfig(sceneCode: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${sceneCode}/beautifyConfig`, defaultBeautifyConfig);
  }
  function getLayoutBackgroundColorList() {
    return cloneDeep(defaultBackground);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let layoutConfigValue = getSceneLayoutConfig(window.sceneCode).value;
      let beautifyConfigValue = getSceneBeautifyConfig(window.sceneCode).value;

      if (e.disableSave || e.disableAutoSaveTools.includes('layout')) {
        layoutConfigValue = cloneDeep(layoutConfigValue);
        beautifyConfigValue = cloneDeep(beautifyConfigValue);
      }

      layoutConfig.value = layoutConfigValue;
      beautifyConfig.value = beautifyConfigValue;
    }

    const currentToolBoxStore = useCurrentToolBoxStore();
    const layoutTool = currentToolBoxStore.getToolByCode('layout');
    layoutTool.reloadConfig().then(() => {});
  });

  return {
    layoutConfig,
    getLayoutConfig,
    beautifyConfig,
    getSceneLayoutConfig,
    getSceneBeautifyConfig,
    getLayoutBackgroundColorList,
  }
}
