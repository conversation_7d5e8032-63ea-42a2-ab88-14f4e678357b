import { cloneDeep } from 'es-toolkit'
import type { BaseTranslateConfig } from '@/components/toolbox/TranslateTool/translate'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { nanoid } from 'nanoid'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ref } from 'vue'

export default () => {

  const translateActiveId = ref<string>('')
  function saveTranslateConfig(config: BaseTranslateConfig) {
    if (!config.id) {
      config.id = nanoid();
      config.createAt = Date.now();
    }
    utools.dbStorage.setItem(`translateToolConfig/config/${config.id}`, cloneDeep(config));
  }

  function getTranslateConfigList() {
    return utools.db.allDocs<{value: BaseTranslateConfig}>('translateToolConfig/config/')
      .map(({ value }) => value) as BaseTranslateConfig[];
  }
  function removeTranslateConfig(id: string) {
    return utools.dbStorage.removeItem(`translateToolConfig/config/${id}`);
  }

  function getTranslateConfig(id: string) {
    return utools.dbStorage.getItem(`translateToolConfig/config/${id}`) as BaseTranslateConfig;
  }

  function getSceneActiveTranslateId(sceneCode: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${sceneCode}/translateActive`, undefined);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneActiveTranslateId(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('translate')) {
        value = cloneDeep(value);
      }
      translateActiveId.value = value;
    }
  });
  return {
    translateActiveId,
    saveTranslateConfig,
    getTranslateConfigList,
    getTranslateConfig,
    removeTranslateConfig,
    getSceneActiveTranslateId
  }
}
