import type { IStrokeConfig, IStrokeWidth } from '@/stores/ToolConfigStore'
import { defaultStroke, defaultStrokeWidth } from '../ToolConfigConstants'
import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'


export interface IPencilConfig extends IStrokeConfig, IStrokeWidth {

}

const defaultPencilConfig: IPencilConfig = {
  ...defaultStroke,
  ...defaultStrokeWidth,
}

export default () => {
  const pencilConfig = ref<IPencilConfig>({
    ...defaultPencilConfig
  });
  const getPencilConfig = computed(() => pencilConfig.value);
  function getScenePencilConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/pencilConfig`, defaultPencilConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getScenePencilConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('pencil')) {
        value = cloneDeep(value);
      }
      pencilConfig.value = value;
    }
  });
  return {
    pencilConfig,
    getPencilConfig,
    getScenePencilConfig,
  }
}
