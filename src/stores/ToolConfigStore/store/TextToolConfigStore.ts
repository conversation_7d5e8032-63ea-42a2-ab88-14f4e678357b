import type { FillColor } from '../index'
import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface ITextConfig {
  width: number;
  height: number;
  fontSize: number;
  fillId: string;
  fillSelectOptions: FillColor[];
  fillBackgroundId: string;
  fillBackgroundSelectOptions: FillColor[];
}

export const defaultTextSelectOptions: FillColor[] = [
  {
    id: '1',
    type: 'solid',
    color: '#000'
  },
  {
    id: '2',
    type: 'solid',
    color: '#c12c1f'
  },
  {
    id: '3',
    type: 'solid',
    color: '#fff799'
  },
  {
    id: '4',
    type: 'solid',
    color: '#e4b8d5'
  },
  {
    id: '5',
    type: 'solid',
    color: '#27ae60'
  },
  {
    id: '6',
    type: 'solid',
    color: 'rgba(230, 103, 103)',
    edit: true
  }
];
export const defaultTextBackgroundSelectOptions: FillColor[] = [
  {
    id: '1',
    type: 'solid',
    color: '#FFE16C'
  },
  {
    id: '2',
    type: 'solid',
    color: '#badc58'
  },
  {
    id: '3',
    type: 'solid',
    color: '#dcdde1'
  },
  {
    id: '4',
    type: 'solid',
    color: '#34ace0'
  },
  {
    id: '5',
    type: 'solid',
    color: '#8c7ae6'
  },
]

const defaultTextConfig: ITextConfig = {
  width: 0,
  height: 0,
  fontSize: 14,
  fillId: '1',
  fillSelectOptions: [...defaultTextSelectOptions],
  fillBackgroundId: '1',
  fillBackgroundSelectOptions: [...defaultTextBackgroundSelectOptions]
}

export default () => {
  const textConfig = ref<ITextConfig>({
    ...defaultTextConfig
  });
  const getTextConfig = computed(() => textConfig.value);

  function getSceneTextConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/textConfig`, defaultTextConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneTextConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('text')) {
        value = cloneDeep(value);
      }
      textConfig.value = value;
    }
  });
  return {
    textConfig,
    getTextConfig,
    getSceneTextConfig,
  }
}


