import type { IStrokeConfig, IStrokeWidth } from '../index'
import { computed, ref } from 'vue'
import { defaultStroke, defaultStrokeWidth } from '@/stores/ToolConfigStore/ToolConfigConstants'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface IRoundConfig extends IStrokeConfig, IStrokeWidth {
  fill: boolean;
}

const defaultRoundConfig: IRoundConfig = {
  ...defaultStroke,
  ...defaultStrokeWidth,
  fill: false,
};

export default () => {
  const roundConfig = ref<IRoundConfig>({
    ...defaultRoundConfig,
  })
  const getRoundConfig = computed(() => roundConfig.value);

  function getSceneRoundConfig(code: string) {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/roundConfig`, defaultRoundConfig);
  }
  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneRoundConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('round')) {
        value = cloneDeep(value);
      }
      roundConfig.value = value;
    }
  });

  return {
    roundConfig,
    getRoundConfig,
    getSceneRoundConfig,
  }
}
