import { computed, type Ref, toRaw } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import type { IOcrPlatFromConfigItem } from '@/leaferApp/ocr/OcrContentBase'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'
import { useEventListener } from '@vueuse/core'

export interface IOcrPlugInConfig {
  globalType: 'plugIn' | 'cloud' | 'local';
  plugInKeyWord: string;
  cloudConfigKey?: number;
  autoOcr?: boolean;
  plugInAutoClose?: boolean;
}

const defaultOcrConfig: IOcrPlugInConfig = {
  globalType: 'plugIn',
  plugInKeyWord: 'OCR 文字识别|OCR文字识别',
  autoOcr: false,
  plugInAutoClose: true,
};

const defaultOcrCloudConfig: IOcrPlatFromConfigItem[] = [];


type IOcrConfig = IOcrPlugInConfig;
export default () => {
  const ocrConfig: Ref<IOcrConfig> = useUtoolsDbStorage('ocrToolConfigStore/OcrConfig', {
    ...defaultOcrConfig
  });

  // fix 兼容版本
  if (ocrConfig.value.plugInKeyWord === 'OCR 文字识别|OCR 识别') {
    ocrConfig.value.plugInKeyWord = 'OCR 文字识别|OCR文字识别';
  }

  const ocrCloudConfig = useUtoolsDbStorage('ocrToolConfigStore/ocrCloudConfig', [
    ...defaultOcrCloudConfig
  ]);

  function ocrCloudAppendConfig(config: IOcrPlatFromConfigItem) {
    if (config.key !== undefined) {
      const index = ocrCloudConfig.value.findIndex(item => item.key === config.key);
      ocrCloudConfig.value.splice(index, 1, {...ocrCloudConfig.value[index], ...toRaw(config)})
    } else {
      ocrCloudConfig.value.push({...toRaw(config), key: Date.now(), type: 'image'});
    }
  }

  function setOcrConfig(config: Partial<IOcrConfig>) {
    ocrConfig.value = { ...ocrConfig.value, ...config };
    console.log( ocrConfig.value )
  }

  function ocrCloudRemoveConfig(key: number) {
    const index = ocrCloudConfig.value.findIndex(item => item.key === key);
    if (index === -1) {
      return;
    }
    ocrCloudConfig.value.splice(index, 1);
  }



  function getOcrConfig(code: string): Ref<IOcrPlugInConfig>  {
    return useUtoolsDbStorage(`sceneToolConfig/${code}/ocrConfig`,
      utools.dbStorage.getItem('ocrToolConfigStore/OcrConfig') || defaultOcrConfig);
  }


  useEventListener(window, ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getOcrConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('pencil')) {
        value = cloneDeep(value);
      }
      ocrConfig.value = value;
    }
  });

  const getOcrCloudConfig = computed(() => ocrCloudConfig.value);
  return {
    ocrConfig,
    setOcrConfig,
    getOcrConfig,
    ocrCloudConfig,
    getOcrCloudConfig,
    ocrCloudAppendConfig,
    ocrCloudRemoveConfig
  }
};
