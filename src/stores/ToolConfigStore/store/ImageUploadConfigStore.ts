import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { ConfigReloadEvent } from '@/events/ConfigReloadEvent'
import { cloneDeep } from 'es-toolkit'

export interface ImageUploadConfig {
  imageUploadType: 'auto' | 'plugIn'
  uploadWay: string;
}
const defaultImageUploadConfig: ImageUploadConfig = {
  imageUploadType: 'auto',
  uploadWay: '',
}
export default () => {
  const imageUploadConfig = ref<ImageUploadConfig>({
    ...defaultImageUploadConfig
  });
  const getImageUploadConfig = computed(() => imageUploadConfig.value);


  function getSceneImageUploadConfig(code: string) {
    return useUtoolsDbStorage(`imageUploadConfig/${code}/textConfig`, defaultImageUploadConfig);
  }

  // @ts-ignore
  window.addEventListener(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE, (e: ConfigReloadEvent) => {
    if (window.sceneCode) {
      let value = getSceneImageUploadConfig(window.sceneCode).value;
      if (e.disableSave || e.disableAutoSaveTools.includes('text')) {
        value = cloneDeep(value);
      }
      imageUploadConfig.value = value;
    }
  });
  return {
    imageUploadConfig,
    getImageUploadConfig,
    getSceneImageUploadConfig
  }
}
