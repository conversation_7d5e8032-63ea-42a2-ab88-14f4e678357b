import type { FillColor, IStrokeConfig, IStrokeWidth } from './index'

export const defaultShadowStrokeSelectOptions: FillColor[] = [
  {
    id: '1',
    type: 'solid',
    color: '#d12920'
  },
  {
    id: '2',
    type: 'solid',
    color: '#3498db'
  },
  {
    id: '3',
    type: 'solid',
    color: '#2ed573'
  },
  {
    id: '4',
    type: 'linear',
    stops: [{ offset: 0, color: '#FF4B4B' }, { offset: 1, color: '#FEB027' }]
  },
  {
    id: '5',
    type: 'linear',
    stops: [{ offset: 0, color: '#00d2ff' }, { offset: 1, color: '#3a7bd5' }]
  },
  {
    id: '6',
    type: 'solid',
    color: '#e66767',
    edit: true
  }
]
export const defaultStrokeSelectOptions: FillColor[] = [
  {
    id: '1',
    type: 'solid',
    color: '#d12920'
  },
  {
    id: '2',
    type: 'solid',
    color: '#3498db'
  },
  {
    id: '3',
    type: 'solid',
    color: '#2ed573'
  },
  {
    id: '4',
    type: 'solid',
    color: '#fa8231'
  },
  {
    id: '5',
    type: 'solid',
    color: '#8c7ae6'
  },
  {
    id: '6',
    type: 'solid',
    color: '#e66767',
    edit: true
  }
]
export const defaultBackground: FillColor[] = [
  {
    id: '1',
    type: 'linear',
    stops: [{ offset: 0, color: '#f54ea2' }, { offset: 1, color: '#ff7676' }]
  },
  {
    id: '0',
    type: 'solid',
    color: '#FFFFFF',
  },
  {
    id: '2',
    type: 'linear',
    stops: [{ offset: 0, color: '#84fab0' }, { offset: 1, color: '#8fd3f4' }]
  },
  {
    id: '3',
    type: 'linear',
    stops: [{ offset: 0, color: '#ff9a9e' }, { offset: 1, color: '#fad0c4' }]
  },
  {
    id: '5',
    type: 'linear',
    stops: [{ offset: 0, color: '#f3c4d8' }, { offset: 1, color: '#a198f5' }]
  },
  {
    id: '6',
    type: 'linear',
    stops: [{ offset: 0, color: '#b6e9ed' }, { offset: 1, color: '#c2cbec' }]
  }
]
export const defaultStroke: IStrokeConfig = {
  currentStroke: '1',
  strokeSelectOptions: [...defaultShadowStrokeSelectOptions]
}

export const defaultStrokeWidth: IStrokeWidth = {
  strokeWidth: 2,
}
