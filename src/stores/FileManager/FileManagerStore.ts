import { defineStore } from 'pinia'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'
import { computed } from 'vue'
import jsUtil from '@/utils/jsUtil'

export interface IFileManagerConfig {
  sync: 'no-sync'
}
const defaultFileManagerConfig: IFileManagerConfig = {
  sync: 'no-sync',
}

export interface FileTagLibrary {
  id: number;
  label: string;
  color: string;
}

export const tagColorSelectList: string[] = [
  '#779649', '#168cff', '#165dff', '#0fc6c2',
  '#ecd452', '#dd7694', '#ee7959', '#45465e',
  '#ea5514', '#4a4b9d', '#108b96', '#7c4449',
  '#ce93bf', '#b0436f', '#006d87'
];
export function getRandomTagColor() {
  return tagColorSelectList[jsUtil.getRandomInt(0, tagColorSelectList.length - 1)];
}

export const useFileManagerStore = defineStore('fileManagerStore', () => {
  const localFileManagerEnable = useUtoolsDbStorage<boolean>(`localFileManagerEnable/${window.utools.getNativeId()}`,
    false);

  const fileManagerConfig = useUtoolsDbStorage<IFileManagerConfig>('fileManagerConfig',
    { ...defaultFileManagerConfig });

  const fileTagLibraryList = useUtoolsDbStorage<FileTagLibrary[]>('fileTagLibrary', [
  ]);

  const tagIdToFileTagLibrary = computed(() => {
    return fileTagLibraryList.value.reduce((accumulator, tag) => {
      // 使用 tag.id 作为键，tag 作为值
      accumulator[tag.id] = tag;
      return accumulator;
    }, {} as Record<string, FileTagLibrary>);
  });


  /**
   * 文件标签
   * @param fileTag
   */
  function addOrUpdateFileTag(fileTag: Partial<FileTagLibrary>) {
    const existData: FileTagLibrary = fileTag.id ? {...tagIdToFileTagLibrary.value[fileTag.id]} : {
      id: Date.now(),
      label: '',
      color: getRandomTagColor(),
    } as any

    const addFileTag: FileTagLibrary = {
      ...existData,
      ...fileTag,
    }

    const index = tagIdToFileTagLibrary.value[fileTag.id]
      ? fileTagLibraryList.value.findIndex(item => item.id === addFileTag.id)
      : -1;

    if (index === -1) {
      fileTagLibraryList.value.push(addFileTag);
    } else {
      fileTagLibraryList.value.splice(index, 1, addFileTag);
    }

    return addFileTag;
  }

  return {
    localFileManagerEnable,
    fileManagerConfig,
    fileTagLibraryList,
    tagIdToFileTagLibrary,
    addOrUpdateFileTag
  }
});
