import { defineStore } from 'pinia'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks'


export interface IExpressionUserItem {
  imageUrl: string;
  createTime: number;
}


export const useExpressionListStore = defineStore('expressionListStore', () => {
  const expressionUserList = useUtoolsDbStorage<IExpressionUserItem[]>('expressionListStore/userExpressionList', []);

  function addExpressionUserList(expressionUserItem: IExpressionUserItem) {
    expressionUserList.value.unshift(expressionUserItem);
  }

  function removeExpressionUserList(imageUrl: string) {
    const idx = expressionUserList.value.findIndex(item => item.imageUrl === imageUrl);
    if (idx !== -1) {
      expressionUserList.value.splice(idx, 1);
      utools.dbStorage.removeItem(imageUrl.split("//")[1]);
    }
  }

  function getPageExpressionUserList(page: number, pageSize = 20) {
    const start = (page - 1) * pageSize;
    return expressionUserList.value.slice(start, start + pageSize);
  }

  return {
    addExpressionUserList,
    removeExpressionUserList,
    getPageExpressionUserList
  }
});
