/**
 * 场景配置加载事件
 */
export class ConfigReloadEvent extends CustomEvent<any> {
  static CONFIG_RELOAD_EVENT_CODE = "config.reload";
  public readonly disableAutoSaveTools: string[] = [];
  public readonly disableSave: boolean = false;
  constructor(disableAutoSaveTools: string[], disableSave: boolean) {
    super(ConfigReloadEvent.CONFIG_RELOAD_EVENT_CODE)
    this.disableAutoSaveTools = disableAutoSaveTools;
    this.disableSave = disableSave;
  }

  public dispatchEvent() {
    dispatchEvent(this);
  }
}
