import type { App } from 'vue'
import fileUtils from '@/utils/FileUtils'
import DataUtils from '@/utils/DataUtils'


function handleUtoolsAttachment(value: string): string {
  if (value.startsWith('utools:attachment://')) {
    value = value.replace('utools:attachment://', '');
  }
  const saveImageDir = window.path.join(DataUtils.getDataAttachmentPath(), window.path.dirname(value))
  fileUtils.createDirIfAbsent(saveImageDir);
  const attachment = utools.db.getAttachment(value);
  const saveImagePath = window.path.join(saveImageDir, window.path.basename(value));
  if (window.fs.existsSync(saveImagePath)) {
    return saveImagePath;
  }
  window.fs.writeFileSync(saveImagePath, attachment, 'binary');
  return saveImagePath;
}



export default {
  install (app: App) {
    app.directive('imageUrl', {
      mounted (el, binding) {
        console.log('utoolsImage', el, binding);
        el.id = binding.value;
        el.src = handleUtoolsAttachment(binding.value.toString());
        //   const { stop } = useIntersectionObserver(el, ([{ isIntersecting }], observerElement) => {
        //     if (isIntersecting) { // 可见区域
        //       el.onerror = () => { // 当图片加载失败 设置为默认图片
        //         el.src = imgDefault
        //       }
        //       stop() // 可见区域后 下次不在执行监听
        //       el.src = binding.value // 设置传过来的地址去请求
        //     }
        //   }, { threshold: 0 }) // 当可视区域宽高为0就触发
        //   console.log(el, binding.value)
        // }
      }
    })
  }
}
