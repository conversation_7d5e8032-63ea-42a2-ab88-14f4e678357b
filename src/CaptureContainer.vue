<script setup lang="ts">

import { nextTick, ref } from 'vue'
import { App, Rect } from 'leafer-ui'
import '@leafer-in/editor'
import '@leafer-in/text-editor'
import './leaferApp/custom/CustomEditTool/CustomEditTool'
// 导入组件样式
import './components/toolbox/toolbox.less'
import LongCaptureToolBar from '@/components/LongCaptureToolBar/LongCaptureToolBar.vue'
import LeaferInstallCapacity from '@/leaferApp/LeaferInstallCapacity'
import LeaferHistoryList from '@/leaferApp/LeaferHistoryList'
import LeaferConstant from '@/leaferApp/LeaferConstant'
import ToolboxBar from '@/components/ToolboxBar/ToolboxBar.vue'
import type { ToolBoxBarInstance } from '@/components/ToolboxBar/types'

const contentRef = ref<HTMLDivElement>();

Rect.setEditOuter((rect: Rect) => {
  console.log('setEditOuter', rect.id)
  return rect.id === LeaferConstant.ElementChooseAreaRect ? 'CustomEditTool' : 'EditTool'
});
const toolBoxBarRef = ref<ToolBoxBarInstance>();
nextTick(() => {
  const app = new App({
    view: contentRef.value,
    tree: {},
    ground: { type: 'draw' },
    editor: {},
    move: {
      disabled: true
    },
    zoom: {
      disabled: true
    },
  });
  toolBoxBarRef.value.loadToolList(app);
  // 历史列表
  window.leaferHistoryList = new LeaferHistoryList(app);
  LeaferInstallCapacity.loadMosaic(app);
  LeaferInstallCapacity.loadEditorSelectImposeAction(app);
  LeaferInstallCapacity.loadDrawGroup(app);
  LeaferInstallCapacity.loadMiddleGroup(app);
  LeaferInstallCapacity.registerApi(app);
  LeaferInstallCapacity.installDeleteElement(app);
});

</script>

<template>
  <div id="qrcode">
  </div>
  <div id="app" ref="contentRef">
  </div>
  <div id="ocr">
  </div>
  <div id="coordinate">
    <div id="x_div"></div>
    <div id="y_div"></div>
  </div>
  <ToolboxBar ref="toolBoxBarRef">
    <LongCaptureToolBar />
  </ToolboxBar>
</template>

<style scoped lang="less">
#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
#toolboxContainerWrapper {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
#x_div, #y_div {
  position:absolute;
  top: 0;
  left: 0;
  background-color: #bdc3c7;
  width: 100%;
  height: 1px;
  pointer-events: none;
}
#y_div {
  height:100%;
  width:2px;
}
</style>
