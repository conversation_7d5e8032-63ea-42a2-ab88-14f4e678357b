import { createApp } from 'vue'
import { createPinia } from 'pinia'
import KeyboardManager from './utils/KeyboardManager'
import SuspendContainer from './SuspendContainer.vue'
import WinHelper from '@/core/utils/WinHelper'
(() => {
  import('virtual:uno.css')
})()

// 初始化 API 容器
window.api = {};
window.tempData = {};
window.globalParams = {};
window.keyboardManager = new KeyboardManager();

if (window.utools) {
  document.body.setAttribute('os',
    utools.isMacOS() ? 'mac' : utools.isWindows() ? 'windows' : 'linux');

  window.winHelper = new WinHelper({
    initCallback(initData) {
    },
  });
}

const app = createApp(SuspendContainer)

app.use(createPinia())
app.mount('#app');

