{"name": "screenshot-pro", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "dev:prod": "vite --host --mode prod", "dev:dev": "vite --host --mode dev", "build:prod": "vite build --mode prod", "build:release": "vite build --mode release --config vite.release.config.ts", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@leafer-in/arrow": "^1.9.1", "@leafer-in/editor": "^1.9.1", "@leafer-in/export": "^1.9.1", "@leafer-in/find": "^1.9.1", "@leafer-in/state": "^1.9.1", "@leafer-in/text-editor": "^1.9.1", "@leafer-in/viewport": "^1.9.1", "@vueuse/core": "^13.5.0", "@xiaou66/interconnect-client": "^0.0.16", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "es-toolkit": "^1.39.8", "hotkeys-js": "^3.13.15", "leafer-ui": "^1.9.1", "leafer-x-connector": "^0.1.3", "nanoid": "^5.1.5", "pinia": "^3.0.3", "tdesign-vue-next": "^1.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-waterfall-plugin-next": "^2.6.7", "vue3-colorpicker": "^2.3.0", "wechat-qrcode-ocr-wasm": "^0.0.6"}, "devDependencies": {"@xiaou66/u-web-ui": "^0.0.38", "@iconify/utils": "^2.3.0", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@unocss/preset-icons": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "code-inspector-plugin": "^0.20.17", "cross-zip": "^4.0.1", "electron": "22.3.27", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "less": "^4.4.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "prettier": "3.5.3", "tdesign-vue-next": "^1.15.1", "typescript": "~5.8.0", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "utools-api-types": "^7.2.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}